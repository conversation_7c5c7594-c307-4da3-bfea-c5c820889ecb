
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import BusinessProfileSetup from '../../components/BusinessProfileSetup';

// Mock mapbox-gl
vi.mock('mapbox-gl', () => ({
  default: {
    accessToken: '',
    Map: vi.fn(() => ({
      on: vi.fn(),
      remove: vi.fn(),
      addControl: vi.fn(),
      setCenter: vi.fn(),
      setZoom: vi.fn(),
    })),
  },
}));

describe('BusinessProfileSetup', () => {
  const mockOnComplete = vi.fn();
  
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders basic information step correctly', () => {
    render(<BusinessProfileSetup onComplete={mockOnComplete} />);
    
    expect(screen.getByText('Basic Information')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Your Business Name')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('************')).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    render(<BusinessProfileSetup onComplete={mockOnComplete} />);
    
    const businessNameInput = screen.getByPlaceholderText('Your Business Name');
    
    // Test empty field validation
    fireEvent.blur(businessNameInput);
    await waitFor(() => {
      expect(screen.getByText('Business name is required')).toBeInTheDocument();
    });
  });

  it('formats phone number correctly', async () => {
    const user = userEvent.setup();
    render(<BusinessProfileSetup onComplete={mockOnComplete} />);
    
    const phoneInput = screen.getByPlaceholderText('************');
    
    await user.type(phoneInput, '1234567890');
    expect(phoneInput).toHaveValue('************');
  });

  it('progresses through steps correctly', async () => {
    const user = userEvent.setup();
    render(<BusinessProfileSetup onComplete={mockOnComplete} />);
    
    // Fill required fields
    await user.type(screen.getByPlaceholderText('Your Business Name'), 'Test Business');
    await user.type(screen.getByPlaceholderText('************'), '1234567890');
    await user.type(screen.getByPlaceholderText('Start typing your address...'), '123 Main St, City, State');
    
    // Select business category
    const categoryButton = screen.getByText('Food & Beverage');
    await user.click(categoryButton);
    
    // Move to next step
    const nextButton = screen.getByText('Next Step');
    await user.click(nextButton);
    
    expect(screen.getByText('Locations & Hours')).toBeInTheDocument();
  });

  it('handles location addition correctly', async () => {
    const user = userEvent.setup();
    render(<BusinessProfileSetup onComplete={mockOnComplete} />);
    
    // Navigate to step 2
    // ... (fill required fields and navigate)
    
    // Add location
    await user.type(screen.getByPlaceholderText('Main Store, Downtown Branch, etc.'), 'Downtown Branch');
    await user.type(screen.getByPlaceholderText('Street address'), '456 Oak St');
    await user.type(screen.getByPlaceholderText('City'), 'Test City');
    await user.type(screen.getByPlaceholderText('State'), 'TS');
    
    const addLocationButton = screen.getByText('Add Location');
    await user.click(addLocationButton);
    
    expect(screen.getByText('Downtown Branch')).toBeInTheDocument();
  });

  it('prevents JSX syntax errors with proper structure', () => {
    // This test ensures the component renders without syntax errors
    const { container } = render(<BusinessProfileSetup onComplete={mockOnComplete} />);
    expect(container.firstChild).toBeInTheDocument();
  });
});
