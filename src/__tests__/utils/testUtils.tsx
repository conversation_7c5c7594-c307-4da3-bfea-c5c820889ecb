
import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from '../../contexts/AuthContext';
import { BusinessProfileProvider } from '../../contexts/BusinessProfileContext';

// Custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <BrowserRouter>
      <AuthProvider>
        <BusinessProfileProvider>
          {children}
        </BusinessProfileProvider>
      </AuthProvider>
    </BrowserRouter>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };

// Mock implementations for external dependencies
export const mockMapboxGL = {
  Map: jest.fn(() => ({
    on: jest.fn(),
    remove: jest.fn(),
    addControl: jest.fn(),
    setCenter: jest.fn(),
    setZoom: jest.fn(),
  })),
  NavigationControl: jest.fn(),
  Marker: jest.fn(() => ({
    setLngLat: jest.fn().mockReturnThis(),
    addTo: jest.fn().mockReturnThis(),
    remove: jest.fn(),
  })),
};

// Test data factories
export const createMockBusinessProfile = (overrides = {}) => ({
  businessName: 'Test Business',
  primaryAddress: '123 Main St, Test City, TS 12345',
  website: 'https://testbusiness.com',
  description: 'A test business',
  phoneNumber: '************',
  logoUrl: '',
  contactEmail: '<EMAIL>',
  locations: [],
  businessCategory: 'food-beverage',
  businessTags: ['restaurant', 'delivery'],
  primaryHours: {
    monday: { open: '09:00', close: '17:00', closed: false },
    tuesday: { open: '09:00', close: '17:00', closed: false },
    wednesday: { open: '09:00', close: '17:00', closed: false },
    thursday: { open: '09:00', close: '17:00', closed: false },
    friday: { open: '09:00', close: '17:00', closed: false },
    saturday: { open: '10:00', close: '16:00', closed: false },
    sunday: { open: '10:00', close: '16:00', closed: true }
  },
  ...overrides,
});
