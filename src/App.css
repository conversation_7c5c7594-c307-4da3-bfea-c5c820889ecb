@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Fix for framer-motion scroll offset warnings */
.relative-container {
  position: relative;
}

/* Ensure proper positioning for animated elements */
.motion-container {
  position: relative;
  contain: layout;
}

/* Custom slider styling */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: linear-gradient(45deg, #4f46e5, #7c3aed);
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(79, 70, 229, 0.4);
}

.slider::-moz-range-thumb {
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: linear-gradient(45deg, #4f46e5, #7c3aed);
  cursor: pointer;
  border: none;
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
}

.slider::-webkit-slider-track {
  background: linear-gradient(45deg, #e5e7eb, #d1d5db);
  border-radius: 8px;
}

.slider::-moz-range-track {
  background: linear-gradient(45deg, #e5e7eb, #d1d5db);
  border-radius: 8px;
  border: none;
}

@layer base {
  * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    margin: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-feature-settings: 'cv08', 'kern', 'ss01';
    line-height: 1.6;
    scroll-behavior: smooth;
    color: #374151;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
    text-rendering: optimizeLegibility;
    color: #111827;
  }

  /* Stripe-like text selection */
  ::selection {
    background-color: rgba(59, 130, 246, 0.15);
    color: inherit;
  }

  /* Clean focus styles */
  :focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 4px;
  }

  /* Smooth transitions for interactive elements */
  button, a {
    transition: all 0.2s ease;
  }
}

@layer components {
  .glass-morphism {
    backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(255, 255, 255, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }

  .glass-morphism-dark {
    backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(17, 25, 40, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }

  .premium-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  /* Widget-specific styles */
  .widget-hover-glow {
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .widget-hover-glow:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
  }

  /* Glassmorphism enhanced */
  .glass-card {
    backdrop-filter: blur(20px) saturate(190%);
    -webkit-backdrop-filter: blur(20px) saturate(190%);
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-card-dark {
    backdrop-filter: blur(20px) saturate(190%);
    -webkit-backdrop-filter: blur(20px) saturate(190%);
    background-color: rgba(17, 25, 40, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  /* Smooth scrolling enhancements */
  @media (prefers-reduced-motion: no-preference) {
    * {
      scroll-behavior: smooth;
    }
  }

  /* Custom scrollbar for widget areas */
  .widget-scroll::-webkit-scrollbar {
    width: 4px;
  }

  .widget-scroll::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 2px;
  }

  .widget-scroll::-webkit-scrollbar-thumb {
    background: rgba(99, 102, 241, 0.3);
    border-radius: 2px;
  }

  .widget-scroll::-webkit-scrollbar-thumb:hover {
    background: rgba(99, 102, 241, 0.5);
  }

  .premium-shadow {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .premium-shadow-lg {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .text-balance {
    text-wrap: balance;
  }
}

/* Premium shadows */
.premium-shadow {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.premium-shadow-lg {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Glass morphism */
.glass-morphism {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Better button interactions */
.btn-primary {
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s;
}

.btn-primary:hover::before {
  left: 100%;
}

/* Fix overflow issues */
body {
  overflow-x: hidden;
}

section {
  position: relative;
  overflow: hidden;
}

/* Smooth animations */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

/* Enhanced focus states */
button:focus-visible,
a:focus-visible,
input:focus-visible,
textarea:focus-visible {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
  border-radius: 8px;
}

/* Improved selection styling */
::selection {
  background: rgba(99, 102, 241, 0.2);
  color: inherit;
}

/* Smooth transitions for all interactive elements */
button,
a,
input,
textarea {
  transition: all 0.2s cubic-bezier(0.21, 0.47, 0.32, 0.98);
}