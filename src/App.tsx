import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { BusinessProfileProvider } from './contexts/BusinessProfileContext';
import Layout from './components/Layout';
import Home from './pages/Home';
import Business from './pages/Business';
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import SignUp from './pages/SignUp';
import Contact from './pages/Contact';
import Customers from './pages/Customers';
import ResetPassword from './pages/ResetPassword';
import VerifyEmail from './pages/VerifyEmail';
import NotFound from './pages/NotFound';
import ErrorBoundary from './components/ErrorBoundary';
import { setupGlobalErrorHandling } from './utils/errorTracking';
import './App.css';

// Enhanced error logging with context
const logError = (error: any, context: string) => {
  console.error(`🚨 [App Error - ${context}]:`, {
    message: error?.message || 'Unknown error',
    stack: error?.stack,
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent
  });
};

function App() {
  // Enhanced error handling and performance monitoring
  React.useEffect(() => {
    setupGlobalErrorHandling();
    console.log('🚀 Application initialized');
    console.log('🔍 App component mounted successfully');
    console.log('📍 Current location:', window.location.pathname);

    const handleError = (event: ErrorEvent) => {
      logError(event.error, 'Global Error Handler');
      // Prevent the error from breaking the entire app
      event.preventDefault();
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      logError(event.reason, 'Unhandled Promise Rejection');
      // Prevent unhandled rejection from crashing the app
      event.preventDefault();
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        console.log('📱 App became visible - checking for updates');
      }
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return (
    <ErrorBoundary>
      <AuthProvider>
        <BusinessProfileProvider>
          <Router>
            <Layout>
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/business" element={<Business />} />
                <Route path="/customers" element={<Customers />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/contact" element={<Contact />} />
                <Route path="/login" element={<Login />} />
                <Route path="/signup" element={<SignUp />} />
                <Route path="/reset-password" element={<ResetPassword />} />
                <Route path="/verify-email" element={<VerifyEmail />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </Layout>
          </Router>
        </BusinessProfileProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;