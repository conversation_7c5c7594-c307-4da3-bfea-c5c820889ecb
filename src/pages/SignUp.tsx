import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  Users, 
  Building, 
  ArrowRight, 
  Check,
  Sparkles,
  Target,
  TrendingUp,
  DollarSign,
  Gift,
  Share2,
  BarChart3,
  Zap,
  Heart,
  Star,
  Globe,
  Shield
} from 'lucide-react';
import SocialAuth from '../components/SocialAuth';

type AccountType = 'customer' | 'business' | null;

const SignUp: React.FC = () => {
  const [selectedType, setSelectedType] = useState<AccountType>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const handleAuthSuccess = (provider: string, userData: any) => {
    // Update user data with selected account type
    const updatedUserData = {
      ...userData,
      accountType: selectedType || 'customer',
      user_type: selectedType || 'customer'
    };

    console.log(`Sign up successful with ${provider}:`, updatedUserData);

    // The SocialAuth component will handle the actual authentication through auth context
  };

  const selectAccountType = (type: AccountType) => {
    if (type === selectedType) return;

    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedType(type);
      setIsTransitioning(false);
    }, 300);
  };

  const customerFeatures = [
    { icon: DollarSign, title: "Instant Payouts", desc: "Get paid immediately when friends buy" },
    { icon: Gift, title: "Exclusive Deals", desc: "Access member-only discounts and offers" },
    { icon: Share2, title: "Easy Sharing", desc: "One-click sharing across all platforms" },
    { icon: TrendingUp, title: "Track Earnings", desc: "Real-time dashboard of your referrals" }
  ];

  const businessFeatures = [
    { icon: Target, title: "Smart Campaigns", desc: "AI-powered referral campaign optimization" },
    { icon: BarChart3, title: "Analytics", desc: "Deep insights into customer behavior" },
    { icon: Users, title: "Network Growth", desc: "Watch your customer base expand organically" },
    { icon: Zap, title: "Quick Setup", desc: "Launch campaigns in under 5 minutes" }
  ];

  const customerBenefits = [
    "Earn $5-50 per successful referral",
    "No purchase required to start earning",
    "Instant notifications on new earnings",
    "Cash out anytime, no minimums"
  ];

  const businessBenefits = [
    "65% lower customer acquisition cost",
    "3x higher customer lifetime value",
    "Automated reward distribution",
    "Advanced fraud protection"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Dynamic Floating Elements based on selection */}
        <AnimatePresence>
          {selectedType === 'customer' && (
            <motion.div
              className="absolute inset-0"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {[Gift, Heart, Star, DollarSign].map((Icon, index) => (
                <motion.div
                  key={index}
                  className="absolute text-pink-300 opacity-20"
                  style={{ 
                    left: `${20 + index * 20}%`, 
                    top: `${10 + (index % 2) * 70}%` 
                  }}
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ 
                    scale: 1, 
                    rotate: 0,
                    y: [-10, 10, -10]
                  }}
                  transition={{ 
                    duration: 2,
                    y: { duration: 4, repeat: Infinity, ease: "easeInOut" }
                  }}
                >
                  <Icon className="w-8 h-8" />
                </motion.div>
              ))}
            </motion.div>
          )}

          {selectedType === 'business' && (
            <motion.div
              className="absolute inset-0"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {[Target, BarChart3, Globe, Building].map((Icon, index) => (
                <motion.div
                  key={index}
                  className="absolute text-blue-300 opacity-20"
                  style={{ 
                    left: `${15 + index * 25}%`, 
                    top: `${15 + (index % 2) * 60}%` 
                  }}
                  initial={{ scale: 0, rotate: 180 }}
                  animate={{ 
                    scale: 1, 
                    rotate: 0,
                    y: [-8, 8, -8]
                  }}
                  transition={{ 
                    duration: 2,
                    y: { duration: 3, repeat: Infinity, ease: "easeInOut" }
                  }}
                >
                  <Icon className="w-10 h-10" />
                </motion.div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Base gradient orbs */}
        <motion.div
          className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-r from-purple-300 to-pink-300 rounded-full blur-3xl opacity-20"
          animate={{ 
            scale: [1, 1.2, 1],
            x: [0, 30, 0]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />
      </div>

      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Header */}
        <motion.div
          className="text-center pt-12 pb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl mb-4"
            whileHover={{ scale: 1.05, rotate: 5 }}
          >
            <span className="text-2xl font-bold text-white">R</span>
          </motion.div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-3">
            Join Referit
          </h1>
          <p className="text-xl text-gray-600">
            Choose your path to start earning through referrals
          </p>
        </motion.div>

        <div className="flex-1 flex items-center justify-center px-4">
          <div className="w-full max-w-6xl">
            {!selectedType ? (
              /* Account Type Selection */
              <motion.div
                className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.8 }}
              >
                {/* Customer Option */}
                <motion.div
                  className="relative group cursor-pointer"
                  whileHover={{ scale: 1.02, y: -5 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => selectAccountType('customer')}
                >
                  <div className="bg-white/80 backdrop-blur-xl rounded-3xl border border-white/50 p-8 shadow-2xl relative overflow-hidden h-full">
                    {/* Animated background gradient */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-br from-pink-50 to-purple-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      initial={false}
                    />

                    <div className="relative z-10">
                      {/* Icon */}
                      <motion.div
                        className="w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6"
                        whileHover={{ rotate: [0, -5, 5, 0] }}
                        transition={{ duration: 0.5 }}
                      >
                        <Users className="w-8 h-8 text-white" />
                      </motion.div>

                      <h3 className="text-2xl font-bold text-gray-900 mb-3">
                        I'm a Customer
                      </h3>
                      <p className="text-gray-600 mb-6 text-lg">
                        Discover amazing deals and earn money by sharing them with friends
                      </p>

                      {/* Key Benefits */}
                      <div className="space-y-3 mb-6">
                        {["Earn $5-50 per referral", "No purchase required", "Instant payouts"].map((benefit, index) => (
                          <motion.div
                            key={index}
                            className="flex items-center"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.4 + index * 0.1 }}
                          >
                            <Check className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                            <span className="text-gray-700">{benefit}</span>
                          </motion.div>
                        ))}
                      </div>

                      <motion.div
                        className="inline-flex items-center text-pink-600 font-semibold group-hover:text-pink-700"
                        animate={{ x: [0, 5, 0] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        Get Started <ArrowRight className="w-4 h-4 ml-2" />
                      </motion.div>
                    </div>
                  </div>
                </motion.div>

                {/* Business Option */}
                <motion.div
                  className="relative group cursor-pointer"
                  whileHover={{ scale: 1.02, y: -5 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => selectAccountType('business')}
                >
                  <div className="bg-white/80 backdrop-blur-xl rounded-3xl border border-white/50 p-8 shadow-2xl relative overflow-hidden h-full">
                    {/* Animated background gradient */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      initial={false}
                    />

                    <div className="relative z-10">
                      {/* Icon */}
                      <motion.div
                        className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6"
                        whileHover={{ rotate: [0, 5, -5, 0] }}
                        transition={{ duration: 0.5 }}
                      >
                        <Building className="w-8 h-8 text-white" />
                      </motion.div>

                      <h3 className="text-2xl font-bold text-gray-900 mb-3">
                        I'm a Business
                      </h3>
                      <p className="text-gray-600 mb-6 text-lg">
                        Grow your customer base through powerful referral campaigns
                      </p>

                      {/* Key Benefits */}
                      <div className="space-y-3 mb-6">
                        {["65% lower acquisition cost", "3x customer lifetime value", "AI-powered optimization"].map((benefit, index) => (
                          <motion.div
                            key={index}
                            className="flex items-center"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.4 + index * 0.1 }}
                          >
                            <Check className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                            <span className="text-gray-700">{benefit}</span>
                          </motion.div>
                        ))}
                      </div>

                      <motion.div
                        className="inline-flex items-center text-blue-600 font-semibold group-hover:text-blue-700"
                        animate={{ x: [0, 5, 0] }}
                        transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
                      >
                        Get Started <ArrowRight className="w-4 h-4 ml-2" />
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            ) : (
              /* Registration Form */
              <AnimatePresence mode="wait">
                <motion.div
                  key={selectedType}
                  className="max-w-4xl mx-auto"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  transition={{ duration: 0.5, ease: "easeOut" }}
                >
                  <div className="grid lg:grid-cols-2 gap-12 items-center">
                    {/* Left Side - Dynamic Content */}
                    <motion.div
                      className="space-y-8"
                      initial={{ opacity: 0, x: -50 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2, duration: 0.6 }}
                    >
                      {/* Account Type Badge */}
                      <motion.div
                        className="inline-flex items-center space-x-2"
                        whileHover={{ scale: 1.05 }}
                      >
                        <button
                          onClick={() => setSelectedType(null)}
                          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                        >
                          <ArrowRight className="w-4 h-4 rotate-180" />
                        </button>
                        <div className={`px-4 py-2 rounded-full ${
                          selectedType === 'customer' 
                            ? 'bg-gradient-to-r from-pink-500 to-purple-600' 
                            : 'bg-gradient-to-r from-blue-500 to-indigo-600'
                        } text-white font-semibold flex items-center`}>
                          {selectedType === 'customer' ? <Users className="w-4 h-4 mr-2" /> : <Building className="w-4 h-4 mr-2" />}
                          {selectedType === 'customer' ? 'Customer Account' : 'Business Account'}
                        </div>
                      </motion.div>

                      {/* Dynamic Title */}
                      <div>
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                          {selectedType === 'customer' 
                            ? 'Start Earning Today' 
                            : 'Grow Your Business'
                          }
                        </h2>
                        <p className="text-xl text-gray-600">
                          {selectedType === 'customer'
                            ? 'Join thousands earning money by sharing deals they love'
                            : 'Join businesses growing 3x faster with referral marketing'
                          }
                        </p>
                      </div>

                      {/* Features Grid */}
                      <div className="grid grid-cols-2 gap-4">
                        {(selectedType === 'customer' ? customerFeatures : businessFeatures).map((feature, index) => {
                          const IconComponent = feature.icon;
                          return (
                            <motion.div
                              key={index}
                              className="p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40"
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.4 + index * 0.1 }}
                              whileHover={{ scale: 1.02, y: -2 }}
                            >
                              <IconComponent className={`w-6 h-6 mb-2 ${
                                selectedType === 'customer' ? 'text-pink-600' : 'text-blue-600'
                              }`} />
                              <h4 className="font-semibold text-gray-800 text-sm">{feature.title}</h4>
                              <p className="text-xs text-gray-600">{feature.desc}</p>
                            </motion.div>
                          );
                        })}
                      </div>

                      {/* Benefits List */}
                      <div className="space-y-3">
                        <h4 className="font-semibold text-gray-800">What you get:</h4>
                        {(selectedType === 'customer' ? customerBenefits : businessBenefits).map((benefit, index) => (
                          <motion.div
                            key={index}
                            className="flex items-center"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.6 + index * 0.1 }}
                          >
                            <Shield className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                            <span className="text-gray-700">{benefit}</span>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>

                    {/* Right Side - Form */}
                    <motion.div
                      initial={{ opacity: 0, x: 50 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3, duration: 0.6 }}
                    >
                      <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/50 p-8 relative overflow-hidden">
                        {/* Dynamic form background */}
                        <motion.div
                          className={`absolute inset-0 ${
                            selectedType === 'customer' 
                              ? 'bg-gradient-to-br from-pink-50/50 to-purple-50/50' 
                              : 'bg-gradient-to-br from-blue-50/50 to-indigo-50/50'
                          }`}
                          animate={{ 
                            background: selectedType === 'customer' 
                              ? [
                                  "linear-gradient(45deg, rgba(236, 72, 153, 0.02), rgba(168, 85, 247, 0.02))",
                                  "linear-gradient(45deg, rgba(168, 85, 247, 0.02), rgba(236, 72, 153, 0.02))"
                                ]
                              : [
                                  "linear-gradient(45deg, rgba(59, 130, 246, 0.02), rgba(99, 102, 241, 0.02))",
                                  "linear-gradient(45deg, rgba(99, 102, 241, 0.02), rgba(59, 130, 246, 0.02))"
                                ]
                          }}
                          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                        />

                        <div className="relative z-10">
                          <div className="text-center mb-6">
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">
                              {selectedType === 'customer' ? 'Start Earning' : 'Start Growing'}
                            </h3>
                            <p className="text-gray-600">
                              Create your account in seconds
                            </p>
                          </div>

                          <SocialAuth mode="signup" accountType={selectedType} onSuccess={handleAuthSuccess} />
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </motion.div>
              </AnimatePresence>
            )}
          </div>
        </div>

        {/* Footer */}
        <motion.div
          className="text-center py-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.6 }}
        >
          <p className="text-gray-600">
            Already have an account?{' '}
            <Link 
              to="/login" 
              className="font-semibold text-indigo-600 hover:text-indigo-500 transition-colors"
            >
              Sign in here
            </Link>
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default SignUp;