
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  Home, 
  Search, 
  ArrowLeft,
  Sparkles,
  Users,
  Gift,
  Target,
  RefreshCw,
  Zap,
  Heart
} from 'lucide-react';

const NotFound: React.FC = () => {
  const [isHovering, setIsHovering] = useState(false);
  const [particles, setParticles] = useState<Array<{ id: number; x: number; y: number }>>([]);

  useEffect(() => {
    // Generate random particles
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100
    }));
    setParticles(newParticles);
  }, []);

  const floatingIcons = [
    { Icon: Gift, color: 'text-purple-400', delay: 0 },
    { Icon: Users, color: 'text-blue-400', delay: 0.5 },
    { Icon: Target, color: 'text-green-400', delay: 1 },
    { Icon: Zap, color: 'text-yellow-400', delay: 1.5 },
    { Icon: Heart, color: 'text-pink-400', delay: 2 }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden flex items-center justify-center">
      {/* Animated Background */}
      <div className="absolute inset-0">
        {/* Floating Particles */}
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute w-2 h-2 bg-indigo-300 rounded-full opacity-30"
            style={{ left: `${particle.x}%`, top: `${particle.y}%` }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 0.6, 0.3],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Gradient Orbs */}
        <motion.div
          className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-r from-purple-300 to-pink-300 rounded-full blur-3xl opacity-20"
          animate={{ 
            scale: [1, 1.3, 1],
            x: [0, 50, 0],
            y: [0, -30, 0]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-blue-300 to-indigo-300 rounded-full blur-3xl opacity-15"
          animate={{ 
            scale: [1, 0.8, 1],
            x: [0, -40, 0],
            y: [0, 20, 0]
          }}
          transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-4 max-w-4xl mx-auto">
        {/* Floating Icons */}
        <div className="absolute inset-0 pointer-events-none">
          {floatingIcons.map(({ Icon, color, delay }, index) => (
            <motion.div
              key={index}
              className={`absolute ${color} opacity-20`}
              style={{
                left: `${20 + index * 15}%`,
                top: `${10 + (index % 2) * 80}%`
              }}
              initial={{ opacity: 0, scale: 0, rotate: -180 }}
              animate={{ 
                opacity: 0.2, 
                scale: 1, 
                rotate: 0,
                y: [-10, 10, -10],
              }}
              transition={{ 
                delay,
                duration: 2,
                y: {
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }
              }}
            >
              <Icon className="w-12 h-12" />
            </motion.div>
          ))}
        </div>

        {/* 404 Illustration */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, ease: "easeOut" }}
        >
          <motion.div
            className="relative inline-block"
            onHoverStart={() => setIsHovering(true)}
            onHoverEnd={() => setIsHovering(false)}
          >
            {/* Large 404 */}
            <motion.h1 
              className="text-9xl md:text-[12rem] font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 relative"
              animate={{ 
                backgroundPosition: isHovering ? ["0% 50%", "100% 50%"] : ["100% 50%", "0% 50%"]
              }}
              style={{
                backgroundSize: "200% 200%"
              }}
              transition={{ duration: 2, ease: "easeInOut" }}
            >
              404
            </motion.h1>

            {/* Cute Character in the '0' */}
            <motion.div
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
              animate={{ 
                y: isHovering ? [-5, 5, -5] : [0],
                rotate: isHovering ? [0, 5, -5, 0] : [0]
              }}
              transition={{ duration: 1, ease: "easeInOut" }}
            >
              <div className="w-16 h-16 bg-gradient-to-b from-amber-200 to-amber-300 rounded-full relative">
                {/* Eyes */}
                <motion.div 
                  className="flex justify-center items-center pt-4 space-x-2"
                  animate={{ 
                    scale: isHovering ? [1, 1.2, 1] : [1]
                  }}
                >
                  <div className="w-2 h-2 bg-gray-800 rounded-full"></div>
                  <div className="w-2 h-2 bg-gray-800 rounded-full"></div>
                </motion.div>
                {/* Confused Expression */}
                <motion.div 
                  className="w-3 h-1 bg-gray-800 rounded-full mx-auto mt-1"
                  animate={{ 
                    scaleX: isHovering ? [1, 0.5, 1] : [1]
                  }}
                />
              </div>
            </motion.div>

            {/* Sparkle Effects */}
            <AnimatePresence>
              {isHovering && (
                <motion.div
                  className="absolute inset-0 pointer-events-none"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  {[...Array(8)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute"
                      style={{
                        left: `${Math.random() * 100}%`,
                        top: `${Math.random() * 100}%`
                      }}
                      initial={{ scale: 0, rotate: 0 }}
                      animate={{ 
                        scale: [0, 1, 0],
                        rotate: 360,
                        y: -20
                      }}
                      transition={{ 
                        duration: 1,
                        delay: i * 0.1
                      }}
                    >
                      <Sparkles className="w-4 h-4 text-yellow-400" />
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </motion.div>

        {/* Error Message */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.8 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Oops! Page Not Found
          </h2>
          <p className="text-xl text-gray-600 mb-2">
            Looks like you've wandered off the referral path!
          </p>
          <p className="text-lg text-gray-500">
            Don't worry, even the best navigators get lost sometimes.
          </p>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.8 }}
        >
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link
              to="/"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group"
            >
              <motion.div
                className="absolute inset-0 bg-white/20"
                initial={{ x: "-100%" }}
                whileHover={{ x: "100%" }}
                transition={{ duration: 0.6 }}
              />
              <Home className="w-5 h-5 mr-2 relative z-10" />
              <span className="relative z-10">Back to Home</span>
            </Link>
          </motion.div>

          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <button
              onClick={() => window.history.back()}
              className="inline-flex items-center px-8 py-4 bg-white border-2 border-gray-200 text-gray-700 rounded-xl font-semibold text-lg hover:border-indigo-300 hover:text-indigo-600 transition-all duration-300"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Go Back
            </button>
          </motion.div>
        </motion.div>

        {/* Fun Suggestions */}
        <motion.div
          className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/50 p-6 shadow-xl"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.8 }}
        >
          <h3 className="text-xl font-semibold text-gray-800 mb-4">
            While you're here, why not explore:
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              { 
                title: "Start Referring", 
                desc: "Begin your earning journey", 
                icon: Target,
                link: "/customers"
              },
              { 
                title: "For Business", 
                desc: "Grow your customer base", 
                icon: Users,
                link: "/business"
              },
              { 
                title: "Search Deals", 
                desc: "Find amazing offers", 
                icon: Search,
                link: "/"
              }
            ].map((item, index) => {
              const IconComponent = item.icon;
              return (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.02, y: -2 }}
                  transition={{ duration: 0.2 }}
                >
                  <Link
                    to={item.link}
                    className="block p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200 hover:border-indigo-300 transition-all duration-300 group"
                  >
                    <IconComponent className="w-8 h-8 text-indigo-600 mb-2 group-hover:scale-110 transition-transform" />
                    <h4 className="font-semibold text-gray-800 mb-1">{item.title}</h4>
                    <p className="text-sm text-gray-600">{item.desc}</p>
                  </Link>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Refresh Button */}
        <motion.div
          className="mt-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.1, duration: 0.8 }}
        >
          <motion.button
            onClick={() => window.location.reload()}
            className="inline-flex items-center text-gray-500 hover:text-indigo-600 transition-colors font-medium"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="mr-2"
            >
              <RefreshCw className="w-4 h-4" />
            </motion.div>
            Try refreshing the page
          </motion.button>
        </motion.div>
      </div>
    </div>
  );
};

export default NotFound;
