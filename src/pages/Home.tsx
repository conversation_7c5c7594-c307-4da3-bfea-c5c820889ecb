import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useScroll, useTransform, useInView } from 'framer-motion';
import { Link } from 'react-router-dom';
import PerformanceDashboard from '../components/PerformanceDashboard';
import AdvancedFeatures from '../components/AdvancedFeatures';
import InteractiveDemo from '../components/InteractiveDemo';
import SocialProof from '../components/SocialProof';
import ValueCalculator from '../components/ValueCalculator';
import { 
  ArrowRight, 
  CheckCircle, 
  Star, 
  Users, 
  TrendingUp, 
  Gift, 
  Share2, 
  Coins,
  Sparkles,
  Play,
  Quote,
  ArrowUpRight,
  BarChart3,
  Target,
  Zap,
  Heart,
  Shield,
  Rocket,
  DollarSign,
  Layers,
  Globe
} from 'lucide-react';

const Home: React.FC = () => {
  const [activeTestimonial, setActiveTestimonial] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  // Parallax transforms
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const textY = useTransform(scrollYProgress, [0, 1], ["0%", "20%"]);

  // Testimonial carousel - removed auto-cycling to prevent refreshing

  const testimonials = [
    {
      name: "Sarah Chen",
      role: "Small Business Owner",
      company: "Bloom Café",
      avatar: "/images/woman_coffee_shop.jpg",
      quote: "Zero upfront cost, zero risk. We only pay when we get new customers. Referit delivered 150+ customers in 3 months.",
      rating: 5,
      metrics: { customers: "150+", revenue: "$12K", timeframe: "3 months" },
      verified: true,
      featured: true
    },
    {
      name: "Marcus Rodriguez",
      role: "Marketing Director",
      company: "TechFlow Solutions",
      avatar: "/images/man_glasses_brown_jacket.jpg",
      quote: "Finally, a marketing channel where we only pay for results. Our customers earn instant cash for sharing - it's brilliant.",
      rating: 5,
      metrics: { roi: "340%", referrals: "2.1K", conversion: "94%" },
      verified: true,
      featured: false
    },
    {
      name: "Emily Thompson",
      role: "Customer",
      company: "Referit Member",
      avatar: "/images/woman_natural_portrait.jpg",
      quote: "I earned $200 instantly just by sharing vouchers from businesses I love with friends. Pure win-win!",
      rating: 5,
      metrics: { earned: "$200", shares: "47", friends: "12" },
      verified: true,
      featured: false
    },
    {
      name: "David Kim",
      role: "Tech Startup CEO",
      company: "InnovateLab",
      avatar: "/images/man_business_suit.jpg",
      quote: "Referit helped us scale from 50 to 5000 users in just 6 months. The ROI is incredible - we only pay for actual conversions.",
      rating: 5,
      metrics: { growth: "10x", users: "5K", timeframe: "6 months" },
      verified: true,
      featured: false
    },
    {
      name: "Jennifer Walsh",
      role: "E-commerce Manager",
      company: "StyleHub",
      avatar: "/images/woman_blonde_smile.jpg",
      quote: "Our referral program generated $50K in sales last quarter. The analytics dashboard gives us insights we never had before.",
      rating: 5,
      metrics: { sales: "$50K", referrals: "1.8K", quarter: "Q3" },
      verified: true,
      featured: false
    },
    {
      name: "Alex Thompson",
      role: "Restaurant Owner",
      company: "Urban Bistro",
      avatar: "/images/man_beard_casual.jpg",
      quote: "Our customers love earning rewards for sharing. We've seen a 300% increase in new customer acquisition since starting with Referit.",
      rating: 5,
      metrics: { increase: "300%", customers: "800+", period: "6 months" },
      verified: true,
      featured: false
    }
  ];

  // Modern SaaS color palette
  const modernColors = {
    primary: "#6366f1", // Indigo
    secondary: "#10b981", // Emerald
    accent: "#f59e0b", // Amber
    purple: "#8b5cf6", // Violet
    blue: "#3b82f6", // Blue
    teal: "#14b8a6" // Teal
  };

  // Animation variants
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.8, ease: [0.21, 0.47, 0.32, 0.98] }
  };

  const staggerContainer = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  // Scroll-triggered animation hook
  const useScrollReveal = () => {
    const ref = useRef(null);
    const isInView = useInView(ref, { once: true, margin: "-10%" });
    return { ref, isInView };
  };

  // Enhanced Interactive Cursor Effects
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const updateMousePosition = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', updateMousePosition);
    return () => window.removeEventListener('mousemove', updateMousePosition);
  }, []);

  // Enhanced Modern SaaS Hero Illustration
  const ModernHeroIllustration = () => (
    <div className="relative w-full max-w-2xl mx-auto">
      {/* Background glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-indigo-200/30 via-purple-200/30 to-pink-200/30 rounded-full blur-3xl scale-150 -z-10"></div>

      {/* Main voucher mockup */}
      <motion.div 
        className="relative"
        initial={{ opacity: 0, scale: 0.8, rotateY: 20 }}
        animate={{ opacity: 1, scale: 1, rotateY: 0 }}
        transition={{ duration: 1.2, delay: 0.5 }}
      >
        {/* Main voucher container with enhanced styling */}
        <div className="relative bg-white rounded-[1rem] shadow-2xl border border-gray-200/50 transform rotate-3 hover:rotate-0 transition-all duration-700 backdrop-blur-sm">

          {/* Voucher header with enhanced design */}
          <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-700 px-6 py-5 text-white relative overflow-hidden rounded-t-[1rem]">
            {/* Header pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 left-0 w-20 h-20 bg-white rounded-full -translate-x-10 -translate-y-10"></div>
              <div className="absolute bottom-0 right-0 w-16 h-16 bg-white rounded-full translate-x-8 translate-y-8"></div>
            </div>

            <div className="flex items-center justify-between relative z-10">
              <div className="flex items-center">
                {/* Enhanced Starbucks-style logo */}
                <div className="w-12 h-12 bg-white rounded-full mr-4 flex items-center justify-center shadow-lg">
                  <motion.div 
                    className="w-8 h-8 bg-indigo-600 rounded-full relative flex items-center justify-center"
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                  >
                    {/* Star pattern */}
                    <div className="absolute w-6 h-6 flex items-center justify-center">
                      <Star className="w-4 h-4 text-white fill-current" />
                    </div>
                    {/* Orbiting dots */}
                    <motion.div 
                      className="absolute w-1 h-1 bg-white rounded-full"
                      style={{ top: '2px', left: '50%', transformOrigin: '0 14px' }}
                      animate={{ rotate: [0, -360] }}
                      transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                    />
                    <motion.div 
                      className="absolute w-1 h-1 bg-white rounded-full"
                      style={{ bottom: '2px', left: '50%', transformOrigin: '0 -14px' }}
                      animate={{ rotate: [0, -360] }}
                      transition={{ duration: 4, repeat: Infinity, ease: "linear", delay: 2 }}
                    />
                  </motion.div>
                </div>
                <div>
                  <motion.h3 
                    className="font-bold text-xl text-white"
                    whileHover={{ scale: 1.05 }}
                  >
                    Brickyard Coffee
                  </motion.h3>
                  <div className="text-indigo-100 text-sm flex items-center">
                    <Sparkles className="w-3 h-3 mr-1" />
                    Premium Coffee Experience
                  </div>
                </div>
              </div>
              <motion.div 
                className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-bold border border-white/30"
                animate={{ 
                  scale: [1, 1.1, 1],
                  boxShadow: ['0 0 0 0 rgba(255,255,255,0.3)', '0 0 0 10px rgba(255,255,255,0)', '0 0 0 0 rgba(255,255,255,0)']
                }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                FREE ⭐
              </motion.div>
            </div>
          </div>

          {/* Voucher content with enhanced design */}
          <div className="p-6 bg-gradient-to-br from-white to-gray-50/50 rounded-b-[1rem]">
            <motion.h2 
              className="text-2xl font-bold text-gray-900 mb-6 text-center"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2 }}
            >
              Free Pumpkin Spice Latte
            </motion.h2>

            {/* Enhanced Pumpkin Spice Latte Illustration with Full Image */}
            <div className="h-40 bg-gradient-to-br from-orange-100 via-orange-50 to-amber-50 rounded-3xl mb-6 relative overflow-hidden border border-orange-200/50">
              {/* $2 Reward badge positioned on coffee image */}
              <motion.div 
                className="absolute top-2 left-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg z-30 flex items-center space-x-1"
                animate={{ rotate: [0, 5, -5, 0] }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                <Gift className="w-3 h-3" />
                <span>$2 REWARD</span>
              </motion.div>

              {/* Background pattern - subtle */}
              <div className="absolute inset-0 opacity-10">
                {[...Array(8)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-1.5 h-1.5 bg-orange-300 rounded-full"
                    style={{
                      left: `${15 + (i % 4) * 25}%`,
                      top: `${15 + Math.floor(i / 4) * 35}%`,
                    }}
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.3, 0.6, 0.3]
                    }}
                    transition={{
                      duration: 3 + (i % 3),
                      repeat: Infinity,
                      delay: i * 0.4
                    }}
                  />
                ))}
              </div>

              {/* Full Coverage Latte Art Image */}
              <motion.div 
                className="absolute inset-0 z-10"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 1.5, duration: 0.8 }}
              >
                <motion.img
                  src="/images/coffee-latte.png"
                  alt="Beautiful pumpkin spice latte with artistic foam design showcasing the quality coffee offered through Referit vouchers"
                  className="w-full h-full object-cover rounded-3xl shadow-inner"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.3 }}
                />

                {/* Overlay gradient for better text readability */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent rounded-3xl"></div>
              </motion.div>

              {/* Enhanced steam animation positioned at top */}
              {[...Array(4)].map((_, i) => (
                <motion.div 
                  key={i}
                  className="absolute top-2 z-20"
                  style={{ left: `${25 + i * 15}%` }}
                  animate={{ 
                    y: [0, -15 - i * 2, 0],
                    opacity: [0.3, 0.7, 0.3],
                    x: [0, (i % 2 === 0 ? 3 : -3), 0]
                  }}
                  transition={{ 
                    duration: 3 + i * 0.5, 
                    repeat: Infinity, 
                    ease: "easeInOut",
                    delay: i * 0.4
                  }}
                >
                  <div className="w-1 h-8 bg-gradient-to-t from-white/60 to-transparent rounded-full backdrop-blur-sm"></div>
                </motion.div>
              ))}

              {/* Floating autumn spice particles */}
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 rounded-full z-30"
                  style={{
                    backgroundColor: i % 2 === 0 ? '#92400e' : '#d97706',
                    left: `${20 + (i % 3) * 30}%`,
                    top: `${20 + Math.floor(i / 3) * 30}%`,
                  }}
                  animate={{
                    y: [0, -8, 0],
                    x: [0, (i % 2 === 0 ? 4 : -4), 0],
                    opacity: [0.6, 1, 0.6],
                    scale: [0.8, 1.4, 0.8]
                  }}
                  transition={{
                    duration: 4 + (i % 3) * 0.5,
                    repeat: Infinity,
                    delay: i * 0.5
                  }}
                />
              ))}

              {/* Corner decorative elements */}
              <motion.div 
                className="absolute bottom-2 left-2 w-5 h-5 bg-gradient-to-br from-orange-400/80 to-orange-600/80 rounded-full flex items-center justify-center shadow-lg z-20 backdrop-blur-sm"
                animate={{ rotate: 360, scale: [1, 1.15, 1] }}
                transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
              >
                <div className="w-2 h-2 bg-orange-200 rounded-full"></div>
              </motion.div>

              <motion.div 
                className="absolute top-2 right-2 w-4 h-4 bg-gradient-to-br from-amber-400/80 to-yellow-500/80 rounded-full shadow-md z-20 backdrop-blur-sm"
                animate={{ rotate: -360, y: [0, -2, 0] }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              />


            </div>

            {/* Enhanced Voucher Stats with better styling */}
            <div className="grid grid-cols-3 gap-4">
              {[
                { value: '1.2k', label: 'Shared', icon: Share2, color: 'text-blue-600' },
                { value: '847', label: 'Redeemed', icon: CheckCircle, color: 'text-green-600' },
                { value: '71%', label: 'Success', icon: TrendingUp, color: 'text-purple-600' }
              ].map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div key={index} className="text-center bg-white/60 backdrop-blur-sm rounded-xl p-3 border border-gray-200/50">
                    <IconComponent className={`w-4 h-4 mx-auto mb-1 ${stat.color}`} />
                    <motion.div 
                      className={`text-2xl font-bold ${stat.color}`}
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 1.8 + index * 0.1, type: "spring" }}
                      whileHover={{ scale: 1.1 }}
                    >
                      {stat.value}
                    </motion.div>
                    <div className="text-xs text-gray-600 font-medium">{stat.label}</div>
                  </div>
                );
              })}
            </div>

            {/* Expiry and terms */}
            <motion.div 
              className="mt-4 text-center text-xs text-gray-500 border-t border-gray-200 pt-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2.2 }}
            >
              <div className="flex items-center justify-center space-x-4">
                <span>Valid until Dec 31, 2024</span>
                <span>•</span>
                <span>Terms apply</span>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Enhanced floating notification card */}
        <motion.div
          className="absolute -top-4 -right-12 bg-white rounded-2xl shadow-xl border border-gray-100 p-5 max-w-xs backdrop-blur-sm bg-white/95"
          initial={{ opacity: 0, x: 50, y: -20 }}
          animate={{ opacity: 1, x: 0, y: 0 }}
          transition={{ duration: 0.8, delay: 1.5 }}
          whileHover={{ scale: 1.05, y: -5 }}
        >
          <div className="flex items-center">
            <motion.div 
              className="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-3 shadow-lg"
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
            >
              <DollarSign className="w-6 h-6 text-white" />
            </motion.div>
            <div>
              <div className="font-bold text-gray-900 text-sm">Instant Reward! 🎉</div>
              <div className="text-green-600 font-bold text-lg">+$25.00</div>
              <div className="text-gray-500 text-xs">From Sarah's referral</div>
            </div>
          </div>
          {/* Celebration particles */}
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-yellow-400 rounded-full"
              style={{
                left: `${20 + i * 10}%`,
                top: `${10 + (i % 2) * 80}%`,
              }}
              animate={{
                y: [0, -10, 0],
                opacity: [0, 1, 0],
                scale: [0, 1, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.2 + 2
              }}
            />
          ))}
        </motion.div>

        {/* Enhanced floating user engagement card */}
        <motion.div
          className="absolute -bottom-8 -left-12 bg-white rounded-2xl shadow-xl border border-gray-100 p-4 flex items-center space-x-3 backdrop-blur-sm bg-white/95"
          initial={{ opacity: 0, x: -50, y: 20 }}
          animate={{ opacity: 1, x: 0, y: 0 }}
          transition={{ duration: 0.8, delay: 1.8 }}
          whileHover={{ scale: 1.05, y: -5 }}
        >
          <div className="flex -space-x-2">
            {[
              { color: 'from-blue-400 to-purple-500', delay: 0 },
              { color: 'from-pink-400 to-red-500', delay: 0.1 },
              { color: 'from-green-400 to-teal-500', delay: 0.2 },
              { color: 'from-yellow-400 to-orange-500', delay: 0.3 }
            ].map((user, i) => (
              <motion.div 
                key={i} 
                className={`w-9 h-9 bg-gradient-to-r ${user.color} rounded-full border-2 border-white shadow-md flex items-center justify-center`}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 2 + user.delay, type: "spring" }}
                whileHover={{ scale: 1.2, zIndex: 10 }}
              >
                <Users className="w-4 h-4 text-white" />
              </motion.div>
            ))}
          </div>
          <div>
            <motion.div 
              className="text-sm font-bold text-gray-900"
              animate={{ 
                color: ['#111827', '#059669', '#111827']
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              +47 new referrals
            </motion.div>
            <div className="text-xs text-gray-500">In the last hour</div>
          </div>
          <Heart className="w-4 h-4 text-red-400" />
        </motion.div>

        {/* Additional analytics card */}
        <motion.div
          className="absolute top-16 -left-16 bg-white rounded-xl shadow-lg border border-gray-100 p-3 backdrop-blur-sm bg-white/95"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 2.2 }}
        >
          <div className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5 text-indigo-600" />
            <div>
              <div className="text-xs font-bold text-gray-900">Conversion</div>
              <div className="text-lg font-bold text-indigo-600">94.2%</div>
            </div>
          </div>
        </motion.div>
      </motion.div>

      {/* Enhanced floating geometric shapes with better animations */}
      <motion.div
        className="absolute bottom-12 -left-8 w-20 h-20 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-3xl opacity-70 shadow-lg"
        animate={{ 
          y: [0, -25, 0],
          rotate: [0, 180, 360],
          scale: [1, 1.1, 1]
        }}
        transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
      />

      <motion.div
        className="absolute bottom-0 right-0 w-16 h-16 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-2xl opacity-70 shadow-lg"
        animate={{ 
          y: [0, 20, 0],
          x: [0, -15, 0],
          rotate: [0, -45, 0]
        }}
        transition={{ duration: 8, repeat: Infinity, ease: "easeInOut", delay: 2 }}
      />

      <motion.div
        className="absolute top-1/2 -right-16 w-12 h-12 bg-gradient-to-r from-amber-400 to-orange-500 rounded-xl opacity-70 shadow-lg"
        animate={{ 
          scale: [1, 1.4, 1],
          rotate: [0, -90, 0],
          y: [0, -10, 0]
        }}
        transition={{ duration: 6, repeat: Infinity, ease: "easeInOut", delay: 1 }}
      />

      {/* Floating sparkle effects */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-2 h-2"
          style={{
            left: `${10 + (i % 4) * 25}%`,
            top: `${15 + Math.floor(i / 4) * 70}%`,
          }}
          animate={{
            scale: [0, 1, 0],
            rotate: [0, 180, 360],
            opacity: [0, 1, 0]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            delay: i * 0.4,
            ease: "easeInOut"
          }}
        >
          <Sparkles className="w-2 h-2 text-yellow-400" />
        </motion.div>
      ))}
    </div>
  );

  // Simple non-looping counter component
  const StaticCounter = ({ value, suffix }: { value: number; suffix: string }) => {
    const [displayValue, setDisplayValue] = useState(0);
    const [hasAnimated, setHasAnimated] = useState(false);
    const ref = useRef<HTMLSpanElement>(null);

    useEffect(() => {
      if (hasAnimated) return;

      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting && !hasAnimated) {
            setHasAnimated(true);
            observer.disconnect();

            let startTime: number;
            const duration = 2000;

            const animate = (currentTime: number) => {
              if (!startTime) startTime = currentTime;
              const progress = Math.min((currentTime - startTime) / duration, 1);

              const easeOut = 1 - Math.pow(1 - progress, 3);
              setDisplayValue(Math.floor(value * easeOut));

              if (progress < 1) {
                requestAnimationFrame(animate);
              } else {
                setDisplayValue(value);
              }
            };

            requestAnimationFrame(animate);
          }
        },
        { threshold: 0.5, rootMargin: '0px' }
      );

      if (ref.current) {
        observer.observe(ref.current);
      }

      return () => observer.disconnect();
    }, [value, hasAnimated]);

    return (
      <span ref={ref}>
        {displayValue.toLocaleString()}{suffix}
      </span>
    );
  };

  // StatsSection Component
  const StatsSection = () => {
    const stats = [
      { label: "Active Users", value: 50000, suffix: "+", icon: Users, color: modernColors.blue },
      { label: "Referrals Made", value: 250000, suffix: "+", icon: Share2, color: modernColors.secondary },
      { label: "Revenue Generated", value: 2.4, suffix: "M+", icon: DollarSign, color: modernColors.accent },
      { label: "Success Rate", value: 94, suffix: "%", icon: TrendingUp, color: modernColors.purple }
    ];

    return (
      <section className="relative py-20 bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50"
      >
        <div className="max-w-7xl mx-auto px-4">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, once: true }}
            viewport={{ once: true, margin: "-10%" }}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Trusted by Growing Businesses
            </h2>
            <p className="text-xl text-gray-600">
              Join thousands of companies accelerating their growth
            </p>
          </motion.div>

          <div className="grid md:grid-cols-4 gap-8 text-center">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                className="group p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true, margin: "-10%" }}
              >
                <div 
                  className="w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg"
                  style={{ background: `linear-gradient(135deg, ${stat.color}, ${stat.color}88)` }}
                >
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div 
                  className="text-5xl font-bold mb-2"
                  style={{ color: stat.color }}
                >
                  <StaticCounter value={stat.value} suffix={stat.suffix} />
                </div>
                <div className="text-gray-600 font-medium text-lg">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    );
  };

  return (
    <div ref={containerRef} className="overflow-hidden">
      {/* Hero Section - Modern SaaS Style */}
      <section className="relative pt-32 pb-20 overflow-hidden min-h-screen flex items-center">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-20 items-center">
            {/* Left: Content */}
            <motion.div 
              className="lg:pr-8"
              style={{ y: textY }}
              initial={{ opacity: 0, x: -50 }}
              animate={{
opacity: 1, x: 0 }}
              transition={{ duration: 1, ease: [0.21, 0.47, 0.32, 0.98] }}
            >
              <motion.div
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold mb-8 bg-white border border-gray-200 shadow-sm"
              >
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                <span className="text-gray-700">Trusted by 10,000+ businesses</span>
              </motion.div>

              <motion.h1 
                className="text-5xl lg:text-7xl font-bold leading-[1.1] mb-8 text-gray-900"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.4, ease: [0.21, 0.47, 0.32, 0.98] }}
              >
                Smart Referrals{' '}
                <br />
                for Smart{' '}
                <motion.span 
                  className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  Business
                </motion.span>
              </motion.h1>

              <motion.div
                className="mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
              >
                <div className="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-2xl p-6 mb-4">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <div className="text-xl font-bold text-indigo-700">Zero Risk Guarantee</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-black text-gray-900 mb-1">$0 Setup Fee</div>
                    <div className="text-gray-600">Pay only when you acquire new customers</div>
                  </div>
                </div>
              </motion.div>

              <motion.p 
                className="text-xl leading-relaxed mb-8 max-w-2xl text-gray-600"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                Transform your customer acquisition with our risk-free referral platform. 
                Modern insights. Unlimited growth potential.
              </motion.p>

              <motion.div 
                className="flex flex-col sm:flex-row gap-4 mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.7 }}
              >
                <motion.div
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link
                    to="/signup"
                    className="inline-flex items-center px-8 py-4 bg-indigo-600 text-white rounded-xl font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl hover:bg-indigo-700 focus:ring-4 focus:ring-indigo-200 focus:outline-none"
                  >
                    <span>Get Started Free</span>
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Link>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link
                    to="/contact"
                    className="inline-flex items-center px-8 py-4 bg-white text-gray-700 rounded-xl font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl border border-gray-200 hover:border-gray-300 focus:ring-4 focus:ring-gray-200 focus:outline-none"
                  >
                    <span>Request Demo</span>
                    <Play className="ml-2 w-5 h-5" />
                  </Link>
                </motion.div>
              </motion.div>

              {/* Trust indicators */}
              <motion.div 
                className="flex items-center space-x-6 text-sm text-gray-500"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
              >
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  <span>No setup fees</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  <span>Cancel anytime</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                  <span>24/7 support</span>
                </div>
              </motion.div>
            </motion.div>

            {/* Right: Modern Illustration */}
            <motion.div 
              className="lg:pl-8"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 1, delay: 0.6 }}
            >
              <ModernHeroIllustration />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <StatsSection />

      {/* Interactive Demo */}
      <InteractiveDemo />

      {/* Social Proof */}
      <SocialProof />

      {/* Enhanced Features Section */}
      <section className="py-32 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-20 left-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl"></div>
          <div className="absolute top-20 right-10 w-72 h-72 bg-yellow-200 rounded-full mix-blend-multiply filter blur-xl"></div>
          <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div 
            className="text-center mb-20"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true, margin: "-10%" }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
              className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold mb-6 bg-indigo-100 text-indigo-700"
            >
              <Rocket className="w-4 h-4 mr-2" />
              Complete Solution
            </motion.div>
            <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              The Complete Referral Solution
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to build and scale your referral program, built for modern businesses
            </p>
          </motion.div>

          <motion.div 
            className="grid md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true, margin: "-15%" }}
          >
            {[
                {
                  title: "Zero Risk Setup",
                  description: "No upfront costs, no monthly fees. Pay only when you acquire new customers through referrals.",
                  icon: Shield,
                  color: modernColors.secondary,
                  gradient: "from-emerald-500 to-teal-600",
                  features: ["No setup fees", "Pay per acquisition", "Cancel anytime"],
                  demo: "Try risk-free for 30 days"
                },
{
                  title: "Smart Analytics",
                  description: "Track performance with detailed insights and analytics. See what works and optimize accordingly.",
                  icon: BarChart3,
                  color: modernColors.primary,
                  gradient: "from-indigo-500 to-purple-600",
                  features: ["Real-time tracking", "Conversion insights", "Performance metrics"],
                  demo: "View live dashboard"
                },
                {
                  title: "Instant Rewards",
                  description: "Customers earn instant cash rewards for successful referrals. No waiting, no complications.",
                  icon: Zap,
                  color: modernColors.accent,
                  gradient: "from-amber-500 to-orange-600",
                  features: ["Instant payouts", "Multiple reward types", "Automated processing"],
                  demo: "See payout simulation"
                }
            ].map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <motion.div
                  key={index}
                  className="relative group"
                  variants={fadeInUp}
                  whileHover={{ y: -8 }}
                  transition={{ duration: 0.4, ease: "easeOut" }}
                >
                  <div className="bg-white rounded-3xl p-8 border border-gray-100 shadow-lg hover:shadow-2xl transition-all duration-500 h-full relative overflow-hidden">
                    {/* Hover gradient overlay */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>

                    <motion.div 
                      className={`w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 bg-gradient-to-r ${feature.gradient} relative z-10`}
                      whileHover={{ scale: 1.1, rotate: [0, -5, 5, 0] }}
                      transition={{ duration: 0.5 }}
                    >
                      <IconComponent className="w-8 h-8 text-white" />
                    </motion.div>

                    <h3 className="text-2xl font-bold text-gray-900 mb-4 text-center relative z-10">{feature.title}</h3>
                    <p className="text-gray-600 leading-relaxed text-center mb-6 relative z-10">{feature.description}</p>

                    {/* Feature list */}
                    <div className="space-y-2 relative z-10">
                      {feature.features.map((item, idx) => (
                        <motion.div
                          key={idx}
                          className="flex items-center text-sm text-gray-500"
                          initial={{ opacity: 0, x: -10 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.3 + idx * 0.1 }}
                        >
                          <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                          {item}
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </motion.div>
        </div>
      </section>

      {/* Enhanced How It Works Section */}
      <section className="py-32 bg-white relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center mb-20"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold mb-6 bg-purple-100 text-purple-700"
            >
              <Target className="w-4 h-4 mr-2" />
              Simple Process
            </motion.div>
            <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              How It Works
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Join the referral revolution in three simple steps. Works for businesses and customers alike.
            </p>
          </motion.div>

          {/* Interactive Process Flow */}
          <div className="relative max-w-6xl mx-auto">
            {/* Animated progress line */}
            <div className="hidden lg:block absolute top-1/2 left-12 right-12 h-1 transform -translate-y-1/2 z-0">
              <div className="w-full h-full bg-gray-200 rounded-full">
                <motion.div 
                  className="h-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full"
                  initial={{ width: "0%" }}
                  whileInView={{ width: "100%" }}
                  transition={{ duration: 2, delay: 0.5 }}
                  viewport={{ once: true }}
                />
              </div>
              {/* Flowing particles */}
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute top-1/2 w-3 h-3 bg-white rounded-full shadow-lg transform -translate-y-1/2"
                  initial={{ left: "0%", opacity: 0 }}
                  animate={{ 
                    left: "100%", 
                    opacity: [0, 1, 1, 0],
                    scale: [0.8, 1.2, 1, 0.8]
                  }}
                  transition={{ 
                    duration: 3, 
                    delay: 1 + i * 0.8, 
                    repeat: Infinity,
                    repeatDelay: 2
                  }}
                />
              ))}
            </div>

            <motion.div 
              className="grid md:grid-cols-3 gap-8 lg:gap-12 relative z-10"
              variants={staggerContainer}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true, margin: "-20%" }}
            >
              {[
                {
                  step: "01",
                  title: "Join & Discover",
                  description: "Businesses create campaigns, customers discover deals. Everyone finds their perfect match.",
                  icon: Target,
                  color: "from-blue-500 to-indigo-600",
                  delay: 0,
                  animation: "rotate",
                  preview: (
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-xl flex items-center justify-center">
                      <motion.div
                        className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      />
                    </div>
                  )
                },
                {
                  step: "02", 
                  title: "Share & Connect",
                  description: "Share vouchers with your network. Every share builds connections and creates opportunities.",
                  icon: Share2,
                  color: "from-purple-500 to-pink-600",
                  delay: 0.2,
                  animation: "pulse",
                  preview: (
                    <div className="relative w-16 h-16">
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <motion.div
                        className="absolute inset-2 bg-white rounded-full flex items-center justify-center"
                        animate={{ scale: [1, 0.9, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <Share2 className="w-6 h-6 text-purple-600" />
                      </motion.div>
                    </div>
                  )
                },
                {
                  step: "03",
                  title: "Earn & Grow",
                  description: "Watch your network expand and rewards grow. Success creates more success.",
                  icon: TrendingUp,
                  color: "from-green-500 to-emerald-600",
                  delay: 0.4,
                  animation: "grow",
                  preview: (
                    <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-emerald-100 rounded-xl flex items-center justify-center relative overflow-hidden">
                      <motion.div
                        className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-green-500 to-emerald-600"
                        initial={{ height: "20%" }}
                        animate={{ height: ["20%", "80%", "20%"] }}
                        transition={{ duration: 3, repeat: Infinity }}
                      />
                      <TrendingUp className="w-8 h-8 text-green-600 relative z-10" />
                    </div>
                  )
                }
              ].map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <motion.div
                    key={index}
                    className="relative text-center group"
                    variants={{
                      initial: { opacity: 0, y: 60, scale: 0.9 },
                      animate: { opacity: 1, y: 0, scale: 1 }
                    }}
                    transition={{ duration: 0.8, delay: item.delay, type: "spring", stiffness: 100 }}
                    whileHover={{ y: -10, scale: 1.02 }}
                  >
                    {/* Enhanced step indicator */}
                    <motion.div 
                      className="absolute -top-6 left-1/2 transform -translate-x-1/2 w-20 h-20 rounded-2xl bg-white border-4 border-gray-100 flex items-center justify-center text-lg font-bold text-gray-400 z-20 shadow-lg"
                      whileHover={{ 
                        scale: 1.1, 
                        borderColor: "#6366f1",
                        boxShadow: "0 10px 30px rgba(99, 102, 241, 0.3)"
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      <span className="text-gray-600">{item.step}</span>
                    </motion.div>

                    <div className="bg-white rounded-3xl p-8 pt-16 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 group-hover:border-purple-200 relative overflow-hidden">
                      {/* Gradient overlay on hover */}
                      <motion.div
                        className={`absolute inset-0 bg-gradient-to-br ${item.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
                      />

                      {/* Animated icon container */}
                      <motion.div 
                        className={`w-24 h-24 rounded-2xl flex items-center justify-center mx-auto mb-6 bg-gradient-to-r ${item.color} relative overflow-hidden shadow-lg`}
                        whileHover={{ 
                          scale: 1.1, 
                          rotate: [0, -5, 5, 0],
                          boxShadow: "0 20px 40px rgba(0,0,0,0.15)"
                        }}
                        transition={{ duration: 0.5 }}
                      >
                        <IconComponent className="w-12 h-12 text-white relative z-10" />

                        {/* Background animation */}
                        <motion.div
                          className="absolute inset-0 bg-white/20"
                          animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.2, 0.4, 0.2]
                          }}
                          transition={{ duration: 2, repeat: Infinity, delay: index * 0.5 }}
                        />
                      </motion.div>

                      {/* Preview animation */}
                      <motion.div
                        className="flex justify-center mb-6"
                        initial={{ opacity: 0, scale: 0.8 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.8 + index * 0.2 }}
                      >
                        {item.preview}
                      </motion.div>

                      <motion.h3 
                        className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-purple-700 transition-colors duration-300"
                        whileHover={{ scale: 1.05 }}
                      >
                        {item.title}
                      </motion.h3>

                      <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                        {item.description}
                      </p>

                      {/* Action button with success metrics */}
                      <motion.div
                        className="mt-6 space-y-3"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ delay: 1 + index * 0.1 }}
                      >
                        <motion.button
                          className={`w-full text-sm font-semibold bg-gradient-to-r ${item.color} text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all duration-300 flex items-center justify-center group`}
                          whileHover={{ scale: 1.05, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <span>Explore Step {item.step}</span>
                          <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                        </motion.button>

                        {/* Success metric */}
                        <div className="text-xs text-gray-500 flex items-center justify-center">
                          <CheckCircle className="w-3 h-3 mr-1 text-green-500" />
                          <span>
                            {index === 0 && "Join 50k+ users"}
                            {index === 1 && "2.1M shares monthly"}
                            {index === 2 && "94% success rate"}
                          </span>
                        </div>
                      </motion.div>
                    </div>

                    {/* Floating particles */}
                    {[...Array(3)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute w-1 h-1 bg-purple-400 rounded-full"
                        style={{
                          left: `${20 + i * 30}%`,
                          top: `${30 + i * 20}%`,
                        }}
                        animate={{
                          y: [0, -10, 0],
                          opacity: [0.3, 1, 0.3],
                          scale: [0.8, 1.2, 0.8]
                        }}
                        transition={{
                          duration: 3 + i,
                          repeat: Infinity,
                          delay: index * 0.5 + i * 0.3
                        }}
                      />
                    ))}
                  </motion.div>
                );
              })}
            </motion.div>
          </div>

          {/* Call to action */}
          <motion.div
            className="text-center mt-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2, duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.p 
              className="text-lg text-gray-600 mb-6"
              animate={{ 
                color: ['#6b7280', '#059669', '#6b7280']
              }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              Ready to see it in action?
            </motion.p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                className="px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg font-medium hover:shadow-xl transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                Start as Business
              </motion.button>
              <motion.button
                className="px-8 py-3 border-2 border-indigo-600 text-indigo-600 rounded-lg font-medium hover:bg-indigo-50 transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                Join as Customer
              </motion.button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Interactive Benefits Section */}
      <section className="py-32 bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center mb-20"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Why Choose Referit?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Built for modern businesses that want to grow smart, not just fast
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left side - Benefits list */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="space-y-8">
                {[
                  {
                    title: "Performance-Based Pricing",
                    description: "Only pay when you acquire new customers. Zero risk, maximum ROI.",
                    icon: DollarSign,
                    color: "text-green-600",
                    bg: "bg-green-100"
                  },
                  {
                    title: "Advanced Analytics Dashboard", 
                    description: "Track every metric that matters with our comprehensive analytics suite.",
                    icon: BarChart3,
                    color: "text-blue-600",
                    bg: "bg-blue-100"
                  },
                  {
                    title: "Global Network Reach",
                    description: "Tap into worldwide networks and expand your reach beyond borders.",
                    icon: Globe,
                    color: "text-purple-600", 
                    bg: "bg-purple-100"
                  },
                  {
                    title: "Enterprise Security",
                    description: "Bank-level security with compliance certifications you can trust.",
                    icon: Shield,
                    color: "text-orange-600",
                    bg: "bg-orange-100"
                  }
                ].map((benefit, index) => {
                  const IconComponent = benefit.icon;
                  return (
                    <motion.div
                      key={index}
                      className="flex items-start space-x-4 group"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1, duration: 0.6 }}
                      viewport={{ once: true }}
                      whileHover={{ x: 8 }}
                    >
                      <motion.div 
                        className={`w-12 h-12 rounded-xl ${benefit.bg} flex items-center justify-center flex-shrink-0`}
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ duration: 0.3 }}
                      >
                        <IconComponent className={`w-6 h-6 ${benefit.color}`} />
                      </motion.div>
                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-indigo-600 transition-colors duration-300">
                          {benefit.title}
                        </h4>
                        <p className="text-gray-600 leading-relaxed">{benefit.description}</p>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </motion.div>

            {/* Right side - Interactive visual */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-white rounded-3xl p-8 shadow-2xl border border-gray-100">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">Live Performance</h3>
                  <p className="text-gray-600">See your growth in real-time</p>
                </div>

                {/* Mock analytics dashboard */}
                <div className="space-y-4">
                  {[
                    { label: "Active Referrals", value: "2,847", growth: "+23%", color: "text-green-600" },
                    { label: "Conversion Rate", value: "94.2%", growth: "+8%", color: "text-blue-600" },
                    { label: "Revenue Generated", value: "$127K", growth: "+41%", color: "text-purple-600" }
                  ].map((metric, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-xl"
                      initial={{ opacity: 0, scale: 0.9 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.3 + index * 0.1 }}
                      whileHover={{ scale: 1.02 }}
                    >
                      <div>
                        <div className="text-sm text-gray-600">{metric.label}</div>
                        <div className="text-2xl font-bold text-gray-900">{metric.value}</div>
                      </div>
                      <div className={`text-sm font-semibold ${metric.color}`}>
                        {metric.growth}
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Progress bars */}
                <div className="mt-6 space-y-3">
                  {[75, 92, 68].map((progress, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Goal {index + 1}</span>
                        <span className="text-gray-900 font-medium">{progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <motion.div 
                          className="bg-gradient-to-r from-indigo-500 to-purple-600 h-2 rounded-full"
                          initial={{ width: 0 }}
                          whileInView={{ width: `${progress}%` }}
                          transition={{ duration: 1, delay: 0.5 + index * 0.2 }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Floating decorations */}
              <motion.div
                className="absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full"
                animate={{ y: [0, -10, 0], rotate: 360 }}
                transition={{ duration: 4, repeat: Infinity }}
              />
              <motion.div
                className="absolute -bottom-4 -left-4 w-6 h-6 bg-pink-400 rounded-full"
                animate={{ y: [0, 10, 0], rotate: -360 }}
                transition={{ duration: 5, repeat: Infinity }}
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Performance Dashboard Section */}
      <PerformanceDashboard />

      {/* Advanced Features Section */}
      <AdvancedFeatures />

      {/* Value Calculator */}
      <ValueCalculator />

      {/* Enhanced Testimonials Section */}
      <section className="py-32 bg-gradient-to-br from-gray-900 via-gray-800 to-indigo-900 text-white relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <motion.div 
            className="text-center mb-20"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
              className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold mb-6 bg-white/10 text-white backdrop-blur-sm"
            >
              <Heart className="w-4 h-4 mr-2" />
              Customer Stories
            </motion.div>
            <h2 className="text-5xl lg:text-6xl font-bold mb-6 text-white">
              Trusted by Industry Leaders
            </h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              See what our customers are saying about their success with Referit
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-3 gap-8 mb-16">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                className={`relative group cursor-pointer transition-all duration-500 ${
                  index === activeTestimonial 
                    ? 'lg:col-span-2 lg:row-span-2' 
                    : 'hover:scale-105'
                }`}
                onClick={() => setActiveTestimonial(index)}
                initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 + index * 0.1, duration: 0.6 }}
                whileHover={{ y: -5 }}
              >
                <div className={`bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20 h-full transition-all duration-500 ${
                  index === activeTestimonial 
                    ? 'bg-white/20 border-white/40 shadow-2xl' 
                    : 'hover:bg-white/15 hover:border-white/30'
                }`}>
                  {/* Quote icon */}
                  <Quote className="w-8 h-8 text-white/60 mb-6" />

                  {/* Quote text */}
                  <blockquote className={`font-medium leading-relaxed text-white mb-8 ${
                    index === activeTestimonial ? 'text-xl lg:text-2xl' : 'text-lg'
                  }`}>
                    "{testimonial.quote}"
                  </blockquote>

                  {/* Author info */}
                  <div className="flex items-center">
                    <motion.img 
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      className={`rounded-full border-2 border-white/30 mr-4 ${
                        index === activeTestimonial ? 'w-16 h-16' : 'w-12 h-12'
                      }`}
                      whileHover={{ scale: 1.1 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    />
                    <div>
                      <div className="font-bold text-white">{testimonial.name}</div>
                      <div className="text-white/70 text-sm">{testimonial.role}</div>
                      <div className="flex mt-1">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 fill-current text-yellow-400" />
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Active indicator */}
                  {index === activeTestimonial && (
                    <motion.div
                      className="absolute top-4 right-4 w-3 h-3 bg-green-400 rounded-full"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.3 }}
                    />
                  )}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Trust indicators */}
          <motion.div
            className="grid grid-cols-2 md:grid-cols-4 gap-8 pt-16 border-t border-white/20"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            viewport={{ once: true }}
          >
            {[
              { value: "99.9%", label: "Uptime" },
              { value: "50k+", label: "Happy Users" },
              { value: "24/7", label: "Support" },
              { value: "150+", label: "Countries" }
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.6 + index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                <div className="text-white/70">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>
      {/* Enhanced Final CTA */}
      <section className="py-32 bg-gradient-to-br from-indigo-600 via-purple-700 to-pink-600 text-white relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-10 w-64 h-64 bg-white rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-64 h-64 bg-yellow-200 rounded-full mix-blend-multiply filter blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>
          <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl animate-pulse" style={{ animationDelay: '4s' }}></div>
        </div>

        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%svg%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
              className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold mb-8 bg-white/20 text-white backdrop-blur-sm"
            >
              <Rocket className="w-4 h-4 mr-2" />
              Join 10,000+ Growing Businesses
            </motion.div>

            <h2 className="text-5xl lg:text-7xl font-bold mb-8">
              Ready to{' '}
              <motion.span 
                className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                10x
              </motion.span>{' '}
              Your Growth?
            </h2>

            <p className="text-xl opacity-90 mb-12 max-w-4xl mx-auto leading-relaxed">
              Join thousands of businesses growing through smart referrals. Start free, scale fast, pay only for results.
            </p>

            {/* Enhanced Action buttons with magnetic effect */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                onHoverStart={() => setIsHovering(true)}
                onHoverEnd={() => setIsHovering(false)}
                className="relative group"
              >
                <div className="absolute -inset-1 bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 rounded-2xl blur opacity-30 group-hover:opacity-70 transition duration-1000 group-hover:duration-200"></div>
                <Link
                  to="/signup"
                  className="relative inline-flex items-center px-12 py-6 bg-white text-indigo-600 rounded-2xl font-bold hover:bg-gray-100 transition-all duration-300 shadow-2xl hover:shadow-white/30 text-xl group"
                >
                  <span>Start Free Trial</span>
                  <motion.div
                    className="ml-3"
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <ArrowRight className="w-6 h-6" />
                  </motion.div>
                  {/* Hover particles */}
                  <motion.div
                    className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-400 rounded-full opacity-0 group-hover:opacity-100"
                    animate={isHovering ? { scale: [1, 1.5, 1], opacity: [0, 1, 0] } : {}}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                </Link>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="relative group"
              >
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 to-purple-600 rounded-2xl blur opacity-30 group-hover:opacity-70 transition duration-1000 group-hover:duration-200"></div>
                <Link
                  to="/contact"
                  className="relative inline-flex items-center px-12 py-6 border-2 border-white/30 text-white rounded-2xl font-bold hover:bg-white/10 transition-all duration-300 text-xl backdrop-blur-sm group"
                >
                  <span>Book Demo</span>
                  <Play className="ml-3 w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                  {/* Demo indicator */}
                  <motion.div
                    className="absolute -top-2 -right-2 w-3 h-3 bg-green-400 rounded-full"
                    animate={{ scale: [1, 1.3, 1], opacity: [0.7, 1, 0.7] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                </Link>
              </motion.div>
            </div>

            {/* Trust indicators */}
            <motion.div
              className="flex flex-wrap justify-center items-center gap-8 opacity-80"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 0.8, y: 0 }}
              transition={{ delay: 0.4 }}
              viewport={{ once: true }}
            >
              {[
                { icon: CheckCircle, text: "No setup fees" },
                { icon: Shield, text: "Enterprise security" },
                { icon: Users, text: "24/7 support" },
                { icon: Zap, text: "Instant activation" }
              ].map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <motion.div
                    key={index}
                    className="flex items-center space-x-2 text-white/90"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <IconComponent className="w-5 h-5" />
                    <span className="text-sm font-medium">{item.text}</span>
                  </motion.div>
                );
              })}
            </motion.div>

            {/* Success metrics */}
            <motion.div
              className="mt-16 grid grid-cols-1 sm:grid-cols-3 gap-8 pt-12 border-t border-white/20"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              viewport={{ once: true }}
            >
              {[
                { value: "500%", label: "Average ROI increase" },
                { value: "48hrs", label: "Setup time" },
                { value: "96%", label: "Customer satisfaction" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  className="text-center"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1 + index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="text-4xl font-bold text-white mb-2">{stat.value}</div>
                  <div className="text-white/80 text-sm">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;