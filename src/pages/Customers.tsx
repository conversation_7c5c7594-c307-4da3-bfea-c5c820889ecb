
import React, { useState, useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  Coins, 
  Users, 
  Heart, 
  TrendingUp, 
  Rocket, 
  Zap, 
  DollarSign,
  Shield,
  BarChart3,
  Globe,
  CheckCircle,
  ArrowRight,
  Play,
  Star,
  Brain,
  Gift,
  Share2,
  Sparkles,
  Target,
  Smartphone,
  MapPin,
  Clock,
  Trophy,
  Camera,
  MessageCircle,
  Instagram,
  Facebook,
  Twitter
} from 'lucide-react';
import FloatingShareButton from '../components/FloatingShareButton';
import LazyImage from '../components/LazyImage';

const Customers: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  // Sound effects
  const playClickSound = () => {
    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmocBjs=');
    audio.play().catch(() => {});
  };

  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const textY = useTransform(scrollYProgress, [0, 1], ["0%", "20%"]);

  // Animation variants
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.8, ease: [0.21, 0.47, 0.32, 0.98] }
  };

  const staggerContainer = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  // Modern color palette
  const modernColors = {
    primary: "#10b981",
    secondary: "#14b8a6", 
    accent: "#f59e0b",
    purple: "#8b5cf6",
    blue: "#3b82f6",
    pink: "#ec4899"
  };

  // Static counter component
  const StaticCounter = ({ value, suffix, prefix = "" }: { value: number; suffix: string; prefix?: string }) => {
    const [displayValue, setDisplayValue] = useState(0);
    const [hasAnimated, setHasAnimated] = useState(false);
    const ref = useRef<HTMLSpanElement>(null);

    React.useEffect(() => {
      if (hasAnimated) return;

      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting && !hasAnimated) {
            setHasAnimated(true);
            observer.disconnect();

            let startTime: number;
            const duration = 2000;

            const animate = (currentTime: number) => {
              if (!startTime) startTime = currentTime;
              const progress = Math.min((currentTime - startTime) / duration, 1);

              const easeOut = 1 - Math.pow(1 - progress, 3);
              setDisplayValue(Math.floor(value * easeOut));

              if (progress < 1) {
                requestAnimationFrame(animate);
              } else {
                setDisplayValue(value);
              }
            };

            requestAnimationFrame(animate);
          }
        },
        { threshold: 0.5, rootMargin: '0px' }
      );

      if (ref.current) {
        observer.observe(ref.current);
      }

      return () => observer.disconnect();
    }, [value, hasAnimated]);

    return (
      <span ref={ref}>
        {prefix}{displayValue.toLocaleString()}{suffix}
      </span>
    );
  };

  return (
    <div ref={containerRef} className="overflow-hidden bg-white">
      {/* Hero Section - Premium Design */}
      <section className="relative pt-20 pb-32 overflow-hidden">
        {/* Enhanced Background */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 via-white to-teal-50" />
          <div className="absolute top-0 left-0 w-full h-full opacity-30">
            <div className="absolute top-20 left-20 w-96 h-96 bg-emerald-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse" />
            <div className="absolute top-40 right-20 w-96 h-96 bg-teal-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
            <div className="absolute bottom-20 left-1/3 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse" style={{ animationDelay: '4s' }} />
          </div>

          {/* Floating Elements */}
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(12)].map((_, i) => (
              <motion.div
                key={`floating-${i}`}
                className="absolute w-2 h-2 bg-emerald-400 rounded-full opacity-20"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [0, -30, 0],
                  opacity: [0.2, 0.8, 0.2],
                  scale: [1, 1.5, 1]
                }}
                transition={{
                  duration: 4 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2
                }}
              />
            ))}
          </div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]">
            {/* Left: Enhanced Content */}
            <motion.div 
              className="lg:pr-8 z-10"
              style={{ y: textY }}
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 1, ease: [0.21, 0.47, 0.32, 0.98] }}
            >
              {/* Trust Badge */}
              <motion.div
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold mb-8 bg-white border border-emerald-200 shadow-lg backdrop-blur-sm"
              >
                <div className="w-3 h-3 bg-emerald-500 rounded-full mr-3 animate-pulse"></div>
                <span className="text-emerald-700 font-medium">25,000+ people earning daily</span>
                <motion.div
                  className="ml-3 flex space-x-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1 }}
                >
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                  ))}
                </motion.div>
              </motion.div>

              {/* Main Headline */}
              <motion.h1 
                className="text-6xl lg:text-8xl font-bold leading-[0.9] mb-8"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.4, ease: [0.21, 0.47, 0.32, 0.98] }}
              >
                <span className="text-gray-900">Turn Your</span>
                <br />
                <motion.span 
                  className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 via-teal-600 to-blue-600"
                  whileHover={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  Influence
                </motion.span>
                <br />
                <motion.span 
                  className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500"
                  whileHover={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  Into Income
                </motion.span>
              </motion.h1>

              {/* Value Proposition */}
              <motion.div
                className="mb-8 space-y-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <div className="text-2xl font-semibold text-gray-800 mb-3">
                  Earn $5-50+ every time friends use your recommendations
                </div>
                <p className="text-xl text-gray-600 leading-relaxed">
                  Share deals from your favorite brands. When friends purchase, you earn real money. 
                  <span className="text-emerald-600 font-semibold"> It's that simple.</span>
                </p>
              </motion.div>

              {/* Key Benefits */}
              <motion.div 
                className="flex flex-wrap gap-3 mb-10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
              >
                {[
                  { icon: Zap, text: "Instant payouts", color: "bg-yellow-100 text-yellow-700 border-yellow-200" },
                  { icon: Shield, text: "Verified brands", color: "bg-blue-100 text-blue-700 border-blue-200" },
                  { icon: DollarSign, text: "Free forever", color: "bg-emerald-100 text-emerald-700 border-emerald-200" }
                ].map((benefit, index) => (
                  <motion.div 
                    key={index}
                    className={`flex items-center px-4 py-2 rounded-full border ${benefit.color} font-medium text-sm backdrop-blur-sm`}
                    whileHover={{ scale: 1.05, y: -2 }}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 1 + index * 0.1 }}
                  >
                    <benefit.icon className="w-4 h-4 mr-2" />
                    {benefit.text}
                  </motion.div>
                ))}
              </motion.div>

              {/* CTAs */}
              <motion.div 
                className="flex flex-col sm:flex-row gap-4 mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1 }}
              >
                <motion.div
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  className="group relative"
                >
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-600 to-teal-600 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300" />
                  <Link
                    to="/signup"
                    onClick={playClickSound}
                    className="relative inline-flex items-center px-10 py-5 bg-emerald-600 text-white rounded-2xl font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl hover:bg-emerald-700 focus:ring-4 focus:ring-emerald-200 focus:outline-none"
                  >
                    <span>Start Earning Free</span>
                    <motion.div
                      className="ml-3"
                      animate={{ x: [0, 4, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ArrowRight className="w-5 h-5" />
                    </motion.div>
                  </Link>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link
                    to="/contact"
                    className="inline-flex items-center px-10 py-5 bg-white text-gray-700 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl border border-gray-200 hover:border-gray-300 focus:ring-4 focus:ring-gray-200 focus:outline-none"
                  >
                    <Play className="mr-3 w-5 h-5" />
                    <span>Watch Demo</span>
                  </Link>
                </motion.div>
              </motion.div>

              {/* Social Proof */}
              <motion.div 
                className="text-sm text-gray-500"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.2 }}
              >
                <div className="flex items-center mb-2">
                  <div className="flex -space-x-2 mr-3">
                    {[...Array(4)].map((_, i) => (
                      <div key={i} className="w-8 h-8 rounded-full bg-gradient-to-r from-emerald-400 to-teal-500 border-2 border-white flex items-center justify-center text-white text-xs font-bold">
                        {String.fromCharCode(65 + i)}
                      </div>
                    ))}
                  </div>
                  <span>Join 25,000+ earning members</span>
                </div>
              </motion.div>
            </motion.div>

            {/* Right: Enhanced App Mockup */}
            <motion.div 
              className="lg:pl-8 relative z-10"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 1, delay: 0.6 }}
            >
              <div className="relative w-full max-w-sm mx-auto">
                {/* Enhanced Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/30 via-teal-400/30 to-blue-400/30 rounded-full blur-3xl scale-150 -z-10 animate-pulse" />

                {/* Floating Network Nodes */}
                <div className="absolute -top-16 -left-16 w-40 h-40 z-20">
                  <div className="relative w-full h-full">
                    {/* Central user node */}
                    <motion.div
                      className="absolute w-6 h-6 bg-emerald-500 rounded-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 shadow-lg border-2 border-white"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />

                    {/* Connected nodes */}
                    {[...Array(6)].map((_, i) => {
                      const angle = (i * 60) * (Math.PI / 180);
                      const radius = 50;
                      const x = Math.cos(angle) * radius;
                      const y = Math.sin(angle) * radius;

                      return (
                        <motion.div
                          key={`node-${i}`}
                          className="absolute w-3 h-3 bg-teal-400 rounded-full shadow-md border border-white"
                          style={{
                            left: `calc(50% + ${x}px)`,
                            top: `calc(50% + ${y}px)`,
                            transform: 'translate(-50%, -50%)'
                          }}
                          initial={{ scale: 0, opacity: 0 }}
                          animate={{ scale: 1, opacity: 0.8 }}
                          transition={{ delay: 0.5 + i * 0.1, duration: 0.4 }}
                        />
                      );
                    })}
                  </div>
                </div>

                {/* Premium Phone Mockup */}
                <motion.div 
                  className="relative"
                  initial={{ opacity: 0, scale: 0.8, rotateY: 15 }}
                  animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                  transition={{ duration: 1.2, delay: 0.5, type: "spring", stiffness: 100 }}
                >
                  {/* Phone Frame */}
                  <div className="relative bg-gray-900 rounded-[3rem] p-2 shadow-2xl border-4 border-gray-800">
                    <div className="bg-black rounded-[2.7rem] p-1">
                      <div className="bg-white rounded-[2.5rem] overflow-hidden relative h-[600px]">
                        {/* Dynamic Island */}
                        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-7 bg-black rounded-b-3xl z-30" />

                        {/* App Content */}
                        <div className="h-full bg-gradient-to-br from-gray-50 to-white">
                          {/* Starbucks Voucher Card */}
                          <motion.div
                            className="bg-white shadow-xl overflow-hidden relative h-full"
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 1.2, duration: 0.8 }}
                            whileHover={{ scale: 1.01, y: -2 }}
                          >
                            {/* Header Section with Starburst */}
                            <div className="relative bg-gradient-to-br from-green-600 to-green-700 px-6 py-12 text-white overflow-hidden" style={{ backgroundColor: '#00704A' }}>
                              {/* Back arrow */}
                              <div className="absolute top-4 left-4 z-30">
                                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                </svg>
                              </div>

                              {/* Enhanced Starburst behind logo */}
                              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 -z-10">
                                {[...Array(24)].map((_, i) => (
                                  <motion.div
                                    key={`starburst-${i}`}
                                    className="absolute bg-gradient-to-t from-transparent via-green-400/40 to-transparent"
                                    style={{
                                      width: '4px',
                                      height: '180px',
                                      transformOrigin: '50% 90px',
                                      transform: `rotate(${i * 15}deg)`,
                                      borderRadius: '2px',
                                    }}
                                    animate={{
                                      opacity: [0.3, 0.8, 0.3],
                                      scale: [1, 1.1, 1]
                                    }}
                                    transition={{
                                      duration: 4,
                                      delay: i * 0.1,
                                      repeat: Infinity
                                    }}
                                  />
                                ))}
                              </div>

                              {/* Content */}
                              <div className="relative z-20 text-center">
                                <motion.div
                                  className="w-24 h-24 mx-auto mb-8 bg-white rounded-full flex items-center justify-center shadow-2xl"
                                  animate={{ rotate: [0, 2, -2, 0] }}
                                  transition={{ duration: 8, repeat: Infinity }}
                                >
                                  <LazyImage 
                                    src="/images/Starbucks_Corporation_Logo_2011.svg.png" 
                                    alt="Starbucks" 
                                    className="w-20 h-20 object-contain"
                                  />
                                </motion.div>

                                <motion.div
                                  className="text-center"
                                  animate={{ scale: [1, 1.02, 1] }}
                                  transition={{ duration: 3, repeat: Infinity }}
                                >
                                  <div className="text-7xl font-bold mb-2 tracking-tight">25%</div>
                                  <div className="text-2xl font-light tracking-[0.3em]">OFF</div>
                                </motion.div>
                              </div>
                            </div>

                            {/* Enhanced White Content Section */}
                            <div 
                              className="bg-white rounded-t-3xl -mt-6 relative z-10 pt-8 pb-8 px-6 flex-1" 
                              style={{ 
                                boxShadow: '0 -12px 40px rgba(0, 0, 0, 0.15), 0 -6px 20px rgba(0, 0, 0, 0.1)' 
                              }}
                            >
                              {/* Expiry and Reward */}
                              <div className="flex items-center justify-between mb-6">
                                <div>
                                  <div className="text-lg font-bold text-gray-900 mb-1">Expiring</div>
                                  <div className="text-gray-500 font-medium flex items-center">
                                    <Clock className="w-4 h-4 mr-1" />
                                    25 May
                                  </div>
                                </div>
                                <motion.div 
                                  className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white px-6 py-3 rounded-full shadow-lg"
                                  whileHover={{ scale: 1.05 }}
                                >
                                  <span className="font-bold text-lg">$12 Reward</span>
                                </motion.div>
                              </div>

                              {/* Enhanced Conditions */}
                              <div className="mb-6">
                                <h4 className="text-lg font-bold text-gray-900 mb-3 flex items-center">
                                  <CheckCircle className="w-5 h-5 text-emerald-500 mr-2" />
                                  Conditions
                                </h4>
                                <p className="text-gray-600 text-sm leading-relaxed bg-gray-50 p-3 rounded-lg">
                                  Valid for any large size coffee or specialty drink. Cannot be combined with other offers.
                                </p>
                              </div>

                              {/* How it works */}
                              <div className="mb-8">
                                <h4 className="text-lg font-bold text-gray-900 mb-3 flex items-center">
                                  <Sparkles className="w-5 h-5 text-purple-500 mr-2" />
                                  How it works
                                </h4>
                                <div className="space-y-2 text-sm text-gray-600">
                                  <div className="flex items-center">
                                    <div className="w-6 h-6 bg-emerald-100 text-emerald-600 rounded-full flex items-center justify-center text-xs font-bold mr-3">1</div>
                                    Share this voucher with friends
                                  </div>
                                  <div className="flex items-center">
                                    <div className="w-6 h-6 bg-emerald-100 text-emerald-600 rounded-full flex items-center justify-center text-xs font-bold mr-3">2</div>
                                    They get 25% off their order
                                  </div>
                                  <div className="flex items-center">
                                    <div className="w-6 h-6 bg-emerald-100 text-emerald-600 rounded-full flex items-center justify-center text-xs font-bold mr-3">3</div>
                                    You earn $12 instantly
                                  </div>
                                </div>
                              </div>

                              {/* Enhanced Barcode */}
                              <div className="mb-8">
                                <div className="bg-white border-2 border-gray-100 p-6 rounded-xl">
                                  <div className="flex justify-center items-center h-16 w-full mb-3">
                                    {[2, 1, 2, 3, 1, 1, 6, 1, 3, 3, 1, 1, 1, 6, 1, 1, 3, 3, 1, 1, 1, 6, 1, 1, 3].map((width, i) => (
                                      <div
                                        key={`barcode-${i}`}
                                        className={i % 2 === 0 ? "bg-black" : "bg-transparent"}
                                        style={{ width: `${width}px`, height: '100%' }}
                                      />
                                    ))}
                                  </div>
                                  <div className="text-center text-xs text-gray-700 font-mono tracking-[0.2em]">
                                    012345678905
                                  </div>
                                </div>
                              </div>

                              {/* Enhanced CTA */}
                              <motion.button
                                className="w-full bg-gradient-to-r from-gray-900 to-black text-white py-4 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                                whileHover={{ scale: 1.02, y: -2 }}
                                whileTap={{ scale: 0.98 }}
                              >
                                <Share2 className="w-5 h-5 mr-2" />
                                Share & Earn $12
                              </motion.button>
                            </div>
                          </motion.div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Floating Elements */}
                  <motion.div
                    className="absolute -top-6 -right-8 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 backdrop-blur-sm z-50"
                    initial={{ opacity: 0, x: 30, y: -10 }}
                    animate={{ opacity: 1, x: 0, y: 0 }}
                    transition={{ duration: 0.6, delay: 1.8 }}
                    whileHover={{ scale: 1.05, y: -5 }}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full flex items-center justify-center shadow-lg">
                        <DollarSign className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <div className="font-bold text-gray-900 text-sm">+$25 Earned!</div>
                        <div className="text-gray-500 text-xs">3 friends used your link</div>
                      </div>
                    </div>
                    <motion.div
                      className="absolute -top-1 -right-1 w-4 h-4 bg-emerald-400 rounded-full border-2 border-white"
                      animate={{ scale: [1, 1.3, 1], opacity: [0.7, 1, 0.7] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    />
                  </motion.div>

                  {/* Social Sharing Indicators */}
                  <motion.div
                    className="absolute top-1/3 -right-16 z-40"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.8, delay: 2.5 }}
                  >
                    <div className="flex flex-col space-y-3">
                      {[
                        { Icon: Instagram, color: "from-pink-500 to-purple-600" },
                        { Icon: MessageCircle, color: "from-green-500 to-emerald-600" },
                        { Icon: Facebook, color: "from-blue-500 to-indigo-600" }
                      ].map(({ Icon, color }, i) => (
                        <motion.div
                          key={i}
                          className={`w-8 h-8 bg-gradient-to-r ${color} rounded-full shadow-lg flex items-center justify-center border-2 border-white`}
                          animate={{ scale: [1, 1.1, 1], y: [0, -2, 0] }}
                          transition={{ duration: 2, delay: 3 + i * 0.5, repeat: Infinity }}
                          whileHover={{ scale: 1.2 }}
                        >
                          <Icon className="w-4 h-4 text-white" />
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Enhanced Stats Section */}
      <section className="relative py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true, margin: "-10%" }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold mb-8 bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-700 border border-emerald-200"
            >
              <Trophy className="w-4 h-4 mr-2" />
              Community Impact
            </motion.div>
            <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              The Numbers Speak
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Join a thriving community of earners who've discovered the power of shared recommendations
            </p>
          </motion.div>

          <div className="grid md:grid-cols-4 gap-8 text-center">
            {[
              { 
                label: "Active Earners", 
                value: 28500, 
                suffix: "+", 
                icon: Users, 
                color: "from-blue-500 to-indigo-600",
                description: "Growing daily"
              },
              { 
                label: "Total Earned", 
                value: 2.4, 
                suffix: "M+", 
                prefix: "$",
                icon: DollarSign, 
                color: "from-emerald-500 to-teal-600",
                description: "In rewards paid"
              },
              { 
                label: "Successful Shares", 
                value: 185000, 
                suffix: "+", 
                icon: Share2, 
                color: "from-purple-500 to-pink-600",
                description: "Friends helped"
              },
              { 
                label: "Average Monthly", 
                value: 215, 
                suffix: "", 
                prefix: "$",
                icon: TrendingUp, 
                color: "from-orange-500 to-red-500",
                description: "Per active user"
              }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                className="group relative p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 overflow-hidden"
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true, margin: "-10%" }}
                whileHover={{ y: -8 }}
              >
                {/* Background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />

                <div 
                  className={`w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-xl bg-gradient-to-r ${stat.color} relative z-10`}
                >
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div 
                  className={`text-5xl font-bold mb-2 bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`}
                >
                  {stat.prefix}<StaticCounter value={stat.value} suffix={stat.suffix} />
                </div>
                <div className="text-gray-900 font-semibold text-lg mb-2">{stat.label}</div>
                <div className="text-gray-500 text-sm">{stat.description}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section - Enhanced */}
      <section className="py-32 bg-gradient-to-br from-gray-50 to-emerald-50 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4">
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold mb-8 bg-white border border-emerald-200 shadow-lg text-emerald-700"
            >
              <Rocket className="w-4 h-4 mr-2" />
              Simple Process
            </motion.div>
            <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Start Earning in Minutes
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Three simple steps to turn your recommendations into real money
            </p>
          </motion.div>

          {/* Enhanced Process Steps */}
          <div className="relative max-w-6xl mx-auto">
            {/* Animated connection line */}
            <div className="hidden lg:block absolute top-32 left-1/6 right-1/6 h-1 z-0">
              <div className="w-full h-full bg-gray-200 rounded-full">
                <motion.div 
                  className="h-full bg-gradient-to-r from-emerald-500 via-teal-500 to-blue-500 rounded-full"
                  initial={{ width: "0%" }}
                  whileInView={{ width: "100%" }}
                  transition={{ duration: 2, delay: 0.5 }}
                  viewport={{ once: true }}
                />
              </div>
            </div>

            <div className="grid md:grid-cols-3 gap-12 relative z-10">
              {[
                {
                  step: "01",
                  title: "Browse & Discover",
                  description: "Explore deals from 1000+ verified brands you already love and trust",
                  icon: Sparkles,
                  color: "from-blue-500 to-indigo-600",
                  features: ["Curated deals", "Verified brands", "Personal taste matching"]
                },
                {
                  step: "02",
                  title: "Share Authentically",
                  description: "Share with friends through any platform - social media, messaging, or our smart links",
                  icon: Share2,
                  color: "from-emerald-500 to-teal-600",
                  features: ["One-click sharing", "Smart tracking", "Multiple platforms"]
                },
                {
                  step: "03",
                  title: "Earn Instantly",
                  description: "Get paid the moment friends make a purchase. Money hits your account immediately",
                  icon: DollarSign,
                  color: "from-yellow-500 to-orange-600",
                  features: ["Instant payouts", "Real-time tracking", "No minimums"]
                }
              ].map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <motion.div
                    key={index}
                    className="relative group text-center"
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.2, duration: 0.6 }}
                    viewport={{ once: true }}
                    whileHover={{ y: -8 }}
                  >
                    {/* Step indicator */}
                    <motion.div 
                      className="absolute -top-8 left-1/2 transform -translate-x-1/2 w-20 h-20 rounded-3xl bg-white border-4 border-gray-100 flex items-center justify-center text-lg font-bold text-gray-500 z-20 shadow-xl"
                      whileHover={{ 
                        scale: 1.1, 
                        borderColor: "#10b981",
                        boxShadow: "0 20px 40px rgba(16, 185, 129, 0.3)"
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      <span className="text-gray-700">{item.step}</span>
                    </motion.div>

                    <div className="bg-white rounded-3xl p-8 pt-16 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-100 group-hover:border-emerald-200 h-full relative overflow-hidden">
                      {/* Gradient overlay */}
                      <motion.div
                        className={`absolute inset-0 bg-gradient-to-br ${item.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
                      />

                      {/* Icon */}
                      <motion.div 
                        className={`w-20 h-20 rounded-3xl flex items-center justify-center mx-auto mb-6 bg-gradient-to-r ${item.color} shadow-xl relative z-10`}
                        whileHover={{ 
                          scale: 1.1, 
                          rotate: [0, -5, 5, 0],
                          boxShadow: "0 25px 50px rgba(0,0,0,0.15)"
                        }}
                        transition={{ duration: 0.5 }}
                      >
                        <IconComponent className="w-10 h-10 text-white" />
                      </motion.div>

                      <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-emerald-700 transition-colors">
                        {item.title}
                      </h3>

                      <p className="text-gray-600 leading-relaxed mb-6 text-lg">
                        {item.description}
                      </p>

                      {/* Features */}
                      <div className="space-y-2">
                        {item.features.map((feature, idx) => (
                          <motion.div
                            key={idx}
                            className="flex items-center justify-center text-sm text-gray-500"
                            initial={{ opacity: 0, x: -10 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.3 + idx * 0.1 }}
                          >
                            <CheckCircle className="w-4 h-4 text-emerald-500 mr-2" />
                            {feature}
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Final CTA */}
      <section className="py-32 bg-gradient-to-br from-emerald-600 via-teal-700 to-blue-600 text-white relative overflow-hidden">
        {/* Dynamic background elements */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-10 w-72 h-72 bg-white rounded-full mix-blend-multiply filter blur-3xl animate-pulse" />
          <div className="absolute bottom-20 right-10 w-72 h-72 bg-yellow-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
          <div className="absolute top-1/2 left-1/2 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse" style={{ animationDelay: '4s' }} />
        </div>

        <div className="max-w-6xl mx-auto px-4 text-center relative">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold mb-8 bg-white/20 text-white backdrop-blur-sm border border-white/30"
            >
              <Gift className="w-4 h-4 mr-2" />
              Join 28,500+ earning members
            </motion.div>

            <h2 className="text-6xl lg:text-8xl font-bold mb-8 leading-[0.9]">
              Your Network{' '}
              <motion.span 
                className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                Is Your
              </motion.span>{' '}
              <motion.span 
                className="text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-red-400"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                Net Worth
              </motion.span>
            </h2>

            <p className="text-2xl opacity-90 mb-16 max-w-4xl mx-auto leading-relaxed">
              Stop giving away free recommendations. 
              <span className="text-yellow-200 font-semibold"> Start getting paid for your influence.</span>
            </p>

            {/* Enhanced CTA buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                className="relative group"
              >
                <div className="absolute -inset-2 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-600 rounded-3xl blur-lg opacity-30 group-hover:opacity-70 transition duration-500 group-hover:duration-200" />
                <Link
                  to="/signup"
                  className="relative inline-flex items-center px-12 py-6 bg-white text-emerald-600 rounded-3xl font-bold hover:bg-gray-100 transition-all duration-300 shadow-2xl hover:shadow-white/30 text-xl group"
                >
                  <span>Start Earning Free</span>
                  <motion.div
                    className="ml-3"
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <ArrowRight className="w-6 h-6" />
                  </motion.div>
                </Link>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                className="relative group"
              >
                <div className="absolute -inset-2 bg-gradient-to-r from-blue-400 to-teal-600 rounded-3xl blur-lg opacity-30 group-hover:opacity-70 transition duration-500 group-hover:duration-200" />
                <Link
                  to="/contact"
                  className="relative inline-flex items-center px-12 py-6 border-2 border-white/30 text-white rounded-3xl font-bold hover:bg-white/10 transition-all duration-300 text-xl backdrop-blur-sm group"
                >
                  <Play className="mr-3 w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                  <span>See Potential Earnings</span>
                </Link>
              </motion.div>
            </div>

            {/* Trust indicators */}
            <motion.div
              className="flex flex-wrap justify-center items-center gap-8 opacity-80 mb-16"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 0.8, y: 0 }}
              transition={{ delay: 0.4 }}
              viewport={{ once: true }}
            >
              {[
                { icon: CheckCircle, text: "Free forever" },
                { icon: Zap, text: "Instant payouts" },
                { icon: Shield, text: "Secure & trusted" },
                { icon: Heart, text: "28k+ happy members" }
              ].map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <motion.div
                    key={index}
                    className="flex items-center space-x-2 text-white/90 bg-white/10 px-4 py-2 rounded-full backdrop-blur-sm"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ scale: 1.05 }}
                  >
                    <IconComponent className="w-5 h-5" />
                    <span className="text-sm font-medium">{item.text}</span>
                  </motion.div>
                );
              })}
            </motion.div>

            {/* Final metrics */}
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-3 gap-8 pt-12 border-t border-white/20"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              viewport={{ once: true }}
            >
              {[
                { value: "$215", label: "Average monthly earnings" },
                { value: "2min", label: "Time to first share" },
                { value: "98%", label: "Member satisfaction" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  className="text-center"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1 + index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="text-4xl font-bold text-white mb-2">{stat.value}</div>
                  <div className="text-white/80 text-sm">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Floating Share Button */}
      <FloatingShareButton />
    </div>
  );
};

export default Customers;
