import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  Sparkles, 
  Users, 
  Target, 
  TrendingUp,
  Share2,
  Heart,
  Gift,
  Zap
} from 'lucide-react';
import SocialAuth from '../components/SocialAuth';

const Login: React.FC = () => {
  const [hoveredFeature, setHoveredFeature] = useState<number | null>(null);

  const handleAuthSuccess = (provider: string, userData: any) => {
    console.log(`Login successful with ${provider}:`, userData);
    // Handle successful authentication here
  };

  const floatingElements = [
    { icon: Gift, color: 'text-purple-400', delay: 0, position: { x: 10, y: 20 } },
    { icon: Share2, color: 'text-blue-400', delay: 0.5, position: { x: 85, y: 15 } },
    { icon: Heart, color: 'text-pink-400', delay: 1, position: { x: 15, y: 75 } },
    { icon: Zap, color: 'text-yellow-400', delay: 1.5, position: { x: 80, y: 80 } },
    { icon: Target, color: 'text-green-400', delay: 2, position: { x: 50, y: 10 } },
    { icon: TrendingUp, color: 'text-indigo-400', delay: 2.5, position: { x: 5, y: 50 } }
  ];

  const features = [
    { title: "Instant Rewards", subtitle: "Get paid immediately" },
    { title: "Smart Tracking", subtitle: "Real-time analytics" },
    { title: "Global Network", subtitle: "1000+ brands" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating Icons */}
        {floatingElements.map((element, index) => {
          const IconComponent = element.icon;
          return (
            <motion.div
              key={index}
              className={`absolute ${element.color} opacity-20`}
              style={{ 
                left: `${element.position.x}%`, 
                top: `${element.position.y}%` 
              }}
              initial={{ opacity: 0, scale: 0, rotate: -180 }}
              animate={{ 
                opacity: 0.2, 
                scale: 1, 
                rotate: 0,
                y: [-10, 10, -10],
              }}
              transition={{ 
                delay: element.delay,
                duration: 2,
                y: {
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }
              }}
            >
              <IconComponent className="w-8 h-8" />
            </motion.div>
          );
        })}

        {/* Gradient Orbs */}
        <motion.div
          className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-r from-purple-300 to-pink-300 rounded-full blur-3xl opacity-30"
          animate={{ 
            scale: [1, 1.2, 1],
            x: [0, 50, 0],
            y: [0, -30, 0]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-blue-300 to-indigo-300 rounded-full blur-3xl opacity-20"
          animate={{ 
            scale: [1, 0.8, 1],
            x: [0, -40, 0],
            y: [0, 20, 0]
          }}
          transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
        />
      </div>

      <div className="relative z-10 flex min-h-screen">
        {/* Left Side - Illustration & Features */}
        <div className="hidden lg:flex lg:w-1/2 flex-col justify-center items-center p-12 relative">
          {/* Animated Referral Network */}
          <motion.div
            className="relative w-96 h-96 mb-8 flex items-center justify-center"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, ease: "easeOut" }}
          >
            {/* Network Container */}
            <div className="relative w-full h-full">
              {/* Network Connections (SVG) */}
              <svg className="absolute inset-0 w-full h-full" viewBox="0 0 384 384">
                <defs>
                  <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#6366f1" stopOpacity="0.4"/>
                    <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.6"/>
                  </linearGradient>
                  <linearGradient id="signalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#10b981"/>
                    <stop offset="50%" stopColor="#34d399"/>
                    <stop offset="100%" stopColor="#6ee7b7"/>
                  </linearGradient>
                </defs>
                
                {/* Connection Lines */}
                <motion.line
                  x1="192" y1="192" x2="120" y2="120"
                  stroke="url(#connectionGradient)"
                  strokeWidth="2"
                  strokeDasharray="8,4"
                  initial={{ pathLength: 0, opacity: 0 }}
                  animate={{ pathLength: 1, opacity: 1 }}
                  transition={{ duration: 1.5, delay: 0.5 }}
                />
                <motion.line
                  x1="192" y1="192" x2="280" y2="100"
                  stroke="url(#connectionGradient)"
                  strokeWidth="2"
                  strokeDasharray="8,4"
                  initial={{ pathLength: 0, opacity: 0 }}
                  animate={{ pathLength: 1, opacity: 1 }}
                  transition={{ duration: 1.5, delay: 0.7 }}
                />
                <motion.line
                  x1="192" y1="192" x2="320" y2="220"
                  stroke="url(#connectionGradient)"
                  strokeWidth="2"
                  strokeDasharray="8,4"
                  initial={{ pathLength: 0, opacity: 0 }}
                  animate={{ pathLength: 1, opacity: 1 }}
                  transition={{ duration: 1.5, delay: 0.9 }}
                />
                <motion.line
                  x1="192" y1="192" x2="100" y2="280"
                  stroke="url(#connectionGradient)"
                  strokeWidth="2"
                  strokeDasharray="8,4"
                  initial={{ pathLength: 0, opacity: 0 }}
                  animate={{ pathLength: 1, opacity: 1 }}
                  transition={{ duration: 1.5, delay: 1.1 }}
                />
                <motion.line
                  x1="120" y1="120" x2="280" y2="100"
                  stroke="url(#connectionGradient)"
                  strokeWidth="1.5"
                  strokeDasharray="6,3"
                  opacity="0.6"
                  initial={{ pathLength: 0, opacity: 0 }}
                  animate={{ pathLength: 1, opacity: 0.6 }}
                  transition={{ duration: 1.5, delay: 1.3 }}
                />
                <motion.line
                  x1="280" y1="100" x2="320" y2="220"
                  stroke="url(#connectionGradient)"
                  strokeWidth="1.5"
                  strokeDasharray="6,3"
                  opacity="0.6"
                  initial={{ pathLength: 0, opacity: 0 }}
                  animate={{ pathLength: 1, opacity: 0.6 }}
                  transition={{ duration: 1.5, delay: 1.5 }}
                />
                
                {/* Animated Signal Pulses */}
                <motion.circle
                  r="3"
                  fill="url(#signalGradient)"
                  opacity="0.9"
                  filter="drop-shadow(0 0 6px rgba(16, 185, 129, 0.6))"
                >
                  <animateMotion
                    dur="4s"
                    repeatCount="indefinite"
                    path="M192,192 L120,120"
                  />
                  <animate
                    attributeName="opacity"
                    values="0;1;1;0"
                    dur="4s"
                    repeatCount="indefinite"
                  />
                </motion.circle>
                
                <motion.circle
                  r="3"
                  fill="url(#signalGradient)"
                  opacity="0.9"
                  filter="drop-shadow(0 0 6px rgba(16, 185, 129, 0.6))"
                >
                  <animateMotion
                    dur="5s"
                    repeatCount="indefinite"
                    begin="1s"
                    path="M192,192 L280,100"
                  />
                  <animate
                    attributeName="opacity"
                    values="0;1;1;0"
                    dur="5s"
                    repeatCount="indefinite"
                    begin="1s"
                  />
                </motion.circle>
                
                <motion.circle
                  r="3"
                  fill="url(#signalGradient)"
                  opacity="0.9"
                  filter="drop-shadow(0 0 6px rgba(16, 185, 129, 0.6))"
                >
                  <animateMotion
                    dur="6s"
                    repeatCount="indefinite"
                    begin="2s"
                    path="M192,192 L320,220"
                  />
                  <animate
                    attributeName="opacity"
                    values="0;1;1;0"
                    dur="6s"
                    repeatCount="indefinite"
                    begin="2s"
                  />
                </motion.circle>
                
                <motion.circle
                  r="3"
                  fill="url(#signalGradient)"
                  opacity="0.9"
                  filter="drop-shadow(0 0 6px rgba(16, 185, 129, 0.6))"
                >
                  <animateMotion
                    dur="4.5s"
                    repeatCount="indefinite"
                    begin="0.5s"
                    path="M120,120 L280,100"
                  />
                  <animate
                    attributeName="opacity"
                    values="0;1;1;0"
                    dur="4.5s"
                    repeatCount="indefinite"
                    begin="0.5s"
                  />
                </motion.circle>
              </svg>

              {/* User Avatars - Positioned at line intersections and endpoints */}
              {/* Central Avatar (You) - Center intersection */}
              <motion.div
                className="absolute w-16 h-16 z-20"
                style={{ left: '165px', top: '160px', transform: 'translate(-50%, -50%)' }}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: 1, 
                  opacity: 1,
                  y: [0, -4, 0]
                }}
                transition={{ 
                  scale: { duration: 0.8, delay: 0.3 },
                  y: { duration: 3, repeat: Infinity, ease: "easeInOut" }
                }}
              >
                <div className="relative">
                  <img
                    src="/images/uifaces-human-image.jpg"
                    alt="You"
                    className="w-16 h-16 rounded-full border-4 border-indigo-500 shadow-lg object-cover"
                  />
                  <motion.div
                    className="absolute -inset-1 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full opacity-75"
                    animate={{ 
                      scale: [1, 1.1, 1],
                      opacity: [0.75, 0.3, 0.75]
                    }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    style={{ zIndex: -1 }}
                  />
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg">
                    <Users className="w-3 h-3" />
                  </div>
                </div>
              </motion.div>

              {/* Network Avatar 1 - Top left endpoint */}
              <motion.div
                className="absolute w-12 h-12 z-10"
                style={{ left: '105px', top: '105px', transform: 'translate(-50%, -50%)' }}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: 1, 
                  opacity: 1,
                  x: [0, 3, 0],
                  y: [0, -2, 0]
                }}
                transition={{ 
                  scale: { duration: 0.8, delay: 0.6 },
                  x: { duration: 4, repeat: Infinity, ease: "easeInOut" },
                  y: { duration: 4, repeat: Infinity, ease: "easeInOut", delay: 0.5 }
                }}
              >
                <img
                  src="/images/uifaces-human-image (2).jpg"
                  alt="Friend 1"
                  className="w-12 h-12 rounded-full border-3 border-blue-400 shadow-lg object-cover"
                />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center text-xs text-white">
                  <span className="text-xs">2</span>
                </div>
              </motion.div>

              {/* Network Avatar 2 - Top right endpoint */}
              <motion.div
                className="absolute w-12 h-12 z-10"
                style={{ left: '265px', top: '85px', transform: 'translate(-50%, -50%)' }}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: 1, 
                  opacity: 1,
                  x: [0, -2, 0],
                  y: [0, 3, 0]
                }}
                transition={{ 
                  scale: { duration: 0.8, delay: 0.8 },
                  x: { duration: 3.5, repeat: Infinity, ease: "easeInOut" },
                  y: { duration: 3.5, repeat: Infinity, ease: "easeInOut", delay: 1 }
                }}
              >
                <img
                  src="/images/uifaces-human-image (3).jpg"
                  alt="Friend 2"
                  className="w-12 h-12 rounded-full border-3 border-emerald-400 shadow-lg object-cover"
                />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-emerald-500 rounded-full flex items-center justify-center text-xs text-white">
                  <span className="text-xs">5</span>
                </div>
              </motion.div>

              {/* Network Avatar 3 - Right endpoint */}
              <motion.div
                className="absolute w-12 h-12 z-10"
                style={{ left: '305px', top: '205px', transform: 'translate(-50%, -50%)' }}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: 1, 
                  opacity: 1,
                  x: [0, 2, 0],
                  y: [0, -3, 0]
                }}
                transition={{ 
                  scale: { duration: 0.8, delay: 1 },
                  x: { duration: 5, repeat: Infinity, ease: "easeInOut", delay: 0.3 },
                  y: { duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1.5 }
                }}
              >
                <img
                  src="/images/uifaces-human-image (4).jpg"
                  alt="Friend 3"
                  className="w-12 h-12 rounded-full border-3 border-purple-400 shadow-lg object-cover"
                />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-purple-500 rounded-full flex items-center justify-center text-xs text-white">
                  <span className="text-xs">3</span>
                </div>
              </motion.div>

              {/* Network Avatar 4 - Bottom left endpoint */}
              <motion.div
                className="absolute w-12 h-12 z-10"
                style={{ left: '85px', top: '265px', transform: 'translate(-50%, -50%)' }}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: 1, 
                  opacity: 1,
                  x: [0, -3, 0],
                  y: [0, 2, 0]
                }}
                transition={{ 
                  scale: { duration: 0.8, delay: 1.2 },
                  x: { duration: 4.5, repeat: Infinity, ease: "easeInOut", delay: 2 },
                  y: { duration: 4.5, repeat: Infinity, ease: "easeInOut" }
                }}
              >
                <img
                  src="/images/uifaces-human-image (5).jpg"
                  alt="Friend 4"
                  className="w-12 h-12 rounded-full border-3 border-pink-400 shadow-lg object-cover"
                />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-pink-500 rounded-full flex items-center justify-center text-xs text-white">
                  <span className="text-xs">7</span>
                </div>
              </motion.div>

              {/* Secondary Network Nodes (lower opacity - not in network yet) */}
              <motion.div
                className="absolute w-8 h-8 z-5"
                style={{ left: '60px', top: '160px', transform: 'translate(-50%, -50%)' }}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: 1, 
                  opacity: 0.4,
                  x: [0, 1, 0],
                  y: [0, -1, 0]
                }}
                transition={{ 
                  scale: { duration: 0.8, delay: 1.4 },
                  x: { duration: 6, repeat: Infinity, ease: "easeInOut" },
                  y: { duration: 6, repeat: Infinity, ease: "easeInOut", delay: 0.8 }
                }}
              >
                <img
                  src="/images/uifaces-human-image (6).jpg"
                  alt="Potential Connection"
                  className="w-8 h-8 rounded-full border-2 border-gray-300 shadow-md object-cover opacity-60 blur-[0.5px]"
                />
              </motion.div>

              <motion.div
                className="absolute w-8 h-8 z-5"
                style={{ left: '340px', top: '160px', transform: 'translate(-50%, -50%)' }}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: 1, 
                  opacity: 0.4,
                  x: [0, -1, 0],
                  y: [0, 2, 0]
                }}
                transition={{ 
                  scale: { duration: 0.8, delay: 1.6 },
                  x: { duration: 5.5, repeat: Infinity, ease: "easeInOut", delay: 1 },
                  y: { duration: 5.5, repeat: Infinity, ease: "easeInOut" }
                }}
              >
                <img
                  src="/images/uifaces-human-image (7).jpg"
                  alt="Potential Connection"
                  className="w-8 h-8 rounded-full border-2 border-gray-300 shadow-md object-cover opacity-60 blur-[0.5px]"
                />
              </motion.div>

              {/* Additional background avatars - not in network */}
              <motion.div
                className="absolute w-6 h-6 z-0"
                style={{ left: '50px', top: '50px', transform: 'translate(-50%, -50%)' }}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: 1, 
                  opacity: 0.2,
                  x: [0, 1, 0],
                  y: [0, -1, 0]
                }}
                transition={{ 
                  scale: { duration: 0.8, delay: 2 },
                  x: { duration: 8, repeat: Infinity, ease: "easeInOut" },
                  y: { duration: 8, repeat: Infinity, ease: "easeInOut", delay: 1 }
                }}
              >
                <img
                  src="/images/uifaces-human-image (8).jpg"
                  alt="Future Connection"
                  className="w-6 h-6 rounded-full border border-gray-200 shadow-sm object-cover opacity-40 blur-[1px]"
                />
              </motion.div>

              <motion.div
                className="absolute w-6 h-6 z-0"
                style={{ left: '350px', top: '50px', transform: 'translate(-50%, -50%)' }}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: 1, 
                  opacity: 0.2,
                  x: [0, -1, 0],
                  y: [0, 1, 0]
                }}
                transition={{ 
                  scale: { duration: 0.8, delay: 2.2 },
                  x: { duration: 7, repeat: Infinity, ease: "easeInOut" },
                  y: { duration: 7, repeat: Infinity, ease: "easeInOut", delay: 1.5 }
                }}
              >
                <img
                  src="/images/uifaces-human-image (9).jpg"
                  alt="Future Connection"
                  className="w-6 h-6 rounded-full border border-gray-200 shadow-sm object-cover opacity-40 blur-[1px]"
                />
              </motion.div>
            </div>

            {/* Floating Gift Box (top left) */}
            <motion.div
              className="absolute top-8 left-8 z-30"
              initial={{ opacity: 0, scale: 0, rotate: -45 }}
              animate={{ 
                opacity: 1, 
                scale: 1, 
                rotate: [0, 5, -5, 0],
                y: [-5, 5, -5]
              }}
              transition={{ 
                duration: 2,
                delay: 0.5,
                y: { duration: 4, repeat: Infinity, ease: "easeInOut" },
                rotate: { duration: 6, repeat: Infinity, ease: "easeInOut" }
              }}
            >
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg shadow-lg relative transform rotate-12">
                <div className="absolute inset-1 bg-yellow-300 rounded-md"></div>
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-1 h-full bg-red-500"></div>
                <div className="absolute top-1/2 left-0 transform -translate-y-1/2 w-full h-1 bg-red-500"></div>
                <Gift className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 text-red-600" />
              </div>
            </motion.div>

            {/* $50 Money Indicator (top center) */}
            <motion.div
              className="absolute top-4 left-1/2 transform -translate-x-1/2 z-30"
              initial={{ opacity: 0, y: -20, scale: 0.8 }}
              animate={{ 
                opacity: 1, 
                y: [0, -8, 0], 
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 2,
                delay: 1,
                y: { duration: 3, repeat: Infinity, ease: "easeInOut" },
                scale: { duration: 3, repeat: Infinity, ease: "easeInOut" }
              }}
            >
              <div className="bg-gradient-to-br from-emerald-400 to-green-500 text-white px-4 py-2 rounded-full font-bold text-lg shadow-xl border-2 border-white">
                $50
              </div>
              <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-green-500"></div>
            </motion.div>

            {/* Heart Icon (bottom left) */}
            <motion.div
              className="absolute bottom-16 left-4 z-30"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ 
                opacity: [0.7, 1, 0.7], 
                scale: [1, 1.2, 1],
                rotate: [0, 10, -10, 0]
              }}
              transition={{ 
                duration: 2,
                delay: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <div className="w-10 h-10 bg-gradient-to-br from-pink-400 to-red-500 rounded-full flex items-center justify-center shadow-lg">
                <Heart className="w-5 h-5 text-white fill-current" />
              </div>
            </motion.div>

            {/* Dollar Sign Circle (bottom right) */}
            <motion.div
              className="absolute bottom-8 right-8 z-30"
              initial={{ opacity: 0, scale: 0, rotate: 180 }}
              animate={{ 
                opacity: 1, 
                scale: [1, 1.1, 1], 
                rotate: [0, 360]
              }}
              transition={{ 
                duration: 3,
                delay: 2,
                scale: { duration: 2, repeat: Infinity, ease: "easeInOut" },
                rotate: { duration: 8, repeat: Infinity, ease: "linear" }
              }}
            >
              <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-indigo-500 rounded-full flex items-center justify-center shadow-lg border-2 border-white">
                <span className="text-white font-bold text-lg">$</span>
              </div>
            </motion.div>

            {/* Additional Yellow Card (left side) */}
            <motion.div
              className="absolute left-2 top-1/2 transform -translate-y-1/2 z-30"
              initial={{ opacity: 0, x: -20, rotate: -15 }}
              animate={{ 
                opacity: 1, 
                x: [0, 5, 0], 
                rotate: [-15, -10, -15]
              }}
              transition={{ 
                duration: 2,
                delay: 0.8,
                x: { duration: 4, repeat: Infinity, ease: "easeInOut" },
                rotate: { duration: 4, repeat: Infinity, ease: "easeInOut" }
              }}
            >
              <div className="w-12 h-9 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-lg shadow-lg transform rotate-12 flex items-center justify-center">
                <Heart className="w-4 h-4 text-yellow-700" />
              </div>
            </motion.div>

            {/* Sparkle Effects */}
            {[...Array(8)].map((_, index) => {
              const positions = [
                { x: 20, y: 20 }, { x: 180, y: 30 }, { x: 30, y: 100 }, { x: 170, y: 120 },
                { x: 10, y: 180 }, { x: 190, y: 200 }, { x: 50, y: 260 }, { x: 160, y: 280 }
              ];
              const pos = positions[index] || { x: Math.random() * 200, y: Math.random() * 300 };

              return (
                <motion.div
                  key={index}
                  className="absolute z-10"
                  style={{ left: pos.x, top: pos.y }}
                  initial={{ opacity: 0, scale: 0, rotate: 0 }}
                  animate={{ 
                    opacity: [0, 1, 0],
                    scale: [0, 1, 0.5, 0],
                    rotate: [0, 180, 360]
                  }}
                  transition={{ 
                    duration: 3,
                    delay: index * 0.3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <Sparkles className="w-3 h-3 text-yellow-400" />
                </motion.div>
              );
            })}

            {/* Background Glow */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-blue-100/50 via-purple-100/50 to-pink-100/50 rounded-full blur-3xl -z-10"
              animate={{ 
                scale: [1, 1.1, 1],
                opacity: [0.3, 0.5, 0.3]
              }}
              transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
            />

            {/* Floating Particles */}
            {[...Array(12)].map((_, index) => (
              <motion.div
                key={index}
                className="absolute w-1 h-1 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full opacity-40"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  scale: [0, 1, 0],
                  opacity: [0, 0.6, 0],
                  y: [0, -20, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: index * 0.3,
                  ease: "easeInOut",
                }}
              />
            ))}
          </motion.div>

          {/* Feature Highlights */}
          <motion.div
            className="space-y-4 w-full max-w-sm"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="flex items-center space-x-4 p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 cursor-pointer"
                whileHover={{ scale: 1.02, y: -2 }}
                onHoverStart={() => setHoveredFeature(index)}
                onHoverEnd={() => setHoveredFeature(null)}
              >
                <motion.div
                  className="w-3 h-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"
                  animate={{ 
                    scale: hoveredFeature === index ? 1.5 : 1,
                    boxShadow: hoveredFeature === index ? 
                      "0 0 20px rgba(99, 102, 241, 0.6)" : 
                      "0 0 0px rgba(99, 102, 241, 0)"
                  }}
                />
                <div>
                  <h4 className="font-semibold text-gray-800">{feature.title}</h4>
                  <p className="text-sm text-gray-600">{feature.subtitle}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Right Side - Login Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <motion.div
            className="w-full max-w-md"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {/* Header */}
            <motion.div
              className="text-center mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              <motion.div
                className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-3xl mb-6 relative overflow-hidden"
                whileHover={{ scale: 1.05 }}
                animate={{ 
                  boxShadow: [
                    "0 0 0 0 rgba(99, 102, 241, 0)",
                    "0 0 0 10px rgba(99, 102, 241, 0.1)",
                    "0 0 0 0 rgba(99, 102, 241, 0)"
                  ]
                }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <motion.div
                  className="text-3xl font-bold text-white"
                  animate={{ rotate: [0, 5, -5, 0] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                >
                  R
                </motion.div>
                <motion.div
                  className="absolute inset-0 bg-white/20"
                  animate={{ 
                    x: ["-100%", "100%"],
                  }}
                  transition={{ 
                    duration: 2, 
                    repeat: Infinity, 
                    ease: "easeInOut",
                    repeatDelay: 3
                  }}
                />
              </motion.div>

              <motion.h1 
                className="text-4xl font-bold text-gray-900 mb-3"
                animate={{ 
                  backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"]
                }}
                style={{
                  background: "linear-gradient(45deg, #1f2937, #4f46e5, #1f2937)",
                  backgroundSize: "200% 200%",
                  backgroundClip: "text",
                  WebkitBackgroundClip: "text",
                  color: "transparent"
                }}
                transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
              >
                Welcome Back!
              </motion.h1>

              <motion.p 
                className="text-gray-600 text-lg"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                Ready to start earning through referrals?
              </motion.p>
            </motion.div>

            {/* Login Form */}
            <motion.div
              className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/50 p-8 relative overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              whileHover={{ 
                boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.15)",
                scale: 1.01
              }}
            >
              {/* Subtle animated background */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-indigo-50/50 to-purple-50/50"
                animate={{ 
                  background: [
                    "linear-gradient(45deg, rgba(99, 102, 241, 0.02), rgba(168, 85, 247, 0.02))",
                    "linear-gradient(45deg, rgba(168, 85, 247, 0.02), rgba(99, 102, 241, 0.02))"
                  ]
                }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
              />

              <div className="relative z-10">
                <SocialAuth mode="login" onSuccess={handleAuthSuccess} />
              </div>
            </motion.div>

            {/* Footer */}
            <motion.div
              className="text-center mt-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.6 }}
            >
              <p className="text-gray-600 mb-4">
                Don't have an account?{' '}
                <Link 
                  to="/signup" 
                  className="font-semibold text-indigo-600 hover:text-indigo-500 transition-colors relative group"
                >
                  <span className="relative z-10">Sign up for free</span>
                  <motion.div
                    className="absolute inset-0 bg-indigo-100 rounded-lg -z-0"
                    initial={{ scale: 0, opacity: 0 }}
                    whileHover={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  />
                </Link>
              </p>

              {/* Trust Indicators */}
              <motion.div
                className="flex items-center justify-center space-x-6 text-xs text-gray-500"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8, duration: 0.6 }}
              >
                {[
                  { label: "Secure Login", color: "bg-green-500" },
                  { label: "256-bit SSL", color: "bg-blue-500" },
                  { label: "GDPR Compliant", color: "bg-purple-500" }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 + index * 0.1 }}
                  >
                    <motion.div 
                      className={`w-2 h-2 ${item.color} rounded-full mr-2`}
                      animate={{ 
                        scale: [1, 1.2, 1],
                        opacity: [1, 0.7, 1]
                      }}
                      transition={{ 
                        duration: 2, 
                        repeat: Infinity, 
                        delay: index * 0.5,
                        ease: "easeInOut"
                      }}
                    />
                    {item.label}
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Login;