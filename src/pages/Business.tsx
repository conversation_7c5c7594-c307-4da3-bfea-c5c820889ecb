import React, { useState, useEffect } from 'react';
import { motion, useScroll, useTransform, useInView } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  Store, 
  Users, 
  Target, 
  TrendingUp, 
  Rocket, 
  Zap, 
  DollarSign,
  Shield,
  BarChart3,
  Globe,
  CheckCircle,
  ArrowRight,
  Play,
  Star,
  Brain,
  Building,
  Sparkles,
  Gift,
  Share2,
  Heart,
  Layers,
  PieChart,
  Settings,
  Smartphone,
  Monitor,
  Camera,
  MessageSquare,
  Award,
  Briefcase
} from 'lucide-react';
import BusinessProfileSetup from '../components/BusinessProfileSetup';
import QuickBusinessSetup from '../components/QuickBusinessSetup';
import { useBusinessProfile } from '../contexts/BusinessProfileContext';
import { useNavigate, useLocation } from 'react-router-dom';

const Business: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const textY = useTransform(scrollYProgress, [0, 1], ["0%", "20%"]);

  // Animation variants
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.8, ease: [0.21, 0.47, 0.32, 0.98] }
  };

  const staggerContainer = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  // Modern color palette
  const modernColors = {
    primary: "#6366f1",
    secondary: "#10b981", 
    accent: "#f59e0b",
    purple: "#8b5cf6",
    blue: "#3b82f6",
    teal: "#14b8a6"
  };

  // Static counter component
  const StaticCounter = ({ value, suffix, prefix = "" }: { value: number; suffix: string; prefix?: string }) => {
    const [displayValue, setDisplayValue] = useState(0);
    const [hasAnimated, setHasAnimated] = useState(false);
    const ref = useRef<HTMLSpanElement>(null);

    React.useEffect(() => {
      if (hasAnimated) return;

      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting && !hasAnimated) {
            setHasAnimated(true);
            observer.disconnect();

            let startTime: number;
            const duration = 2000;

            const animate = (currentTime: number) => {
              if (!startTime) startTime = currentTime;
              const progress = Math.min((currentTime - startTime) / duration, 1);

              const easeOut = 1 - Math.pow(1 - progress, 3);
              setDisplayValue(Math.floor(value * easeOut));

              if (progress < 1) {
                requestAnimationFrame(animate);
              } else {
                setDisplayValue(value);
              }
            };

            requestAnimationFrame(animate);
          }
        },
        { threshold: 0.5, rootMargin: '0px' }
      );

      if (ref.current) {
        observer.observe(ref.current);
      }

      return () => observer.disconnect();
    }, [value, hasAnimated]);

    return (
      <span ref={ref}>
        {prefix}{displayValue.toLocaleString()}{suffix}
      </span>
    );
  };

  const { profile, updateProfile, isProfileComplete } = useBusinessProfile();
  const [showFullSetup, setShowFullSetup] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Check if we're in edit mode (coming from dashboard profile edit)
  const isEditMode = isProfileComplete && profile;

  useEffect(() => {
    // Only redirect to dashboard if we're creating a new profile and it's complete
    if (isProfileComplete && profile && !isEditMode) {
      navigate('/dashboard');
    }
  }, [isProfileComplete, profile, navigate, isEditMode]);

  const handleProfileComplete = (profileData: any) => {
    updateProfile(profileData);
    // If editing, go back to dashboard; if creating new, also go to dashboard
    navigate('/dashboard');
  };

  if (showFullSetup || isEditMode) {
    return (
      <BusinessProfileSetup 
        onComplete={handleProfileComplete}
        existingData={isEditMode ? profile : undefined}
      />
    );
  }

  return (
    <div ref={containerRef} className="overflow-hidden bg-white">
      {/* Hero Section - Premium Design */}
      <section className="relative pt-20 pb-32 overflow-hidden">
        {/* Enhanced Background */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-purple-50" />
          <div className="absolute top-0 left-0 w-full h-full opacity-30">
            <div className="absolute top-20 left-20 w-96 h-96 bg-indigo-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse" />
            <div className="absolute top-40 right-20 w-96 h-96 bg-purple-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
            <div className="absolute bottom-20 left-1/3 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse" style={{ animationDelay: '4s' }} />
          </div>

          {/* Grid Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }} />
          </div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]">
            {/* Left: Enhanced Content */}
            <motion.div 
              className="lg:pr-8 z-10"
              style={{ y: textY }}
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 1, ease: [0.21, 0.47, 0.32, 0.98] }}
            >
              {/* Trust Badge */}
              <motion.div
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold mb-8 bg-white border border-indigo-200 shadow-lg backdrop-blur-sm"
              >
                <div className="w-3 h-3 bg-green-500 rounded-full mr-3 animate-pulse"></div>
                <span className="text-indigo-700 font-medium">Zero risk, maximum reward</span>
                <motion.div
                  className="ml-3 flex space-x-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1 }}
                >
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                  ))}
                </motion.div>
              </motion.div>

              {/* Main Headline */}
              <motion.h1 
                className="text-6xl lg:text-8xl font-bold leading-[0.9] mb-8"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.4, ease: [0.21, 0.47, 0.32, 0.98] }}
              >
                <span className="text-gray-900">Scale Your</span>
                <br />
                <motion.span 
                  className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600"
                  whileHover={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  Business
                </motion.span>
                <br />
                <motion.span 
                  className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-500 via-teal-500 to-blue-500"
                  whileHover={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  Through Trust
                </motion.span>
              </motion.h1>

              {/* Value Proposition */}
              <motion.div
                className="mb-8 space-y-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <div className="text-2xl font-semibold text-gray-800 mb-3">
                  Pay-per-acquisition only. No upfront costs, no monthly fees.
                </div>
                <p className="text-xl text-gray-600 leading-relaxed">
                  Create compelling referral campaigns that turn your customers into brand ambassadors. 
                  <span className="text-indigo-600 font-semibold"> Scale without risk, grow without limits.</span>
                </p>
              </motion.div>

              {/* Key Benefits */}
              <motion.div 
                className="flex flex-wrap gap-3 mb-10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
              >
                {[
                  { icon: Shield, text: "Zero upfront cost", color: "bg-green-100 text-green-700 border-green-200" },
                  { icon: Zap, text: "5min setup", color: "bg-blue-100 text-blue-700 border-blue-200" },
                  { icon: TrendingUp, text: "340% avg ROI", color: "bg-purple-100 text-purple-700 border-purple-200" }
                ].map((benefit, index) => (
                  <motion.div 
                    key={index}
                    className={`flex items-center px-4 py-2 rounded-full border ${benefit.color} font-medium text-sm backdrop-blur-sm`}
                    whileHover={{ scale: 1.05, y: -2 }}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 1 + index * 0.1 }}
                  >
                    <benefit.icon className="w-4 h-4 mr-2" />
                    {benefit.text}
                  </motion.div>
                ))}
              </motion.div>

              {/* CTAs */}
              <motion.div 
                className="flex flex-col sm:flex-row gap-4 mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1 }}
              >
                <motion.div
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  className="group relative"
                >
                  <div className="absolute -inset-1 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300" />
                  <Link
                    to="/signup"
                    className="relative inline-flex items-center px-10 py-5 bg-indigo-600 text-white rounded-2xl font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl hover:bg-indigo-700 focus:ring-4 focus:ring-indigo-200 focus:outline-none"
                  >
                    <span>Start Free Campaign</span>
                    <motion.div
                      className="ml-3"
                      animate={{ x: [0, 4, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ArrowRight className="w-5 h-5" />
                    </motion.div>
                  </Link>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link
                    to="/contact"
                    className="inline-flex items-center px-10 py-5 bg-white text-gray-700 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl border border-gray-200 hover:border-gray-300 focus:ring-4 focus:ring-gray-200 focus:outline-none"
                  >
                    <Play className="mr-3 w-5 h-5" />
                    <span>Schedule Demo</span>
                  </Link>
                </motion.div>
              </motion.div>

              {/* Social Proof */}
              <motion.div 
                className="text-sm text-gray-500"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.2 }}
              >
                <div className="flex items-center mb-2">
                  <div className="flex -space-x-2 mr-3">
                    {[...Array(4)].map((_, i) => (
                      <div key={i} className="w-8 h-8 rounded-full bg-gradient-to-r from-indigo-400 to-purple-500 border-2 border-white flex items-center justify-center text-white text-xs font-bold">
                        {String.fromCharCode(65 + i)}
                      </div>
                    ))}
                  </div>
                  <span>Trusted by 15,000+ growing businesses</span>
                </div>
              </motion.div>
            </motion.div>

            {/* Right: Enhanced Dashboard Mockup */}
            <motion.div 
              className="lg:pl-8 relative z-10"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 1, delay: 0.6 }}
            >
              <div className="relative w-full max-w-2xl mx-auto">
                {/* Enhanced Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-400/30 via-purple-400/30 to-pink-400/30 rounded-full blur-3xl scale-150 -z-10 animate-pulse" />

                {/* Main Dashboard Container */}
                <motion.div 
                  className="relative"
                  initial={{ opacity: 0, scale: 0.8, rotateY: 20 }}
                  animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                  transition={{ duration: 1.2, delay: 0.5 }}
                >
                  <div className="relative bg-white rounded-3xl shadow-2xl border border-gray-200/50 p-8 backdrop-blur-sm overflow-hidden">
                    {/* Background Pattern */}
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full opacity-50 transform translate-x-16 -translate-y-16" />

                    {/* Dashboard Header */}
                    <div className="flex items-center justify-between mb-8">
                      <div className="flex items-center">
                        <div className="w-14 h-14 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center text-white mr-4 shadow-lg">
                          <Building className="w-7 h-7" />
                        </div>
                        <div>
                          <h3 className="font-bold text-xl text-gray-900">Business Analytics</h3>
                          <div className="text-sm text-gray-500 flex items-center">
                            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                            Live dashboard
                          </div>
                        </div>
                      </div>
                      <motion.div 
                        className="bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-semibold flex items-center"
                        animate={{ scale: [1, 1.05, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <TrendingUp className="w-4 h-4 mr-1" />
                        +340% ROI
                      </motion.div>
                    </div>

                    {/* Key Metrics Grid */}
                    <div className="grid grid-cols-2 gap-6 mb-8">
                      {[
                        { 
                          label: "New Customers", 
                          value: 1247, 
                          change: "+156%", 
                          icon: Users,
                          color: "text-blue-600",
                          bg: "bg-blue-50"
                        },
                        { 
                          label: "Revenue Generated", 
                          value: 89400, 
                          change: "+89%", 
                          icon: DollarSign,
                          color: "text-green-600",
                          bg: "bg-green-50",
                          prefix: "$"
                        },
                        { 
                          label: "Active Campaigns", 
                          value: 8, 
                          change: "+23%", 
                          icon: Target,
                          color: "text-purple-600",
                          bg: "bg-purple-50"
                        },
                        { 
                          label: "Conversion Rate", 
                          value: 94.2, 
                          change: "+12%", 
                          icon: BarChart3,
                          color: "text-orange-600",
                          bg: "bg-orange-50",
                          suffix: "%"
                        }
                      ].map((metric, index) => (
                        <motion.div
                          key={index}
                          className={`${metric.bg} rounded-2xl p-5 border border-gray-100`}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 1 + index * 0.1 }}
                          whileHover={{ scale: 1.02, y: -2 }}
                        >
                          <div className="flex items-center justify-between mb-3">
                            <metric.icon className={`w-5 h-5 ${metric.color}`} />
                            <div className="text-xs text-green-600 font-bold bg-green-100 px-2 py-1 rounded">
                              {metric.change}
                            </div>
                          </div>
                          <div className="text-xs text-gray-600 mb-1">{metric.label}</div>
                          <div className={`text-2xl font-bold ${metric.color}`}>
                            {metric.prefix}{metric.value.toLocaleString()}{metric.suffix}
                          </div>
                        </motion.div>
                      ))}
                    </div>

                    {/* Live Activity Feed */}
                    <div className="space-y-4">
                      <h4 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                        <Zap className="w-5 h-5 text-yellow-500 mr-2" />
                        Live Activity
                      </h4>
                      {[
                        { 
                          icon: "👤", 
                          text: "Sarah shared coffee voucher", 
                          time: "1m ago", 
                          color: "bg-blue-100",
                          badge: "New referral"
                        },
                        { 
                          icon: "💰", 
                          text: "Customer acquired: $85 revenue", 
                          time: "3m ago", 
                          color: "bg-green-100",
                          badge: "Conversion"
                        },
                        { 
                          icon: "📤", 
                          text: "Campaign shared 23 times", 
                          time: "5m ago", 
                          color: "bg-purple-100",
                          badge: "Viral"
                        }
                      ].map((activity, index) => (
                        <motion.div
                          key={index}
                          className="flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-50 transition-colors relative overflow-hidden"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 1.5 + index * 0.2 }}
                        >
                          <div className={`w-10 h-10 ${activity.color} rounded-full flex items-center justify-center text-sm shadow-md`}>
                            {activity.icon}
                          </div>
                          <div className="flex-1">
                            <div className="text-sm text-gray-900 font-medium">{activity.text}</div>
                            <div className="text-xs text-gray-500 flex items-center">
                              <div className="w-1 h-1 bg-gray-400 rounded-full mr-2"></div>
                              {activity.time}
                            </div>
                          </div>
                          <div className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full font-medium">
                            {activity.badge}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>

                  {/* Floating Success Notification */}
                  <motion.div
                    className="absolute -top-6 -right-8 bg-white rounded-2xl shadow-2xl border border-gray-100 p-4 backdrop-blur-sm z-50"
                    initial={{ opacity: 0, x: 50, y: -20 }}
                    animate={{ opacity: 1, x: 0, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.8 }}
                    whileHover={{ scale: 1.05, y: -5 }}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg">
                        <TrendingUp className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <div className="font-bold text-gray-900 text-sm">Campaign Success!</div>
                        <div className="text-green-600 font-bold text-lg">1,247 new customers</div>
                        <div className="text-gray-500 text-xs">This month</div>
                      </div>
                    </div>
                    <motion.div
                      className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"
                      animate={{ scale: [1, 1.3, 1], opacity: [0.7, 1, 0.7] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    />
                  </motion.div>

                  {/* Floating ROI Indicator */}
                  <motion.div
                    className="absolute -bottom-6 -left-8 bg-white rounded-2xl shadow-lg border border-gray-100 p-4"
                    initial={{ opacity: 0, x: -50, y: 20 }}
                    animate={{ opacity: 1, x: 0, y: 0 }}
                    transition={{ duration: 0.8, delay: 2.2 }}
                    whileHover={{ scale: 1.05 }}
                  >
                    <div className="text-center">
                      <div className="text-xs text-gray-500 mb-1">ROI</div>
                      <div className="text-3xl font-bold text-purple-600">340%</div>
                      <div className="text-xs text-purple-500">vs industry avg</div>
                    </div>
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Enhanced Stats Section */}
      <section className="relative py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true, margin: "-10%" }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold mb-8 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 border border-indigo-200"
            >
              <Award className="w-4 h-4 mr-2" />
              Industry Leading Results
            </motion.div>
            <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Trusted by Industry Leaders
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Join thousands of businesses that have transformed their growth with smart referrals
            </p>
          </motion.div>

          <div className="grid md:grid-cols-4 gap-8 text-center">
            {[
              { 
                label: "Active Businesses", 
                value: 15200, 
                suffix: "+", 
                icon: Building, 
                color: "from-blue-500 to-indigo-600",
                description: "Growing monthly"
              },
              { 
                label: "Campaigns Created", 
                value: 85000, 
                suffix: "+", 
                icon: Target, 
                color: "from-emerald-500 to-teal-600",
                description: "Live campaigns"
              },
              { 
                label: "Revenue Generated", 
                value: 24.7, 
                suffix: "M+", 
                prefix: "$",
                icon: DollarSign, 
                color: "from-yellow-500 to-orange-600",
                description: "For our clients"
              },
              { 
                label: "Success Rate", 
                value: 96, 
                suffix: "%", 
                icon: TrendingUp, 
                color: "from-purple-500 to-pink-600",
                description: "Campaign success"
              }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                className="group relative p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 overflow-hidden"
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true, margin: "-10%" }}
                whileHover={{ y: -8 }}
              >
                {/* Background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />

                <div 
                  className={`w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-xl bg-gradient-to-r ${stat.color} relative z-10`}
                >
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div 
                  className={`text-5xl font-bold mb-2 bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`}
                >
                  {stat.prefix}<StaticCounter value={stat.value} suffix={stat.suffix} />
                </div>
                <div className="text-gray-900 font-semibold text-lg mb-2">{stat.label}</div>
                <div className="text-gray-500 text-sm">{stat.description}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section - Enhanced */}
      <section className="py-32 bg-gradient-to-br from-gray-50 to-indigo-50 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4">
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold mb-8 bg-white border border-indigo-200 shadow-lg text-indigo-700"
            >
              <Rocket className="w-4 h-4 mr-2" />
              Simple Process
            </motion.div>
            <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Launch in Minutes
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From campaign creation to customer acquisition - see how businesses scale with Referit
            </p>
          </motion.div>

          {/* Enhanced Process Steps */}
          <div className="relative max-w-6xl mx-auto">
            {/* Animated connection line */}
            <div className="hidden lg:block absolute top-32 left-1/6 right-1/6 h-1 z-0">
              <div className="w-full h-full bg-gray-200 rounded-full">
                <motion.div 
                  className="h-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full"
                  initial={{ width: "0%" }}
                  whileInView={{ width: "100%" }}
                  transition={{ duration: 2, delay: 0.5 }}
                  viewport={{ once: true }}
                />
              </div>
            </div>

            <div className="grid md:grid-cols-5 gap-8 relative z-10">
              {[
                {
                  step: "01",
                  title: "Create Campaign",
                  description: "Design compelling referral campaigns with our AI-powered builder",
                  icon: Target,
                  color: "from-blue-500 to-indigo-600",
                  features: ["5min setup", "AI optimization", "Custom rewards"]
                },
                {
                  step: "02",
                  title: "Design Vouchers",
                  description: "Create beautiful vouchers that customers love to share",
                  icon: Sparkles,
                  color: "from-emerald-500 to-teal-600",
                  features: ["Professional templates", "Brand customization", "Mobile optimized"]
                },
                {
                  step: "03",
                  title: "Launch & Distribute",
                  description: "Deploy across multiple channels with QR codes and smart links",
                  icon: Rocket,
                  color: "from-purple-500 to-pink-600",
                  features: ["Multi-channel", "QR codes", "Smart tracking"]
                },
                {
                  step: "04",
                  title: "Track Performance",
                  description: "Monitor real-time analytics and optimize for maximum ROI",
                  icon: BarChart3,
                  color: "from-orange-500 to-red-500",
                  features: ["Real-time data", "ROI tracking", "Performance insights"]
                },
                {
                  step: "05",
                  title: "Scale & Grow",
                  description: "Watch your customer base expand through word-of-mouth marketing",
                  icon: TrendingUp,
                  color: "from-green-500 to-emerald-600",
                  features: ["Exponential growth", "Viral effects", "Customer loyalty"]
                }
              ].map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <motion.div
                    key={index}
                    className="relative group text-center"
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.6 }}
                    viewport={{ once: true }}
                    whileHover={{ y: -8 }}
                  >
                    {/* Step indicator */}
                    <motion.div 
                      className="absolute -top-8 left-1/2 transform -translate-x-1/2 w-20 h-20 rounded-3xl bg-white border-4 border-gray-100 flex items-center justify-center text-lg font-bold text-gray-500 z-20 shadow-xl"
                      whileHover={{ 
                        scale: 1.1, 
                        borderColor: "#6366f1",
                        boxShadow: "0 20px 40px rgba(99, 102, 241, 0.3)"
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      <span className="text-gray-700">{item.step}</span>
                    </motion.div>

                    <div className="bg-white rounded-3xl p-6 pt-16 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-100 group-hover:border-indigo-200 h-full relative overflow-hidden">
                      {/* Gradient overlay */}
                      <motion.div
                        className={`absolute inset-0 bg-gradient-to-br ${item.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
                      />

                      {/* Icon */}
                      <motion.div 
                        className={`w-16 h-16 rounded-3xl flex items-center justify-center mx-auto mb-6 bg-gradient-to-r ${item.color} shadow-xl relative z-10`}
                        whileHover={{ 
                          scale: 1.1, 
                          rotate: [0, -5, 5, 0],
                          boxShadow: "0 25px 50px rgba(0,0,0,0.15)"
                        }}
                        transition={{ duration: 0.5 }}
                      >
                        <IconComponent className="w-8 h-8 text-white" />
                      </motion.div>

                      <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-indigo-700 transition-colors">
                        {item.title}
                      </h3>

                      <p className="text-sm text-gray-600 leading-relaxed mb-4">
                        {item.description}
                      </p>

                      {/* Features */}
                      <div className="space-y-1">
                        {item.features.map((feature, idx) => (
                          <motion.div
                            key={idx}
                            className="flex items-center justify-center text-xs text-gray-500"
                            initial={{ opacity: 0, x: -10 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.3 + idx * 0.1 }}
                          >
                            <CheckCircle className="w-3 h-3 text-indigo-500 mr-2" />
                            {feature}
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* AI-Powered Features Section */}
      <section className="py-32 bg-gradient-to-br from-gray-900 via-indigo-900 to-purple-900 text-white relative overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        <div className="max-w-7xl mx-auto px-4 relative">
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold mb-8 bg-white/10 text-white backdrop-blur-sm border border-white/20"
            >
              <Brain className="w-4 h-4 mr-2" />
              AI-Powered Intelligence
            </motion.div>
            <h2 className="text-5xl lg:text-6xl font-bold mb-6 text-white">
              Smart Campaign Creation
            </h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Let AI optimize your campaigns for maximum performance and ROI
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Features List */}
            <div className="space-y-8">
              {[
                {
                  title: "AI Campaign Optimization",
                  description: "Our AI analyzes your business and target audience to create optimized campaigns that convert 3x better than industry average.",
                  icon: Brain,
                  color: "from-blue-500 to-indigo-600"
                },
                {
                  title: "Smart Audience Targeting",
                  description: "Advanced algorithms identify the perfect customer segments and create personalized messaging for each group.",
                  icon: Target,
                  color: "from-purple-500 to-pink-600"
                },
                {
                  title: "Real-time Performance Tuning",
                  description: "Continuous optimization based on real-time data ensures your campaigns perform at peak efficiency.",
                  icon: Settings,
                  color: "from-emerald-500 to-teal-600"
                },
                {
                  title: "Predictive Analytics",
                  description: "Forecast campaign performance and customer lifetime value before you launch, minimizing risk and maximizing ROI.",
                  icon: PieChart,
                  color: "from-orange-500 to-red-500"
                }
              ].map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <motion.div
                    key={index}
                    className="flex items-start space-x-6 group"
                    initial={{ opacity: 0, x: -30 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.6 }}
                    viewport={{ once: true }}
                    whileHover={{ x: 8 }}
                  >
                    <motion.div 
                      className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center shadow-lg flex-shrink-0`}
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ duration: 0.3 }}
                    >
                      <IconComponent className="w-8 h-8 text-white" />
                    </motion.div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-white mb-3 group-hover:text-blue-300 transition-colors">
                        {feature.title}
                      </h3>
                      <p className="text-white/80 text-lg leading-relaxed">{feature.description}</p>
                    </div>
                  </motion.div>
                );
              })}
            </div>

            {/* AI Dashboard Visualization */}
            <div className="relative">
              <motion.div
                className="relative w-full max-w-lg mx-auto"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 1 }}
                viewport={{ once: true }}
              >
                <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-white">AI Insights</h3>
                    <motion.div
                      className="w-3 h-3 bg-green-400 rounded-full"
                      animate={{ scale: [1, 1.2, 1], opacity: [0.7, 1, 0.7] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  </div>

                  {/* Performance Chart */}
                  <div className="mb-6">
                    <div className="flex items-end justify-between h-32 space-x-2">
                      {[...Array(7)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="bg-gradient-to-t from-blue-500 to-blue-300 rounded-t flex-1"
                          initial={{ height: "20%" }}
                          animate={{ height: `${30 + i * 10}%` }}
                          transition={{ 
                            duration: 1.5, 
                            delay: i * 0.1, 
                            repeat: Infinity, 
                            repeatType: "reverse",
                            repeatDelay: 2
                          }}
                        />
                      ))}
                    </div>
                    <div className="text-white/60 text-sm mt-2">Campaign Performance Over Time</div>
                  </div>

                  {/* AI Recommendations */}
                  <div className="space-y-3">
                    <h4 className="text-lg font-semibold text-white mb-3">AI Recommendations</h4>
                    {[
                      { text: "Increase reward by 15%", impact: "+23% conversion" },
                      { text: "Target evening hours", impact: "+18% engagement" },
                      { text: "Add social proof", impact: "+31% trust" }
                    ].map((rec, i) => (
                      <motion.div
                        key={i}
                        className="flex items-center justify-between p-3 bg-white/5 rounded-lg"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 1 + i * 0.2 }}
                      >
                        <span className="text-white/90 text-sm">{rec.text}</span>
                        <span className="text-green-400 text-xs font-semibold">{rec.impact}</span>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Final CTA */}
      <section className="py-32 bg-gradient-to-br from-indigo-600 via-purple-700 to-pink-600 text-white relative overflow-hidden">
        {/* Dynamic background elements */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-10 w-72 h-72 bg-white rounded-full mix-blend-multiply filter blur-3xl animate-pulse" />
          <div className="absolute bottom-20 right-10 w-72 h-72 bg-yellow-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
          <div className="absolute top-1/2 left-1/2 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse" style={{ animationDelay: '4s' }} />
        </div>

        <div className="max-w-6xl mx-auto px-4 text-center relative">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold mb-8 bg-white/20 text-white backdrop-blur-sm border border-white/30"
            >
              <Rocket className="w-4 h-4 mr-2" />
              Join 15,200+ Growing Businesses
            </motion.div>

            <h2 className="text-6xl lg:text-8xl font-bold mb-8 leading-[0.9]">
              Ready to{' '}
              <motion.span 
                className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                10x
              </motion.span>{' '}
              Your Growth?
            </h2>

            <p className="text-2xl opacity-90 mb-16 max-w-4xl mx-auto leading-relaxed">
              Join thousands of businesses scaling through smart referrals. 
              <span className="text-yellow-200 font-semibold"> Start free, scale fast, pay only for results.</span>
            </p>

            {/* Enhanced CTA buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                className="relative group"
              >
                <div className="absolute -inset-2 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-600 rounded-3xl blur-lg opacity-30 group-hover:opacity-70 transition duration-500 group-hover:duration-200" />
                <Link
                  to="/signup"
                  className="relative inline-flex items-center px-12 py-6 bg-white text-indigo-600 rounded-3xl font-bold hover:bg-gray-100 transition-all duration-300 shadow-2xl hover:shadow-white/30 text-xl group"
                >
                  <span>Start Free Campaign</span>
                  <motion.div
                    className="ml-3"
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <ArrowRight className="w-6 h-6" />
                  </motion.div>
                </Link>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                className="relative group"
              >
                <div className="absolute -inset-2 bg-gradient-to-r from-blue-400 to-purple-600 rounded-3xl blur-lg opacity-30 group-hover:opacity-70 transition duration-500 group-hover:duration-200" />
                <Link
                  to="/contact"
                  className="relative inline-flex items-center px-12 py-6 border-2 border-white/30 text-white rounded-3xl font-bold hover:bg-white/10 transition-all duration-300 text-xl backdrop-blur-sm group"
                >
                  <Play className="mr-3 w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                  <span>Schedule Demo</span>
                </Link>
              </motion.div>
            </div>

            {/* Trust indicators */}
            <motion.div
              className="flex flex-wrap justify-center items-center gap-8 opacity-80 mb-16"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 0.8, y: 0 }}
              transition={{ delay: 0.4 }}
              viewport={{ once: true }}
            >
              {[
                { icon: CheckCircle, text: "No setup fees" },
                { icon: Shield, text: "Enterprise security" },
                { icon: Users, text: "24/7 support" },
                { icon: Zap, text: "5min activation" }
              ].map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <motion.div
                    key={index}
                    className="flex items-center space-x-2 text-white/90 bg-white/10 px-4 py-2 rounded-full backdrop-blur-sm"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ scale: 1.05 }}
                  >
                    <IconComponent className="w-5 h-5" />
                    <span className="text-sm font-medium">{item.text}</span>
                  </motion.div>
                );
              })}
            </motion.div>

            {/* Final metrics */}
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-3 gap-8 pt-12 border-t border-white/20"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              viewport={{ once: true }}
            >
              {[
                { value: "340%", label: "Average ROI increase" },
                { value: "5min", label: "Setup time" },
                { value: "96%", label: "Customer satisfaction" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  className="text-center"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1 + index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="text-4xl font-bold text-white mb-2">{stat.value}</div>
                  <div className="text-white/80 text-sm">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Business;