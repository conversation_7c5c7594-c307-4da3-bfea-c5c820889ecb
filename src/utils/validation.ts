
export class ValidationService {
  static sanitizeInput(input: string): string {
    if (typeof input !== 'string') return '';
    
    return input
      .trim()
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }

  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static validatePhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
  }

  static validateUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  static validateBusinessHours(hours: any): boolean {
    if (!hours || typeof hours !== 'object') return false;
    
    const requiredDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return requiredDays.every(day => 
      hours[day] && 
      typeof hours[day].open === 'string' && 
      typeof hours[day].close === 'string'
    );
  }

  static sanitizeBusinessData(data: any): any {
    if (!data || typeof data !== 'object') return {};

    const sanitized: any = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        sanitized[key] = this.sanitizeInput(value);
      } else if (Array.isArray(value)) {
        sanitized[key] = value.map(item => 
          typeof item === 'string' ? this.sanitizeInput(item) : item
        );
      } else if (value && typeof value === 'object') {
        sanitized[key] = this.sanitizeBusinessData(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }
}
