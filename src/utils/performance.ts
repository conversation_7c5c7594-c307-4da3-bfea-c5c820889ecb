
export class PerformanceMonitor {
  private static marks = new Map<string, number>();

  static startMark(name: string): void {
    this.marks.set(name, performance.now());
  }

  static endMark(name: string): number {
    const startTime = this.marks.get(name);
    if (!startTime) {
      console.warn(`Performance mark "${name}" not found`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.marks.delete(name);

    if (process.env.NODE_ENV === 'development') {
      console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
    }

    return duration;
  }

  static measureComponent<T extends Record<string, any>>(
    componentName: string,
    Component: React.ComponentType<T>
  ): React.ComponentType<T> {
    return (props: T) => {
      React.useEffect(() => {
        this.startMark(`${componentName}-render`);
        
        return () => {
          this.endMark(`${componentName}-render`);
        };
      });

      return React.createElement(Component, props);
    };
  }
}

export function withPerformanceMonitoring<T extends Record<string, any>>(
  componentName: string
) {
  return function<P extends T>(Component: React.ComponentType<P>) {
    return PerformanceMonitor.measureComponent(componentName, Component);
  };
}
