export const APP_CONFIG = {
  API: {
    BASE_URL: import.meta.env.VITE_API_URL, // Use the environment variable
    TIMEOUT: 10000,
    RETRY_ATTEMPTS: 3
  },
  VALIDATION: {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
    MIN_PASSWORD_LENGTH: 8,
    MAX_BUSINESS_NAME_LENGTH: 100,
    MAX_DESCRIPTION_LENGTH: 500
  },
  UI: {
    DEBOUNCE_DELAY: 300,
    TOAST_DURATION: 4000,
    ANIMATION_DURATION: 200
  }
} as const;

export const ERROR_MESSAGES = {
  NETWORK: 'Network error. Please check your connection.',
  VALIDATION: 'Please check your input and try again.',
  AUTH: 'Authentication failed. Please log in again.',
  FILE_TOO_LARGE: 'File size must be less than 10MB.',
  INVALID_FILE_TYPE: 'Please upload a valid image file.',
  GENERIC: 'Something went wrong. Please try again.'
} as const;

export const SUCCESS_MESSAGES = {
  PROFILE_SAVED: 'Business profile saved successfully!',
  FILE_UPLOADED: 'File uploaded successfully!',
  EMAIL_SENT: 'Email sent successfully!'
} as const;