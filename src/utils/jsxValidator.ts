
import React from 'react';

interface JSXValidationError {
  type: 'unclosed_tag' | 'mismatched_tag' | 'invalid_nesting' | 'missing_key';
  message: string;
  line?: number;
  column?: number;
}

export class JSXValidator {
  static validateComponent(Component: React.ComponentType, props: any = {}): JSXValidationError[] {
    const errors: JSXValidationError[] = [];
    
    try {
      // Attempt to render the component
      const element = React.createElement(Component, props);
      
      // Basic structure validation
      if (!React.isValidElement(element)) {
        errors.push({
          type: 'invalid_nesting',
          message: 'Component does not return a valid React element'
        });
      }
      
    } catch (error) {
      if (error instanceof Error) {
        // Parse common JSX errors
        if (error.message.includes('Adjacent JSX elements')) {
          errors.push({
            type: 'invalid_nesting',
            message: 'Adjacent JSX elements must be wrapped in an enclosing tag or React Fragment'
          });
        } else if (error.message.includes('Expected corresponding JSX closing tag')) {
          errors.push({
            type: 'unclosed_tag',
            message: 'JSX tag is not properly closed'
          });
        } else if (error.message.includes('Unexpected token')) {
          errors.push({
            type: 'mismatched_tag',
            message: 'JSX syntax error - check for missing or extra brackets'
          });
        }
      }
    }
    
    return errors;
  }

  static validateJSXString(jsxString: string): JSXValidationError[] {
    const errors: JSXValidationError[] = [];
    
    // Check for common JSX issues
    const lines = jsxString.split('\n');
    
    lines.forEach((line, index) => {
      // Check for unclosed tags
      const openTags = (line.match(/<[^/!][^>]*[^/]>/g) || []).length;
      const closeTags = (line.match(/<\/[^>]+>/g) || []).length;
      const selfClosing = (line.match(/<[^>]+\/>/g) || []).length;
      
      if (openTags - selfClosing !== closeTags && line.trim()) {
        errors.push({
          type: 'unclosed_tag',
          message: `Potential unclosed tag on line ${index + 1}`,
          line: index + 1
        });
      }
      
      // Check for adjacent elements without wrapper
      if (line.match(/>\s*<[^/]/g) && !line.includes('Fragment') && !line.includes('<>')) {
        errors.push({
          type: 'invalid_nesting',
          message: `Adjacent JSX elements should be wrapped on line ${index + 1}`,
          line: index + 1
        });
      }
    });
    
    return errors;
  }
}

// Development-only validation wrapper
export const withJSXValidation = <P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
): React.ComponentType<P> => {
  if (process.env.NODE_ENV === 'production') {
    return Component;
  }
  
  return (props: P) => {
    const errors = JSXValidator.validateComponent(Component, props);
    
    if (errors.length > 0) {
      console.group(`⚠️ JSX Validation Errors in ${componentName}`);
      errors.forEach(error => {
        console.warn(`${error.type}: ${error.message}`);
      });
      console.groupEnd();
    }
    
    return React.createElement(Component, props);
  };
};
