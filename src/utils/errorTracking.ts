
interface ErrorReport {
  message: string;
  stack?: string;
  componentStack?: string;
  timestamp: Date;
  userAgent: string;
  url: string;
  userId?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

class ErrorTracker {
  private static instance: ErrorTracker;
  private errors: ErrorReport[] = [];
  private maxErrors = 100;

  static getInstance(): ErrorTracker {
    if (!ErrorTracker.instance) {
      ErrorTracker.instance = new ErrorTracker();
    }
    return ErrorTracker.instance;
  }

  captureError(error: Error, severity: ErrorReport['severity'] = 'medium', componentStack?: string, userId?: string) {
    const report: ErrorReport = {
      message: error.message,
      stack: error.stack,
      componentStack,
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId,
      severity,
    };

    this.errors.push(report);
    
    // Keep only the most recent errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(-this.maxErrors);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 Error Captured [${severity.toUpperCase()}]`);
      console.error('Message:', error.message);
      console.error('Stack:', error.stack);
      if (componentStack) {
        console.error('Component Stack:', componentStack);
      }
      console.error('Full Report:', report);
      console.groupEnd();
    }

    // In production, you would send this to an error tracking service
    this.sendToErrorService(report);
  }

  private sendToErrorService(report: ErrorReport) {
    // This would typically send to a service like Sentry, LogRocket, etc.
    // For now, we'll just store it locally
    try {
      const existingErrors = JSON.parse(localStorage.getItem('error_reports') || '[]');
      existingErrors.push(report);
      localStorage.setItem('error_reports', JSON.stringify(existingErrors.slice(-50)));
    } catch (e) {
      console.warn('Failed to store error report:', e);
    }
  }

  getRecentErrors(severity?: ErrorReport['severity']): ErrorReport[] {
    if (severity) {
      return this.errors.filter(error => error.severity === severity);
    }
    return [...this.errors];
  }

  clearErrors() {
    this.errors = [];
    localStorage.removeItem('error_reports');
  }
}

export const errorTracker = ErrorTracker.getInstance();

// Global error handler
export const setupGlobalErrorHandling = () => {
  // Catch unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    errorTracker.captureError(
      new Error(event.reason || 'Unhandled promise rejection'),
      'high'
    );
  });

  // Catch global errors
  window.addEventListener('error', (event) => {
    errorTracker.captureError(
      new Error(event.message || 'Global error'),
      'high'
    );
  });
};

// React error boundary helper
export const reportComponentError = (error: Error, errorInfo: { componentStack: string }) => {
  errorTracker.captureError(error, 'critical', errorInfo.componentStack);
};
