// Database simulation using localStorage
// In a real application, this would be replaced with actual database calls

export interface Customer {
  id: string;
  name: string;
  email: string;
  password?: string;
  provider: string;
  referralCode: string;
  totalEarnings: number;
  referrals: string[];
  createdAt: string;
  avatar?: string;
}

export interface Business {
  id: string;
  businessName: string;
  email: string;
  password?: string;
  provider: string;
  industry: string;
  voucherCount: number;
  totalCustomers: number;
  campaignsActive: number;
  createdAt: string;
  avatar?: string;
}

// Generate unique referral code
export const generateReferralCode = (name: string): string => {
  const cleanName = name.replace(/[^a-zA-Z]/g, '').toUpperCase();
  const randomSuffix = Math.random().toString(36).substr(2, 4).toUpperCase();
  return `${cleanName.slice(0, 3)}${randomSuffix}`;
};

// Customer database operations
export const customerDB = {
  create: (userData: any): Customer => {
    const customer: Customer = {
      id: userData.id,
      name: userData.name,
      email: userData.email,
      password: userData.password,
      provider: userData.provider,
      referralCode: generateReferralCode(userData.name),
      totalEarnings: 0,
      referrals: [],
      createdAt: userData.createdAt || new Date().toISOString(),
      avatar: userData.avatar
    };

    // Save to localStorage
    const customers = customerDB.getAll();
    customers.push(customer);
    localStorage.setItem('customers_db', JSON.stringify(customers));

    return customer;
  },

  getAll: (): Customer[] => {
    return JSON.parse(localStorage.getItem('customers_db') || '[]');
  },

  getById: (id: string): Customer | null => {
    const customers = customerDB.getAll();
    return customers.find(c => c.id === id) || null;
  },

  getByEmail: (email: string): Customer | null => {
    const customers = customerDB.getAll();
    return customers.find(c => c.email === email) || null;
  },

  update: (id: string, updates: Partial<Customer>): Customer | null => {
    const customers = customerDB.getAll();
    const index = customers.findIndex(c => c.id === id);

    if (index !== -1) {
      customers[index] = { ...customers[index], ...updates };
      localStorage.setItem('customers_db', JSON.stringify(customers));
      return customers[index];
    }

    return null;
  }
};

// Business database operations
export const businessDB = {
  create: (userData: any): Business => {
    const business: Business = {
      id: userData.id,
      businessName: userData.businessName || userData.name,
      email: userData.email,
      password: userData.password,
      provider: userData.provider,
      industry: userData.industry || 'General',
      voucherCount: 0,
      totalCustomers: 0,
      campaignsActive: 0,
      createdAt: userData.createdAt || new Date().toISOString(),
      avatar: userData.avatar
    };

    // Save to localStorage
    const businesses = businessDB.getAll();
    businesses.push(business);
    localStorage.setItem('businesses_db', JSON.stringify(businesses));

    return business;
  },

  getAll: (): Business[] => {
    return JSON.parse(localStorage.getItem('businesses_db') || '[]');
  },

  getById: (id: string): Business | null => {
    const businesses = businessDB.getAll();
    return businesses.find(b => b.id === id) || null;
  },

  getByEmail: (email: string): Business | null => {
    const businesses = businessDB.getAll();
    return businesses.find(b => b.email === email) || null;
  },

  update: (id: string, updates: Partial<Business>): Business | null => {
    const businesses = businessDB.getAll();
    const index = businesses.findIndex(b => b.id === id);

    if (index !== -1) {
      businesses[index] = { ...businesses[index], ...updates };
      localStorage.setItem('businesses_db', JSON.stringify(businesses));
      return businesses[index];
    }

    return null;
  }
};

// Authentication utilities
export const authUtils = {
  saveUser: (userData: any, accountType: 'customer' | 'business') => {
    if (accountType === 'customer') {
      return customerDB.create(userData);
    } else {
      return businessDB.create(userData);
    }
  },

  authenticateUser: (email: string, password: string): { user: Customer | Business; accountType: 'customer' | 'business' } | null => {
    // Check customers first
    const customer = customerDB.getByEmail(email);
    if (customer && customer.password === password) {
      return { user: customer, accountType: 'customer' };
    }

    // Check businesses
    const business = businessDB.getByEmail(email);
    if (business && business.password === password) {
      return { user: business, accountType: 'business' };
    }

    return null;
  },

  userExists: (email: string): boolean => {
    return !!customerDB.getByEmail(email) || !!businessDB.getByEmail(email);
  }
};