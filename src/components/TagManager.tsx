
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  Plus,
  Search,
  Tag,
  Trash2,
  Edit3,
  Save,
  AlertCircle,
  CheckCircle,
  Users,
  BarChart3,
  Filter,
  Sparkles,
  Target,
  TrendingUp,
  Star,
  Clock,
  Building
} from 'lucide-react';
import { TagService, CustomerTag, TAG_CATEGORIES } from '../services/tagService';

interface TagManagerProps {
  isOpen: boolean;
  onClose: () => void;
  businessId: string;
  onTagsUpdated?: (tags: CustomerTag[]) => void;
}

const TagManager: React.FC<TagManagerProps> = ({ isOpen, onClose, businessId, onTagsUpdated }) => {
  const [tags, setTags] = useState<CustomerTag[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [editingTag, setEditingTag] = useState<CustomerTag | null>(null);
  const [newTagName, setNewTagName] = useState('');
  const [newTagDescription, setNewTagDescription] = useState('');
  const [newTagCategory, setNewTagCategory] = useState<CustomerTag['category']>('custom');
  const [showAddForm, setShowAddForm] = useState(false);
  const [tagUsageStats, setTagUsageStats] = useState<Record<string, number>>({});
  const [deleteConfirmation, setDeleteConfirmation] = useState<CustomerTag | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadTags();
      loadUsageStats();
    }
  }, [isOpen, businessId]);

  const loadTags = () => {
    const businessTags = TagService.getBusinessTags(businessId);
    setTags(businessTags);
  };

  const loadUsageStats = () => {
    const stats = TagService.getTagUsageStats(businessId);
    setTagUsageStats(stats);
  };

  const handleAddTag = async () => {
    if (!newTagName.trim()) return;

    try {
      const newTag = TagService.addTag(businessId, {
        name: newTagName.toLowerCase().replace(/\s+/g, '-'),
        description: newTagDescription,
        category: newTagCategory
      });

      setTags(prev => [...prev, newTag]);
      setNewTagName('');
      setNewTagDescription('');
      setNewTagCategory('custom');
      setShowAddForm(false);
      
      // Reload usage stats
      loadUsageStats();
      
      onTagsUpdated?.(TagService.getBusinessTags(businessId));
      
      console.log('✅ Tag added successfully:', newTag.name);
    } catch (error) {
      console.error('❌ Error adding tag:', error);
      alert('Failed to add tag. Please try again.');
    }
  };

  const handleDeleteTag = async (tag: CustomerTag) => {
    try {
      const result = TagService.removeTag(businessId, tag.id);
      if (result.success) {
        setTags(prev => prev.filter(t => t.id !== tag.id));
        setDeleteConfirmation(null);
        
        // Reload usage stats
        loadUsageStats();
        
        onTagsUpdated?.(TagService.getBusinessTags(businessId));
        
        console.log('✅ Tag deleted successfully:', tag.name);
      } else {
        console.error('❌ Failed to delete tag:', tag.name);
        alert('Failed to delete tag. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error deleting tag:', error);
      alert('Failed to delete tag. Please try again.');
    }
  };

  const handleUpdateTag = (tag: CustomerTag, updates: Partial<CustomerTag>) => {
    const updatedTag = TagService.updateTag(businessId, tag.id, updates);
    if (updatedTag) {
      setTags(prev => prev.map(t => t.id === tag.id ? updatedTag : t));
      setEditingTag(null);
      onTagsUpdated?.(TagService.getBusinessTags(businessId));
    }
  };

  const filteredTags = tags.filter(tag => {
    const matchesSearch = tag.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tag.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || tag.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getCategoryIcon = (category: CustomerTag['category']) => {
    const icons = {
      lifecycle: Clock,
      value: Star,
      behavior: BarChart3,
      engagement: TrendingUp,
      demographics: Users,
      location: Building,
      custom: Tag
    };
    return icons[category] || Tag;
  };

  const getCategoryColor = (category: CustomerTag['category']) => {
    const colors = {
      lifecycle: 'blue',
      value: 'purple',
      behavior: 'green',
      engagement: 'orange',
      demographics: 'pink',
      location: 'teal',
      custom: 'gray'
    };
    return colors[category] || 'gray';
  };

  if (!isOpen) return null;

  return (
    <motion.div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <motion.div
        className="bg-white rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl border border-gray-100"
        initial={{ scale: 0.9, opacity: 0, y: 30 }}
        animate={{ scale: 1, opacity: 1, y: 0 }}
        exit={{ scale: 0.9, opacity: 0, y: 30 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                <Tag className="w-6 h-6" />
              </div>
              <div>
                <h3 className="text-2xl font-bold">Tag Management</h3>
                <p className="text-indigo-100">Organize and segment your customers</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-lg transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="flex flex-col h-[calc(90vh-120px)]">
          {/* Controls */}
          <div className="p-6 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className="relative flex-1 min-w-80">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search tags..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
                
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Categories</option>
                  {TAG_CATEGORIES.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                  <option value="custom">🔧 Custom Tags</option>
                </select>
              </div>

              <motion.button
                onClick={() => setShowAddForm(true)}
                className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 flex items-center shadow-lg"
                whileHover={{ scale: 1.02, y: -1 }}
                whileTap={{ scale: 0.98 }}
              >
                <Plus className="w-5 h-5 mr-2" />
                Add New Tag
              </motion.button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-4 gap-4">
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Tags</p>
                    <p className="text-2xl font-bold text-gray-900">{tags.length}</p>
                  </div>
                  <Tag className="w-8 h-8 text-indigo-500" />
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Default Tags</p>
                    <p className="text-2xl font-bold text-gray-900">{tags.filter(t => t.isDefault).length}</p>
                  </div>
                  <Sparkles className="w-8 h-8 text-purple-500" />
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Custom Tags</p>
                    <p className="text-2xl font-bold text-gray-900">{tags.filter(t => !t.isDefault).length}</p>
                  </div>
                  <Target className="w-8 h-8 text-emerald-500" />
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Most Used</p>
                    <p className="text-lg font-bold text-gray-900">
                      {Object.keys(tagUsageStats).length > 0 
                        ? Object.entries(tagUsageStats).sort(([,a], [,b]) => b - a)[0]?.[0] || 'None'
                        : 'None'
                      }
                    </p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-orange-500" />
                </div>
              </div>
            </div>
          </div>

          {/* Tags List */}
          <div className="flex-1 overflow-y-auto p-6">
            {showAddForm && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="bg-gradient-to-r from-emerald-50 to-teal-50 border border-emerald-200 rounded-2xl p-6 mb-6"
              >
                <h4 className="text-lg font-semibold text-emerald-800 mb-4 flex items-center">
                  <Plus className="w-5 h-5 mr-2" />
                  Create New Tag
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-emerald-700 mb-2">Tag Name</label>
                    <input
                      type="text"
                      value={newTagName}
                      onChange={(e) => setNewTagName(e.target.value)}
                      placeholder="e.g., weekend-shopper"
                      className="w-full px-4 py-3 border border-emerald-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-emerald-700 mb-2">Category</label>
                    <select
                      value={newTagCategory}
                      onChange={(e) => setNewTagCategory(e.target.value as CustomerTag['category'])}
                      className="w-full px-4 py-3 border border-emerald-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="custom">🔧 Custom</option>
                      <option value="lifecycle">🔄 Lifecycle</option>
                      <option value="value">💎 Value</option>
                      <option value="behavior">📊 Behavior</option>
                      <option value="engagement">🚀 Engagement</option>
                      <option value="demographics">👥 Demographics</option>
                      <option value="location">🏢 Location</option>
                    </select>
                  </div>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-emerald-700 mb-2">Description (Optional)</label>
                  <textarea
                    value={newTagDescription}
                    onChange={(e) => setNewTagDescription(e.target.value)}
                    placeholder="Describe what this tag represents..."
                    rows={2}
                    className="w-full px-4 py-3 border border-emerald-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 resize-none"
                  />
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={handleAddTag}
                    disabled={!newTagName.trim()}
                    className="bg-emerald-600 text-white px-6 py-2 rounded-xl font-medium hover:bg-emerald-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Create Tag
                  </button>
                  <button
                    onClick={() => {
                      setShowAddForm(false);
                      setNewTagName('');
                      setNewTagDescription('');
                      setNewTagCategory('custom');
                    }}
                    className="bg-gray-200 text-gray-700 px-6 py-2 rounded-xl font-medium hover:bg-gray-300 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </motion.div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredTags.map((tag, index) => {
                const IconComponent = getCategoryIcon(tag.category);
                const colorClass = getCategoryColor(tag.category);
                const usageCount = tagUsageStats[tag.name] || 0;

                return (
                  <motion.div
                    key={tag.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className={`bg-white border rounded-2xl p-4 hover:shadow-md transition-all duration-300 ${
                      tag.isDefault ? 'border-indigo-200 bg-indigo-50' : 'border-gray-200'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 rounded-xl bg-${colorClass}-100 flex items-center justify-center`}>
                          <IconComponent className={`w-5 h-5 text-${colorClass}-600`} />
                        </div>
                        <div className="flex-1 min-w-0">
                          {editingTag?.id === tag.id ? (
                            <input
                              type="text"
                              value={editingTag.name}
                              onChange={(e) => setEditingTag({...editingTag, name: e.target.value})}
                              className="text-sm font-semibold text-gray-900 bg-transparent border-b border-gray-300 focus:border-indigo-500 focus:outline-none"
                            />
                          ) : (
                            <h4 className="text-sm font-semibold text-gray-900 truncate">
                              {tag.name}
                              {tag.isDefault && (
                                <span className="ml-2 px-2 py-1 bg-indigo-100 text-indigo-600 text-xs rounded-full">
                                  Default
                                </span>
                              )}
                            </h4>
                          )}
                          <p className="text-xs text-gray-500 capitalize">{tag.category}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        {editingTag?.id === tag.id ? (
                          <>
                            <button
                              onClick={() => handleUpdateTag(tag, {
                                name: editingTag.name,
                                description: editingTag.description
                              })}
                              className="p-1 text-emerald-600 hover:text-emerald-700 transition-colors"
                            >
                              <Save className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => setEditingTag(null)}
                              className="p-1 text-gray-600 hover:text-gray-700 transition-colors"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </>
                        ) : (
                          <>
                            <button
                              onClick={() => setEditingTag(tag)}
                              className="p-1 text-gray-600 hover:text-indigo-600 transition-colors"
                            >
                              <Edit3 className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => setDeleteConfirmation(tag)}
                              className="p-1 text-gray-600 hover:text-red-600 transition-colors"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                    
                    {tag.description && (
                      <p className="text-xs text-gray-600 mb-3">{tag.description}</p>
                    )}
                    
                    <div className="flex items-center justify-between text-xs">
                      <div className="flex items-center text-gray-500">
                        <Users className="w-3 h-3 mr-1" />
                        <span>{usageCount} customers</span>
                      </div>
                      <div className="text-gray-400">
                        {tag.createdAt.toLocaleDateString()}
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>

            {filteredTags.length === 0 && (
              <div className="text-center py-12">
                <Tag className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No tags found</h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || selectedCategory !== 'all'
                    ? 'Try adjusting your search or filter criteria'
                    : 'Create your first custom tag to get started'
                  }
                </p>
                <button
                  onClick={() => setShowAddForm(true)}
                  className="bg-indigo-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-indigo-700 transition-colors"
                >
                  Create New Tag
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        <AnimatePresence>
          {deleteConfirmation && (
            <motion.div
              className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className="bg-white rounded-2xl p-6 max-w-md w-full mx-4"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
              >
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                    <AlertCircle className="w-6 h-6 text-red-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Delete Tag</h3>
                    <p className="text-sm text-gray-600">This action cannot be undone</p>
                  </div>
                </div>
                <p className="text-gray-700 mb-6">
                  Are you sure you want to delete the tag <strong>"{deleteConfirmation.name}"</strong>? 
                  This will remove it from all customers and voucher targeting rules.
                </p>
                <div className="flex space-x-3">
                  <button
                    onClick={() => handleDeleteTag(deleteConfirmation)}
                    className="flex-1 bg-red-600 text-white py-2 rounded-xl font-medium hover:bg-red-700 transition-colors"
                  >
                    Delete Tag
                  </button>
                  <button
                    onClick={() => setDeleteConfirmation(null)}
                    className="flex-1 bg-gray-200 text-gray-700 py-2 rounded-xl font-medium hover:bg-gray-300 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
};

export default TagManager;
