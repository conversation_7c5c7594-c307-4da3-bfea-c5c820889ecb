
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Brain, 
  TrendingUp, 
  Target, 
  Users, 
  Clock,
  ArrowRight,
  Lightbulb,
  Zap,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface Insight {
  id: string;
  type: 'opportunity' | 'warning' | 'success' | 'tip';
  title: string;
  description: string;
  impact: string;
  action?: string;
  priority: 'high' | 'medium' | 'low';
}

const SmartInsights: React.FC = () => {
  const [insights] = useState<Insight[]>([
    {
      id: '1',
      type: 'opportunity',
      title: 'Peak Performance Window',
      description: 'Your campaigns perform 40% better between 6-8 PM. Consider scheduling more voucher releases during this time.',
      impact: '+40% conversion rate',
      action: 'Schedule campaigns for evening hours',
      priority: 'high'
    },
    {
      id: '2',
      type: 'warning',
      title: 'Campaign Fatigue Detected',
      description: 'Your "Holiday Special" campaign has seen declining shares over the past 3 days.',
      impact: '-23% engagement',
      action: 'Refresh campaign creative or pause',
      priority: 'medium'
    },
    {
      id: '3',
      type: 'success',
      title: 'Viral Moment Incoming',
      description: 'Your coffee voucher is trending! Share rate increased 300% in the last hour.',
      impact: '+300% viral potential',
      action: 'Boost campaign budget now',
      priority: 'high'
    },
    {
      id: '4',
      type: 'tip',
      title: 'Competitor Analysis',
      description: 'Local cafes are offering 25% discounts. Consider matching or exceeding this offer.',
      impact: 'Stay competitive',
      action: 'Review pricing strategy',
      priority: 'low'
    }
  ]);

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity': return TrendingUp;
      case 'warning': return AlertTriangle;
      case 'success': return CheckCircle;
      case 'tip': return Lightbulb;
      default: return Brain;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'opportunity': return 'from-blue-500 to-indigo-600';
      case 'warning': return 'from-yellow-500 to-orange-600';
      case 'success': return 'from-green-500 to-emerald-600';
      case 'tip': return 'from-purple-500 to-pink-600';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-700 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-700 border-green-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
            <Brain className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">AI Insights</h2>
            <p className="text-gray-600 text-sm">Powered by real-time data analysis</p>
          </div>
        </div>
        <motion.div
          className="w-3 h-3 bg-green-400 rounded-full"
          animate={{ scale: [1, 1.2, 1], opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 2, repeat: Infinity }}
        />
      </div>

      <div className="space-y-4">
        {insights.map((insight, index) => {
          const IconComponent = getInsightIcon(insight.type);
          const colorClass = getInsightColor(insight.type);
          const priorityClass = getPriorityColor(insight.priority);

          return (
            <motion.div
              key={insight.id}
              className="border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-300 group cursor-pointer"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ y: -2 }}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center">
                  <div className={`w-10 h-10 bg-gradient-to-r ${colorClass} rounded-lg flex items-center justify-center mr-3`}>
                    <IconComponent className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors">
                      {insight.title}
                    </h3>
                    <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full border ${priorityClass} mt-1`}>
                      {insight.priority.toUpperCase()} PRIORITY
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">{insight.impact}</div>
                </div>
              </div>

              <p className="text-gray-600 text-sm mb-3 leading-relaxed">{insight.description}</p>

              {insight.action && (
                <div className="flex items-center justify-between">
                  <button className="flex items-center text-indigo-600 hover:text-indigo-700 text-sm font-medium group-hover:translate-x-1 transition-all">
                    <Zap className="w-4 h-4 mr-1" />
                    {insight.action}
                    <ArrowRight className="w-4 h-4 ml-1" />
                  </button>
                  <div className="flex space-x-2">
                    <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
                      <Clock className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              )}
            </motion.div>
          );
        })}
      </div>

      <div className="mt-6 pt-4 border-t border-gray-100">
        <button className="w-full text-center text-indigo-600 hover:text-indigo-700 font-medium text-sm transition-colors">
          View All Insights & Recommendations
        </button>
      </div>
    </div>
  );
};

export default SmartInsights;
