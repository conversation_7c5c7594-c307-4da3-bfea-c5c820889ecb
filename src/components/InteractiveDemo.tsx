import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, Pause, RotateCcw, Users, DollarSign, Share2, Target } from 'lucide-react';

const InteractiveDemo: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  const demoSteps = [
    {
      title: "Business Creates Campaign",
      description: "A local coffee shop sets up a 25% off referral campaign",
      visual: "business-setup",
      metrics: { cost: "$0", setup: "5 mins" }
    },
    {
      title: "Customer Discovers Deal",
      description: "<PERSON> finds the deal and loves the coffee shop",
      visual: "customer-discover",
      metrics: { deals: "1,200+", categories: "15+" }
    },
    {
      title: "Share with <PERSON>",
      description: "<PERSON> shares the voucher with her network",
      visual: "share-process",
      metrics: { platforms: "All social", reach: "Unlimited" }
    },
    {
      title: "Friends Redeem",
      description: "3 friends visit the coffee shop using <PERSON>'s referral",
      visual: "redemption",
      metrics: { conversion: "94%", satisfaction: "98%" }
    },
    {
      title: "Everyone Wins",
      description: "Business gets customers, <PERSON> earns $25, friends save 25%",
      visual: "success",
      metrics: { revenue: "+$127", earned: "$25" }
    }
  ];

  const nextStep = () => {
    setCurrentStep((prev) => (prev + 1) % demoSteps.length);
  };

  const resetDemo = () => {
    setCurrentStep(0);
    setIsPlaying(false);
  };

  React.useEffect(() => {
    if (isPlaying) {
      const timer = setInterval(nextStep, 3000);
      return () => clearInterval(timer);
    }
  }, [isPlaying]);

  const getVisualComponent = (visual: string) => {
    switch (visual) {
      case 'business-setup':
        return (
          <div className="w-full h-48 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-xl flex items-center justify-center relative overflow-hidden">
            <motion.div
              className="absolute inset-0 flex items-center justify-center"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="bg-white rounded-lg shadow-lg p-4 max-w-xs">
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mr-2">
                    <Target className="w-4 h-4 text-white" />
                  </div>
                  <span className="font-semibold">Coffee Campaign</span>
                </div>
                <div className="text-sm text-gray-600">25% off all drinks</div>
                <div className="text-xs text-green-600 mt-1">$5 reward per referral</div>
              </div>
            </motion.div>
          </div>
        );
      case 'customer-discover':
        return (
          <div className="w-full h-48 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-xl flex items-center justify-center relative">
            <motion.div
              className="flex items-center space-x-4"
              initial={{ x: -50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="w-16 h-16 bg-emerald-500 rounded-full flex items-center justify-center">
                <Users className="w-8 h-8 text-white" />
              </div>
              <div className="bg-white rounded-lg p-3 shadow-lg">
                <div className="font-semibold text-sm">Sarah discovers</div>
                <div className="text-xs text-gray-600">Amazing coffee deal!</div>
              </div>
            </motion.div>
          </div>
        );
      case 'share-process':
        return (
          <div className="w-full h-48 bg-gradient-to-br from-purple-100 to-pink-100 rounded-xl flex items-center justify-center relative">
            <motion.div
              className="relative"
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center">
                <Share2 className="w-6 h-6 text-white" />
              </div>
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-8 h-8 bg-white rounded-full border-2 border-purple-300 flex items-center justify-center"
                  style={{
                    top: `${-20 + i * 40}px`,
                    left: `${40 + i * 30}px`
                  }}
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: i * 0.2, duration: 0.3 }}
                >
                  <Users className="w-4 h-4 text-purple-600" />
                </motion.div>
              ))}
            </motion.div>
          </div>
        );
      case 'redemption':
        return (
          <div className="w-full h-48 bg-gradient-to-br from-orange-100 to-red-100 rounded-xl flex items-center justify-center">
            <motion.div
              className="grid grid-cols-3 gap-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={i}
                  className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: i * 0.2, duration: 0.3 }}
                >
                  <span className="text-white font-bold text-xs">✓</span>
                </motion.div>
              ))}
            </motion.div>
          </div>
        );
      case 'success':
        return (
          <div className="w-full h-48 bg-gradient-to-br from-green-100 to-emerald-100 rounded-xl flex items-center justify-center">
            <motion.div
              className="text-center"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <motion.div
                className="text-4xl font-bold text-green-600 mb-2"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
              >
                🎉
              </motion.div>
              <div className="text-lg font-bold text-green-700">Everyone Wins!</div>
              <div className="text-sm text-gray-600">Business + Customer + Friends</div>
            </motion.div>
          </div>
        );
      default:
        return <div className="w-full h-48 bg-gray-100 rounded-xl"></div>;
    }
  };

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            See Referit in Action
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Watch how a simple referral creates value for everyone involved
          </p>

          {/* Demo Controls */}
          <div className="flex justify-center space-x-4 mb-8">
            <motion.button
              onClick={() => setIsPlaying(!isPlaying)}
              className={`flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                isPlaying 
                  ? 'bg-red-500 hover:bg-red-600 text-white' 
                  : 'bg-green-500 hover:bg-green-600 text-white'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {isPlaying ? <Pause className="w-4 h-4 mr-2" /> : <Play className="w-4 h-4 mr-2" />}
              {isPlaying ? 'Pause' : 'Play Demo'}
            </motion.button>

            <motion.button
              onClick={resetDemo}
              className="flex items-center px-6 py-3 bg-gray-500 hover:bg-gray-600 text-white rounded-xl font-semibold transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset
            </motion.button>
          </div>
        </motion.div>

        {/* Demo Content */}
        <div className="bg-white rounded-3xl shadow-2xl p-8 border border-gray-100">
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            {/* Visual */}
            <div className="relative">
              <AnimatePresence mode="wait">
                <motion.div
                  key={`demo-step-${currentStep}`}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.5 }}
                >
                  {getVisualComponent(demoSteps[currentStep].visual)}
                </motion.div>
              </AnimatePresence>

              {/* Step Indicator */}
              <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                {demoSteps.map((_, index) => (
                  <motion.button
                    key={index}
                    onClick={() => setCurrentStep(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentStep ? 'bg-indigo-600' : 'bg-gray-300'
                    }`}
                    whileHover={{ scale: 1.2 }}
                  />
                ))}
              </div>
            </div>

            {/* Content */}
            <div>
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="mb-4">
                    <div className="text-sm font-semibold text-indigo-600 mb-2">
                      Step {currentStep + 1} of {demoSteps.length}
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">
                      {demoSteps[currentStep].title}
                    </h3>
                    <p className="text-gray-600 text-lg leading-relaxed">
                      {demoSteps[currentStep].description}
                    </p>
                  </div>

                  {/* Metrics */}
                  <div className="grid grid-cols-2 gap-4">
                    {Object.entries(demoSteps[currentStep].metrics).map(([key, value]) => (
                      <div key={key} className="bg-gray-50 rounded-lg p-3">
                        <div className="text-sm text-gray-500 capitalize">{key}</div>
                        <div className="text-lg font-bold text-gray-900">{value}</div>
                      </div>
                    ))}
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <motion.button
            className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            Start Your Own Success Story
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default InteractiveDemo;