import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Target,
  Calendar,
  Download,
  Filter,
  Eye,
  Share2,
  Gift,
  Award,
  Clock,
  Percent
} from 'lucide-react';

const AnalyticsDashboard: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [selectedView, setSelectedView] = useState<'overview' | 'campaigns' | 'customers'>('overview');
  const [hasData] = useState(true); // Set to false to show empty state

  // Comprehensive analytics data
  const analyticsData = {
    overview: {
      totalRevenue: 124750,
      totalReferrals: 1847,
      conversionRate: 67.3,
      activeCustomers: 1247,
      growth: {
        revenue: 31.4,
        referrals: 23.7,
        conversions: 8.2,
        customers: 18.5
      }
    },
    campaignPerformance: [
      { 
        name: "Holiday Coffee Special", 
        referrals: 342, 
        conversions: 127, 
        revenue: 3175, 
        roi: 415,
        status: 'active'
      },
      { 
        name: "New Customer Welcome", 
        referrals: 298, 
        conversions: 89, 
        revenue: 2225, 
        roi: 290,
        status: 'active'
      },
      { 
        name: "VIP Member Exclusive", 
        referrals: 156, 
        conversions: 67, 
        revenue: 1675, 
        roi: 380,
        status: 'completed'
      }
    ],
    revenueChart: [
      { month: 'Jul', revenue: 8500, referrals: 145, conversions: 98 },
      { month: 'Aug', revenue: 12300, referrals: 189, conversions: 134 },
      { month: 'Sep', revenue: 15600, referrals: 234, conversions: 167 },
      { month: 'Oct', revenue: 18900, referrals: 287, conversions: 203 },
      { month: 'Nov', revenue: 22400, referrals: 342, conversions: 245 },
      { month: 'Dec', revenue: 28200, referrals: 398, conversions: 289 }
    ],
    topMetrics: {
      avgOrderValue: 87.50,
      customerLifetimeValue: 245.30,
      referralValue: 67.80,
      retentionRate: 73.2
    }
  };

  const formatCurrency = (amount: number) => `$${amount.toLocaleString()}`;
  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  if (!hasData) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
            <p className="text-gray-600 mt-2">Track your campaign performance and business growth</p>
          </div>
        </div>

        <motion.div
          className="bg-white rounded-3xl shadow-lg border border-gray-100 p-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="max-w-lg mx-auto">
            <div className="w-24 h-24 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <BarChart3 className="w-12 h-12 text-blue-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Your Data Story Begins Here</h3>
            <p className="text-gray-600 mb-8 leading-relaxed">
              Launch your first campaign and start collecting valuable data. You'll see comprehensive analytics 
              including revenue tracking, conversion metrics, and customer insights right here.
            </p>
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 mb-8">
              <h4 className="font-semibold text-gray-900 mb-4">Track everything that matters:</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4 text-green-600" />
                  <span>Revenue growth</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Share2 className="w-4 h-4 text-blue-600" />
                  <span>Referral volume</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Target className="w-4 h-4 text-purple-600" />
                  <span>Conversion rates</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-amber-600" />
                  <span>Customer behavior</span>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <motion.button
                onClick={() => window.location.hash = 'campaigns'}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-2xl font-bold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 flex items-center mx-auto shadow-lg hover:shadow-xl"
                whileHover={{ y: -2, scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Target className="w-5 h-5 mr-2" />
                Launch First Campaign
              </motion.button>
              <p className="text-sm text-gray-500">
                Start collecting data and unlock powerful insights
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-2">Track your campaign performance and business growth</p>
        </div>

        <div className="flex items-center space-x-4">
          {/* View Selector */}
          <div className="flex items-center space-x-2 bg-gray-100 rounded-xl p-1">
            {[
              { key: 'overview', label: 'Overview' },
              { key: 'campaigns', label: 'Campaigns' },
              { key: 'customers', label: 'Customers' }
            ].map((view) => (
              <button
                key={view.key}
                onClick={() => setSelectedView(view.key as any)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                  selectedView === view.key
                    ? 'bg-white text-indigo-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {view.label}
              </button>
            ))}
          </div>

          {/* Period Selector */}
          <div className="flex items-center space-x-2 bg-gray-100 rounded-xl p-1">
            {[
              { key: '7d', label: '7D' },
              { key: '30d', label: '30D' },
              { key: '90d', label: '90D' },
              { key: '1y', label: '1Y' }
            ].map((period) => (
              <button
                key={period.key}
                onClick={() => setSelectedPeriod(period.key as any)}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                  selectedPeriod === period.key
                    ? 'bg-white text-indigo-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {period.label}
              </button>
            ))}
          </div>

          <button className="flex items-center px-4 py-2 text-gray-600 border border-gray-300 rounded-xl hover:bg-gray-50">
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Key Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          { 
            icon: DollarSign, 
            title: "Total Revenue", 
            value: formatCurrency(analyticsData.overview.totalRevenue), 
            change: `+${analyticsData.overview.growth.revenue}%`, 
            color: "green",
            subtitle: "from referrals"
          },
          { 
            icon: Share2, 
            title: "Total Referrals", 
            value: analyticsData.overview.totalReferrals.toLocaleString(), 
            change: `+${analyticsData.overview.growth.referrals}%`, 
            color: "blue",
            subtitle: "this period"
          },
          { 
            icon: Target, 
            title: "Conversion Rate", 
            value: formatPercentage(analyticsData.overview.conversionRate), 
            change: `+${analyticsData.overview.growth.conversions}%`, 
            color: "purple",
            subtitle: "referral success"
          },
          { 
            icon: Users, 
            title: "Active Customers", 
            value: analyticsData.overview.activeCustomers.toLocaleString(), 
            change: `+${analyticsData.overview.growth.customers}%`, 
            color: "amber",
            subtitle: "engaging users"
          }
        ].map((metric, index) => {
          const MetricIcon = metric.icon;
          return (
            <motion.div
              key={index}
              className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ y: -2 }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 rounded-xl bg-${metric.color}-100 flex items-center justify-center`}>
                  <MetricIcon className={`w-6 h-6 text-${metric.color}-600`} />
                </div>
                <span className="text-sm text-green-600 font-semibold bg-green-50 px-2 py-1 rounded-lg">
                  {metric.change}
                </span>
              </div>
              <h3 className="text-gray-600 text-sm font-medium mb-1">{metric.title}</h3>
              <p className="text-3xl font-bold text-gray-900 mb-1">{metric.value}</p>
              <p className="text-xs text-gray-500">{metric.subtitle}</p>
            </motion.div>
          );
        })}
      </div>

      {/* Revenue Chart */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-bold text-gray-900">Revenue Growth</h3>
            <p className="text-gray-600 text-sm">Monthly revenue from referral campaigns</p>
          </div>
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-gray-600">Revenue</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-gray-600">Referrals</span>
            </div>
          </div>
        </div>

        {/* Simple Bar Chart */}
        <div className="space-y-4">
          {analyticsData.revenueChart.map((data, index) => {
            const maxRevenue = Math.max(...analyticsData.revenueChart.map(d => d.revenue));
            const revenuePercentage = (data.revenue / maxRevenue) * 100;

            return (
              <div key={index} className="flex items-center space-x-4">
                <div className="text-sm font-medium text-gray-700 w-12">{data.month}</div>
                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Revenue</span>
                    <span>{formatCurrency(data.revenue)}</span>
                  </div>
                  <div className="bg-gray-200 rounded-full h-4 relative overflow-hidden">
                    <motion.div
                      className="h-full bg-gradient-to-r from-green-500 to-emerald-600 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${revenuePercentage}%` }}
                      transition={{ delay: index * 0.1, duration: 0.8 }}
                    />
                  </div>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{data.referrals} referrals</span>
                    <span>{data.conversions} conversions</span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Campaign Performance */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-xl font-bold text-gray-900">Campaign Performance</h3>
          <p className="text-gray-600 text-sm mt-1">Track individual campaign success</p>
        </div>

        <div className="divide-y divide-gray-200">
          {analyticsData.campaignPerformance.map((campaign, index) => (
            <motion.div
              key={index}
              className="p-6 hover:bg-gray-50 transition-colors"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <Target className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 text-lg">{campaign.name}</h4>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                      <span>ROI: {campaign.roi}%</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        campaign.status === 'active' 
                          ? 'bg-green-100 text-green-600' 
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        {campaign.status}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-4 gap-8 text-center">
                  <div>
                    <p className="text-lg font-bold text-blue-600">{campaign.referrals}</p>
                    <p className="text-xs text-gray-500">Referrals</p>
                  </div>
                  <div>
                    <p className="text-lg font-bold text-green-600">{campaign.conversions}</p>
                    <p className="text-xs text-gray-500">Conversions</p>
                  </div>
                  <div>
                    <p className="text-lg font-bold text-purple-600">{formatCurrency(campaign.revenue)}</p>
                    <p className="text-xs text-gray-500">Revenue</p>
                  </div>
                  <div>
                    <p className="text-lg font-bold text-amber-600">{campaign.roi}%</p>
                    <p className="text-xs text-gray-500">ROI</p>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid lg:grid-cols-2 gap-8">
        {/* Key Business Metrics */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-6">Key Business Metrics</h3>

          <div className="space-y-6">
            {[
              { 
                label: "Average Order Value", 
                value: formatCurrency(analyticsData.topMetrics.avgOrderValue), 
                icon: DollarSign, 
                color: "green" 
              },
              { 
                label: "Customer Lifetime Value", 
                value: formatCurrency(analyticsData.topMetrics.customerLifetimeValue), 
                icon: Users, 
                color: "blue" 
              },
              { 
                label: "Referral Value", 
                value: formatCurrency(analyticsData.topMetrics.referralValue), 
                icon: Gift, 
                color: "purple" 
              },
              { 
                label: "Customer Retention", 
                value: formatPercentage(analyticsData.topMetrics.retentionRate), 
                icon: Percent, 
                color: "amber" 
              }
            ].map((metric, index) => {
              const MetricIcon = metric.icon;
              return (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-lg bg-${metric.color}-100 flex items-center justify-center`}>
                      <MetricIcon className={`w-5 h-5 text-${metric.color}-600`} />
                    </div>
                    <span className="font-medium text-gray-900">{metric.label}</span>
                  </div>
                  <span className="text-xl font-bold text-gray-900">{metric.value}</span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-6">Quick Actions</h3>

          <div className="space-y-4">
            {[
              { 
                icon: Target, 
                title: "Create New Campaign", 
                description: "Launch a targeted referral campaign",
                action: () => window.location.hash = 'campaigns'
              },
              { 
                icon: Users, 
                title: "View Customer Insights", 
                description: "Analyze customer behavior and segments",
                action: () => window.location.hash = 'customers'
              },
              { 
                icon: Award, 
                title: "Check Top Promoters", 
                description: "See your best performing referrers",
                action: () => window.location.hash = 'promoters'
              },
              { 
                icon: Download, 
                title: "Export Analytics", 
                description: "Download comprehensive reports",
                action: () => alert('Exporting analytics data...')
              }
            ].map((action, index) => {
              const ActionIcon = action.icon;
              return (
                <motion.button
                  key={index}
                  onClick={action.action}
                  className="w-full text-left p-4 rounded-xl border border-gray-200 hover:border-indigo-300 hover:bg-indigo-50 transition-all group"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                      <ActionIcon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors">
                        {action.title}
                      </h4>
                      <p className="text-sm text-gray-600">{action.description}</p>
                    </div>
                  </div>
                </motion.button>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;