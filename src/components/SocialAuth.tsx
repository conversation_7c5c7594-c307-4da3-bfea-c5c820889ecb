import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Mail, 
  Lock, 
  Eye, 
  EyeOff, 
  Chrome, 
  Github, 
  Apple,
  Loader2,
  CheckCircle,
  AlertCircle,
  User,
  Building2,
  RefreshCw,
  Send
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface SocialAuthProps {
  mode: 'login' | 'signup';
  accountType?: 'customer' | 'business';
  onSuccess?: (provider: string, userData: any) => void;
}

const SocialAuth: React.FC<SocialAuthProps> = ({ mode, accountType, onSuccess }) => {
  const { login, register } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [needsVerification, setNeedsVerification] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState('');
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    companyName: '',
    companySize: '',
    industry: '',
    website: '',
    acceptTerms: false
  });

  const socialProviders = [
    {
      name: 'Google',
      icon: Chrome,
      color: 'hover:bg-red-50 border-red-200 hover:border-red-300 text-red-600',
      bgColor: 'bg-white',
      provider: 'google'
    },
    {
      name: 'Apple',
      icon: Apple,
      color: 'hover:bg-gray-50 border-gray-200 hover:border-gray-300 text-gray-800',
      bgColor: 'bg-white',
      provider: 'apple'
    },
    {
      name: 'Microsoft',
      icon: Mail,
      color: 'hover:bg-blue-50 border-blue-200 hover:border-blue-300 text-blue-600',
      bgColor: 'bg-white',
      provider: 'microsoft'
    }
  ];

  const handleSocialAuth = async (provider: string) => {
    setIsLoading(provider);

    try {
      // Generate realistic user data based on provider
      const userData = {
        id: `${provider}_${Math.random().toString(36).substr(2, 9)}`,
        full_name: provider === 'google' ? 'John Smith' : provider === 'apple' ? 'Jane Doe' : 'Mike Johnson',
        email: `user@${provider === 'microsoft' ? 'outlook.com' : provider === 'apple' ? 'icloud.com' : 'gmail.com'}`,
        avatar_url: `https://ui-avatars.com/api/?name=${encodeURIComponent(provider === 'google' ? 'John Smith' : provider === 'apple' ? 'Jane Doe' : 'Mike Johnson')}&background=6366f1&color=fff`,
        provider,
        user_type: accountType || 'customer',
        created_at: new Date()
      };

      // Enhanced debugging
      console.log('🚀 Social auth starting...');
      console.log('🔍 Account type prop received:', accountType);
      console.log('🔍 Mode:', mode);
      console.log('🔍 Provider:', provider);
      console.log('🔍 User data being sent:', { 
        email: userData.email, 
        full_name: userData.full_name, 
        provider: userData.provider 
      });
      console.log('🎯 Final account type for registration:', accountType || 'customer');

      if (mode === 'signup') {
        console.log('📝 Calling register() with account type:', accountType || 'customer');
        await register(userData, accountType || 'customer');
      } else {
        console.log('🔐 Calling login() with account type:', accountType || 'customer');
        await login(provider, userData, accountType || 'customer');
      }

    } catch (error) {
      console.error(`❌ ${provider} authentication failed:`, error);
      alert(`Failed to sign in with ${provider}. Please try again.`);
    } finally {
      setIsLoading(null);
    }
  };

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading('email');
    setError(null);
    setNeedsVerification(false);

    try {
      // Comprehensive form validation
      if (!formData.email.trim()) {
        throw new Error('Email address is required');
      }

      if (!formData.password.trim()) {
        throw new Error('Password is required');
      }

      // Email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        throw new Error('Please enter a valid email address');
      }

      if (mode === 'signup') {
        if (!formData.fullName.trim()) {
          throw new Error('Full name is required');
        }

        if (formData.password.length < 8) {
          throw new Error('Password must be at least 8 characters long');
        }

        if (!formData.confirmPassword) {
          throw new Error('Please confirm your password');
        }

        if (formData.password !== formData.confirmPassword) {
          throw new Error("Passwords don't match. Please try again.");
        }

        /*if (accountType === 'business' && !formData.companyName.trim()) {
          throw new Error('Company name is required for business accounts');
        }*/

        if (!formData.acceptTerms) {
          throw new Error('You must accept the terms and conditions to continue');
        }

        // Prepare user data for registration
        const userData = {
          full_name: formData.fullName.trim(),
          email: formData.email.trim().toLowerCase(),
          password: formData.password,
          provider: 'email',
          ...(accountType === 'business' && {
            company_name: formData.fullName.trim(), // Use full name as default company name
            company_size: '1-10',
            industry: 'Technology',
            website: ''
          })
        };

        console.log('🔍 Email registration for account type:', accountType);
        console.log('🔍 User data:', {
          email: userData.email,
          full_name: userData.full_name,
          provider: userData.provider,
          hasPassword: !!userData.password
        });
        console.log('🎯 Final account type being used:', accountType);

        await register(userData, accountType || 'customer');
        onSuccess?.('email', userData);
      } else {
        // Login
        const loginResult = await login('email', {
          email: formData.email.trim().toLowerCase(),
          password: formData.password
        }, accountType);

        // Check if verification is needed
        if (loginResult && 'needsVerification' in loginResult && loginResult.needsVerification) {
          setNeedsVerification(true);
          setVerificationEmail(formData.email);
        }

        onSuccess?.('email', { email: formData.email });
      }
    } catch (error: any) {
      console.error('❌ Email auth error:', error);

      // Handle specific error cases for better user experience
      let errorMessage = error.message;

      if (errorMessage.includes('Failed to create') && errorMessage.includes('account')) {
        // This will be handled by the improved database service error messages
        errorMessage = error.message;
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        errorMessage = 'Unable to connect. Please check your internet connection and try again.';
      }

      setError(errorMessage);
    } finally {
      setIsLoading(null);
    }
  };

  const handleForgotPassword = async () => {
    if (!formData.email) {
      alert('Please enter your email address first');
      return;
    }

    try {
      // Check if email exists
      if (authUtils.userExists(formData.email)) {
        // Send password reset email
        const resetToken = Math.random().toString(36).substring(2, 15);
        const resetLink = `${window.location.origin}/reset-password?token=${resetToken}&email=${encodeURIComponent(formData.email)}`;

        const resetSuccess = await emailService.sendPasswordResetEmail({
          email: formData.email,
          resetLink: resetLink
        });

        if (resetSuccess) {
          alert(`Password reset instructions have been sent to ${formData.email}`);
        } else {
          alert('Failed to send reset email. Please try again.');
        }
      } else {
        alert('No account found with this email address');
      }
    } catch (error) {
      console.error('Password reset failed:', error);
      alert('Failed to send reset email. Please try again.');
    }
  };

  const updateFormData = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAuthSuccess = (provider: string, userData: any) => {
    // Update user data with selected account type if needed
    const updatedUserData = {
      ...userData,
      accountType: userData.accountType || accountType || 'customer',
      user_type: userData.user_type || accountType || 'customer'
    };

    // Save to localStorage
    localStorage.setItem('user_data', JSON.stringify(updatedUserData));
    localStorage.setItem('auth_token', `${provider}_${Date.now()}_${userData.id}`);

    console.log(`${mode} successful with ${provider}:`, updatedUserData);

    // Redirect based on account type
    const accountType = updatedUserData.accountType || updatedUserData.user_type;
    if (accountType === 'business') {
      window.location.href = '/dashboard#business-profile';
    } else {
      window.location.href = '/dashboard';
    }

    // Call the success callback
    onSuccess(provider, updatedUserData);
  };

  const handleResendVerification = async () => {
    setIsLoading('verification');
    try {
      // Call your email service to resend the verification email
      const resendSuccess = await emailService.resendVerificationEmail({
        email: verificationEmail
      });

      if (resendSuccess) {
        alert(`Verification email resent to ${verificationEmail}. Please check your inbox.`);
      } else {
        setError('Failed to resend verification email. Please try again.');
      }
    } catch (error) {
      console.error('Resend verification email failed:', error);
      setError('Failed to resend verification email. Please try again.');
    } finally {
      setIsLoading(null);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Social Authentication Buttons */}
      <motion.div
        className="space-y-3 mb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="text-center mb-4">
          <p className="text-sm text-gray-600">
            {mode === 'login' ? 'Sign in with your preferred method' : 'Create account with'}
          </p>
        </div>

        {socialProviders.map((provider, index) => (
          <motion.button
            key={provider.provider}
            onClick={() => handleSocialAuth(provider.provider)}
            disabled={isLoading !== null}
            className={`w-full flex items-center justify-center px-4 py-3 border-2 rounded-xl font-medium transition-all duration-300 ${provider.bgColor} ${provider.color} ${
              isLoading === provider.provider ? 'opacity-75 cursor-not-allowed' : 'hover:shadow-md'
            }`}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1, duration: 0.5 }}
            whileHover={{ scale: isLoading ? 1 : 1.02 }}
            whileTap={{ scale: isLoading ? 1 : 0.98 }}
          >
            {isLoading === provider.provider ? (
              <Loader2 className="w-5 h-5 animate-spin mr-3" />
            ) : (
              <provider.icon className="w-5 h-5 mr-3" />
            )}
            <span>
              {isLoading === provider.provider 
                ? `Connecting to ${provider.name}...` 
                : `Continue with ${provider.name}`
              }
            </span>
          </motion.button>
        ))}
      </motion.div>

      {/* Divider */}
      <motion.div
        className="relative my-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-200"></div>
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-4 bg-gray-50 text-gray-500">Or continue with email</span>
        </div>
      </motion.div>

      {/* Error Display */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            className="mb-4 p-4 bg-red-50 border border-red-200 rounded-xl"
          >
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-red-500 mr-3 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <p className="text-sm font-medium text-red-800">{error}</p>
                {needsVerification && (
                  <button
                    type="button"
                    onClick={handleResendVerification}
                    disabled={isLoading === 'verification'}
                    className="mt-2 inline-flex items-center text-sm text-red-700 hover:text-red-800 font-medium"
                  >
                    {isLoading === 'verification' ? (
                      <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                    ) : (
                      <Send className="w-4 h-4 mr-1" />
                    )}
                    Resend verification email
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Email Form */}
      <motion.form
        onSubmit={handleEmailAuth}
        className="space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.5 }}
      >
        {mode === 'signup' && (
          <div>
            <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-2">
              Full Name
            </label>
            <input
              id="fullName"
              type="text"
              required
              value={formData.fullName}
              onChange={(e) => updateFormData('fullName', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-300"
              placeholder="Enter your full name"
            />
          </div>
        )}

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            Email Address
          </label>
          <input
            id="email"
            type="email"
            required
            value={formData.email}
            onChange={(e) => {
              setFormData({...formData, email: e.target.value});
              setError(null); // Clear error when user types
            }}
            className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-300 ${
              formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)
                ? 'border-red-300 bg-red-50'
                : formData.email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)
                ? 'border-green-300 bg-green-50'
                : 'border-gray-200'
            }`}
            placeholder="Enter your email address"
          />
          {formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email) && (
            <p className="mt-1 text-sm text-red-600">Please enter a valid email address</p>
          )}
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
            Password
          </label>
          <div className="relative">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              required
              value={formData.password}
              onChange={(e) => {
                setFormData({...formData, password: e.target.value});
                setError(null); // Clear error when user types
              }}
              className="w-full px-4 py-3 pr-12 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-300"
              placeholder={mode === 'login' ? 'Enter your password' : 'Create a password'}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {mode === 'signup' && (
          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
              Confirm Password
            </label>
            <input
              id="confirmPassword"
              type="password"
              required
              value={formData.confirmPassword}
              onChange={(e) => {
                setFormData({...formData, confirmPassword: e.target.value});
                setError(null); // Clear error when user types
              }}
              className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-300 ${
                formData.confirmPassword && formData.password && formData.password !== formData.confirmPassword
                  ? 'border-red-300 bg-red-50'
                  : formData.confirmPassword && formData.password && formData.password === formData.confirmPassword
                  ? 'border-green-300 bg-green-50'
                  : 'border-gray-200'
              }`}
              placeholder="Confirm your password"
            />
            {formData.confirmPassword && formData.password && formData.password !== formData.confirmPassword && (
              <p className="mt-1 text-sm text-red-600">Passwords don't match</p>
            )}
            {formData.confirmPassword && formData.password && formData.password === formData.confirmPassword && (
              <p className="mt-1 text-sm text-green-600">Passwords match</p>
            )}
          </div>
        )}

        

        {mode === 'signup' && (
          <div className="flex items-start">
            <input
              id="terms"
              type="checkbox"
              required
              checked={formData.acceptTerms || false}
              onChange={(e) => updateFormData('acceptTerms', e.target.checked)}
              className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label htmlFor="terms" className="ml-3 block text-sm text-gray-700">
              I agree to the{' '}
              <a href="/terms" className="text-indigo-600 hover:text-indigo-500 font-medium">
                Terms of Service
              </a>{' '}
              and{' '}
              <a href="/privacy" className="text-indigo-600 hover:text-indigo-500 font-medium">
                Privacy Policy
              </a>
            </label>
          </div>
        )}

        <motion.button
          type="submit"
          disabled={isLoading !== null}
          className={`w-full flex items-center justify-center px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-medium transition-all duration-300 ${
            isLoading === 'email' ? 'opacity-75 cursor-not-allowed' : 'hover:shadow-lg hover:shadow-indigo-500/25'
          }`}
          whileHover={{ scale: isLoading ? 1 : 1.02 }}
          whileTap={{ scale: isLoading ? 1 : 0.98 }}
        >
          {isLoading === 'email' ? (
            <Loader2 className="w-5 h-5 animate-spin mr-2" />
          ) : (
            <Lock className="w-5 h-5 mr-2" />
          )}
          <span>
            {isLoading === 'email' 
              ? `${mode === 'login' ? 'Signing in' : 'Creating account'}...`
              : mode === 'login' ? 'Sign In' : 'Create Account'
            }
          </span>

        </motion.button>
      </motion.form>

      {/* Additional Options */}
      {mode === 'login' && (
        <motion.div
          className="mt-6 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <button
            type="button"
            onClick={handleForgotPassword}
            className="text-sm text-indigo-600 hover:text-indigo-500 font-medium transition-colors"
          >
            Forgot your password?
          </button>
        </motion.div>
      )}
    </div>
  );
};

export default SocialAuth;