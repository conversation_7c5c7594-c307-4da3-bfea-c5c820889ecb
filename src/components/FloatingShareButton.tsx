
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Share2, X, MessageCircle, Instagram, Facebook, Twitter } from 'lucide-react';
import { SocialPlatformButton } from './MemoizedComponents';

const FloatingShareButton: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  const socialPlatforms = [
    { icon: MessageCircle, name: 'WhatsApp', color: 'bg-green-500' },
    { icon: Instagram, name: 'Instagram', color: 'bg-pink-500' },
    { icon: Facebook, name: 'Facebook', color: 'bg-blue-600' },
    { icon: Twitter, name: 'Twitter', color: 'bg-blue-400' }
  ];

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <AnimatePresence mode="wait">
        {isOpen && (
          <motion.div
            key="share-menu"
            className="absolute bottom-16 right-0 space-y-3"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            transition={{ duration: 0.2 }}
          >
            {socialPlatforms.map((platform, index) => (
              <SocialPlatformButton
                key={platform.name}
                platform={platform}
                index={index}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="w-14 h-14 bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-full shadow-lg hover:shadow-xl transition-shadow flex items-center justify-center"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        animate={{ rotate: isOpen ? 45 : 0 }}
      >
        {isOpen ? <X className="w-6 h-6" /> : <Share2 className="w-6 h-6" />}
      </motion.button>
    </div>
  );
};

export default FloatingShareButton;
