
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Calculator, DollarSign, Users, TrendingUp } from 'lucide-react';

const ValueCalculator: React.FC = () => {
  const [shares, setShares] = useState(10);
  const [avgReward, setAvgReward] = useState(15);
  const [conversionRate, setConversionRate] = useState(30);

  const monthlyEarnings = Math.round((shares * (conversionRate / 100) * avgReward));
  const yearlyEarnings = monthlyEarnings * 12;

  return (
    <section className="py-20 bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center px-4 py-2 bg-indigo-100 text-indigo-700 rounded-full text-sm font-semibold mb-4">
            <Calculator className="w-4 h-4 mr-2" />
            Earnings Calculator
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            Calculate Your Earning Potential
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See how much you could earn by sharing deals with your network
          </p>
        </motion.div>

        <div className="bg-white rounded-3xl shadow-2xl p-8 border border-gray-100">
          <div className="grid lg:grid-cols-2 gap-8">
            {/* Calculator Inputs */}
            <div className="space-y-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Your Activity</h3>
              
              {/* Shares per month */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  How many deals do you share per month?
                </label>
                <div className="relative">
                  <input
                    type="range"
                    min="1"
                    max="50"
                    value={shares}
                    onChange={(e) => setShares(Number(e.target.value))}
                    className="w-full h-3 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div className="flex justify-between text-sm text-gray-500 mt-2">
                    <span>1</span>
                    <span className="font-bold text-indigo-600">{shares} deals</span>
                    <span>50+</span>
                  </div>
                </div>
              </div>

              {/* Average reward */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Average reward per successful referral
                </label>
                <div className="relative">
                  <input
                    type="range"
                    min="5"
                    max="50"
                    value={avgReward}
                    onChange={(e) => setAvgReward(Number(e.target.value))}
                    className="w-full h-3 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div className="flex justify-between text-sm text-gray-500 mt-2">
                    <span>$5</span>
                    <span className="font-bold text-green-600">${avgReward}</span>
                    <span>$50+</span>
                  </div>
                </div>
              </div>

              {/* Conversion rate */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  How engaged is your network? (conversion rate)
                </label>
                <div className="relative">
                  <input
                    type="range"
                    min="10"
                    max="80"
                    value={conversionRate}
                    onChange={(e) => setConversionRate(Number(e.target.value))}
                    className="w-full h-3 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div className="flex justify-between text-sm text-gray-500 mt-2">
                    <span>10%</span>
                    <span className="font-bold text-purple-600">{conversionRate}%</span>
                    <span>80%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Results */}
            <div className="lg:pl-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Your Potential Earnings</h3>
              
              <div className="space-y-6">
                {/* Monthly */}
                <motion.div
                  className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm text-green-600 font-semibold mb-1">Monthly Earnings</div>
                      <motion.div 
                        className="text-4xl font-bold text-green-700"
                        key={monthlyEarnings}
                        initial={{ scale: 1.2, color: "#059669" }}
                        animate={{ scale: 1, color: "#047857" }}
                        transition={{ duration: 0.3 }}
                      >
                        ${monthlyEarnings}
                      </motion.div>
                    </div>
                    <DollarSign className="w-12 h-12 text-green-500" />
                  </div>
                </motion.div>

                {/* Yearly */}
                <motion.div
                  className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm text-blue-600 font-semibold mb-1">Yearly Potential</div>
                      <motion.div 
                        className="text-4xl font-bold text-blue-700"
                        key={yearlyEarnings}
                        initial={{ scale: 1.2, color: "#2563eb" }}
                        animate={{ scale: 1, color: "#1d4ed8" }}
                        transition={{ duration: 0.3 }}
                      >
                        ${yearlyEarnings.toLocaleString()}
                      </motion.div>
                    </div>
                    <TrendingUp className="w-12 h-12 text-blue-500" />
                  </div>
                </motion.div>

                {/* Breakdown */}
                <div className="bg-gray-50 rounded-xl p-4">
                  <div className="text-sm font-semibold text-gray-700 mb-3">Calculation Breakdown</div>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex justify-between">
                      <span>Deals shared per month:</span>
                      <span className="font-semibold">{shares}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Conversion rate:</span>
                      <span className="font-semibold">{conversionRate}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Successful referrals:</span>
                      <span className="font-semibold">{Math.round(shares * (conversionRate / 100))}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Average reward:</span>
                      <span className="font-semibold">${avgReward}</span>
                    </div>
                  </div>
                </div>

                {/* CTA */}
                <motion.button
                  className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white py-4 rounded-xl font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl"
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Start Earning Today
                </motion.button>
              </div>
            </div>
          </div>
        </div>

        {/* Disclaimer */}
        <motion.div
          className="text-center mt-8"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <p className="text-sm text-gray-500 max-w-2xl mx-auto">
            * Earnings estimates are based on average user performance. Actual results may vary based on network engagement, 
            deal quality, and sharing frequency. Most users see results within their first month.
          </p>
        </motion.div>
      </div>
    </section>
  );
};

export default ValueCalculator;
