
import React from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { 
  User, 
  Settings, 
  LogOut, 
  Bell, 
  CreditCard, 
  Share2, 
  Trophy,
  TrendingUp,
  Users,
  DollarSign
} from 'lucide-react';

const UserDashboard: React.FC = () => {
  const { user, logout } = useAuth();

  const stats = [
    {
      icon: Share2,
      label: 'Total Referrals',
      value: '24',
      change: '+12%',
      color: 'text-blue-600 bg-blue-100'
    },
    {
      icon: DollarSign,
      label: 'Earnings',
      value: '$247',
      change: '+18%',
      color: 'text-green-600 bg-green-100'
    },
    {
      icon: Users,
      label: 'Active Connections',
      value: '18',
      change: '+5%',
      color: 'text-purple-600 bg-purple-100'
    },
    {
      icon: Trophy,
      label: 'Rank',
      value: '#127',
      change: '+23',
      color: 'text-orange-600 bg-orange-100'
    }
  ];

  const handleLogout = async () => {
    try {
      await logout();
      // Redirect will be handled by auth state change
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen bg-gray-50 py-8"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <img
                src={user?.avatar}
                alt={user?.name}
                className="w-16 h-16 rounded-2xl object-cover border-2 border-gray-100"
              />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Welcome back, {user?.name?.split(' ')[0]}!
                </h1>
                <p className="text-gray-600">
                  Signed in via {user?.provider.charAt(0).toUpperCase() + user?.provider.slice(1)}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <motion.button
                className="p-2 text-gray-400 hover:text-gray-600 rounded-xl hover:bg-gray-100"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Bell className="w-5 h-5" />
              </motion.button>
              <motion.button
                className="p-2 text-gray-400 hover:text-gray-600 rounded-xl hover:bg-gray-100"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Settings className="w-5 h-5" />
              </motion.button>
              <motion.button
                onClick={handleLogout}
                className="p-2 text-gray-400 hover:text-red-600 rounded-xl hover:bg-red-50"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <LogOut className="w-5 h-5" />
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1 }}
              whileHover={{ y: -2, shadow: '0 10px 40px rgba(0,0,0,0.1)' }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-xl ${stat.color}`}>
                  <stat.icon className="w-6 h-6" />
                </div>
                <span className="text-sm font-medium text-green-600">
                  {stat.change}
                </span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-1">
                {stat.value}
              </h3>
              <p className="text-gray-600 text-sm">{stat.label}</p>
            </motion.div>
          ))}
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          {/* Recent Activity */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Recent Activity
            </h2>
            <div className="space-y-3">
              {[
                { action: 'Shared Starbucks voucher', time: '2 hours ago', reward: '+$5' },
                { action: 'Friend joined via your link', time: '1 day ago', reward: '+$10' },
                { action: 'Cashback earned', time: '3 days ago', reward: '+$3.50' }
              ].map((activity, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50">
                  <div>
                    <p className="font-medium text-gray-900">{activity.action}</p>
                    <p className="text-sm text-gray-600">{activity.time}</p>
                  </div>
                  <span className="font-semibold text-green-600">{activity.reward}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Account Overview */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Account Overview
            </h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Email</span>
                <span className="font-medium text-gray-900">{user?.email}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Member since</span>
                <span className="font-medium text-gray-900">
                  {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Today'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Authentication</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  {user?.provider}
                </span>
              </div>
              <motion.button
                className="w-full mt-4 px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-medium hover:shadow-lg transition-all duration-300"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <CreditCard className="w-4 h-4 inline mr-2" />
                Upgrade to Premium
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default UserDashboard;
