
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Building,
  MapPin,
  Phone,
  ArrowRight,
  <PERSON>rkles,
  Clock,
  CheckCircle
} from 'lucide-react';

interface QuickBusinessSetupProps {
  onComplete: (businessData: any) => void;
  onSkip?: () => void;
  context?: 'campaign' | 'voucher' | 'general';
}

const QuickBusinessSetup: React.FC<QuickBusinessSetupProps> = ({
  onComplete,
  onSkip,
  context = 'general'
}) => {
  const [businessData, setBusinessData] = useState({
    businessName: '',
    address: '',
    phone: ''
  });

  const handleSubmit = () => {
    if (businessData.businessName && businessData.address) {
      onComplete({
        ...businessData,
        locations: [{
          id: 'main',
          name: 'Main Location',
          address: businessData.address,
          city: '',
          state: '',
          zipCode: '',
          phone: businessData.phone,
          operatingHours: {
            monday: { open: '09:00', close: '17:00', closed: false },
            tuesday: { open: '09:00', close: '17:00', closed: false },
            wednesday: { open: '09:00', close: '17:00', closed: false },
            thursday: { open: '09:00', close: '17:00', closed: false },
            friday: { open: '09:00', close: '17:00', closed: false },
            saturday: { open: '10:00', close: '16:00', closed: false },
            sunday: { open: '10:00', close: '16:00', closed: true }
          }
        }]
      });
    }
  };

  const getContextMessage = () => {
    switch (context) {
      case 'campaign':
        return {
          title: "Let's set up your first location! 🏪",
          subtitle: "We need a business location to launch your campaign",
          cta: "Create Campaign"
        };
      case 'voucher':
        return {
          title: "Quick setup to publish your voucher! ⚡",
          subtitle: "Just need your business details to make your voucher live",
          cta: "Publish Voucher"
        };
      default:
        return {
          title: "Welcome! Let's get you started 🚀",
          subtitle: "Quick 30-second setup to unlock all features",
          cta: "Get Started"
        };
    }
  };

  const { title, subtitle, cta } = getContextMessage();

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="bg-white rounded-3xl shadow-2xl p-8 max-w-lg w-full"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        transition={{ duration: 0.6, type: "spring" }}
      >
        {/* Header */}
        <div className="text-center mb-8">
          <motion.div
            className="w-20 h-20 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4"
            animate={{ 
              boxShadow: [
                "0 0 0 0 rgba(99, 102, 241, 0)",
                "0 0 0 10px rgba(99, 102, 241, 0.1)",
                "0 0 0 0 rgba(99, 102, 241, 0)"
              ]
            }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <Building className="w-10 h-10 text-white" />
          </motion.div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">{title}</h2>
          <p className="text-gray-600 text-lg">{subtitle}</p>
        </div>

        {/* Quick benefits */}
        <div className="flex items-center justify-center space-x-6 mb-8">
          <div className="flex items-center text-sm text-gray-600">
            <Clock className="w-4 h-4 mr-2 text-green-500" />
            30 seconds
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <Sparkles className="w-4 h-4 mr-2 text-purple-500" />
            No commitment
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <CheckCircle className="w-4 h-4 mr-2 text-blue-500" />
            Edit anytime
          </div>
        </div>

        {/* Form */}
        <div className="space-y-6 mb-8">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Business Name *
            </label>
            <input
              type="text"
              value={businessData.businessName}
              onChange={(e) => setBusinessData(prev => ({ ...prev, businessName: e.target.value }))}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
              placeholder="e.g., Joe's Coffee Shop"
              autoFocus
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Business Address *
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
              <input
                type="text"
                value={businessData.address}
                onChange={(e) => setBusinessData(prev => ({ ...prev, address: e.target.value }))}
                className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                placeholder="123 Main St, City, State"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number (optional)
            </label>
            <div className="relative">
              <Phone className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
              <input
                type="tel"
                value={businessData.phone}
                onChange={(e) => setBusinessData(prev => ({ ...prev, phone: e.target.value }))}
                className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                placeholder="+****************"
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="space-y-3">
          <motion.button
            onClick={handleSubmit}
            disabled={!businessData.businessName || !businessData.address}
            className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-4 rounded-xl font-semibold text-lg hover:from-indigo-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 flex items-center justify-center"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {cta}
            <ArrowRight className="w-5 h-5 ml-2" />
          </motion.button>
          
          {onSkip && (
            <button
              onClick={onSkip}
              className="w-full text-gray-600 py-2 font-medium hover:text-gray-800 transition-colors"
            >
              Skip for now
            </button>
          )}
        </div>

        {/* Footer note */}
        <p className="text-xs text-gray-500 text-center mt-6">
          Don't worry - you can add more locations and update details anytime in your dashboard
        </p>
      </motion.div>
    </motion.div>
  );
};

export default QuickBusinessSetup;
