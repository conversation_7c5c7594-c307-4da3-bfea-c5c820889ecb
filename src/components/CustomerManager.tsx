import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import CustomerInsights from './CustomerInsights';
import SmartCustomerRecommendations from './SmartCustomerRecommendations';
import TagManager from './TagManager';
import { 
  Users, 
  UserPlus, 
  Search, 
  Filter, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Mail, 
  Phone, 
  MapPin,
  Download,
  Upload,
  Save,
  X,
  Plus,
  AlertCircle,
  Tag,
  CheckCircle,
  TrendingUp,
  DollarSign,
  Gift,
  BarChart3,
  Calendar,
  Clock,
  Zap,
  Target,
  Share2,
  MessageSquare,
  Bell,
  Settings
} from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  totalReferrals: number;
  totalEarnings: number;
  lastActivity: string;
  status: 'active' | 'inactive';
  joinedDate: string;
  avatar?: string;
  tags: string[];
  customFields?: Record<string, any>;
}

const CustomerManager: React.FC = () => {
  // Get business ID from auth context or use mock for demo
  const [businessId, setBusinessId] = useState<string>('business-123');

  // In a real app, this would come from your auth context
  useEffect(() => {
    // For now, generate a consistent business ID based on session
    const sessionBusinessId = sessionStorage.getItem('demo-business-id');
    if (sessionBusinessId) {
      setBusinessId(sessionBusinessId);
    } else {
      const newBusinessId = `business-${Date.now()}`;
      sessionStorage.setItem('demo-business-id', newBusinessId);
      setBusinessId(newBusinessId);
    }
  }, []);

  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch customers from API
  const fetchCustomers = async () => {
    try {
      setLoading(true);
      setError(null); // Clear any previous errors
      console.log('🔍 Fetching customers for business:', businessId);

      const response = await fetch(`/api/businesses/${businessId}/customers`);
      console.log('📡 API Response status:', response.status);

      if (!response.ok) {
        console.error('❌ API request failed:', response.status, response.statusText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('📊 API Response data:', data);

      // Always expect success: true from our updated API
      if (data.success) {
        // Handle both empty arrays and populated arrays successfully
        const customerData = data.customers || [];
        
        if (customerData.length > 0) {
          // Map API response to component interface
          const mappedCustomers = customerData.map((customer: any) => ({
            id: customer.id,
            name: customer.full_name,
            email: customer.email,
            phone: customer.phone || '',
            totalReferrals: customer.total_referrals || 0,
            totalEarnings: customer.total_earnings || 0,
            lastActivity: customer.updated_at ? new Date(customer.updated_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
            status: customer.email_verified ? 'active' : 'inactive',
            joinedDate: customer.created_at ? new Date(customer.created_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
            avatar: customer.avatar_url || '/images/default-avatar.jpg',
            tags: (customer.preferences?.tags && Array.isArray(customer.preferences.tags)) 
              ? customer.preferences.tags 
              : ['customer'], // Default tag
            customFields: customer.preferences || {}
          }));
          setCustomers(mappedCustomers);
          console.log('✅ Successfully loaded', mappedCustomers.length, 'customers for business:', businessId);
        } else {
          // No customers found - show empty state (this is normal, not an error)
          setCustomers([]);
          console.log('📝 No customers found for business:', businessId, '- showing empty state');
        }
      } else {
        // API returned success: false
        console.error('❌ API returned success: false:', data.message);
        setError(data.message || 'Failed to load customers. Please try again.');
        setCustomers([]);
      }
    } catch (error) {
      console.error('❌ Error fetching customers:', error);

      // More specific error handling
      if (error instanceof TypeError && error.message.includes('fetch')) {
        setError('Unable to connect to the server. Please check your internet connection and try again.');
      } else if (error.name === 'AbortError') {
        setError('Request timeout. Please try again.');
      } else {
        setError('Failed to load customers. Please check your connection and try again.');
      }

      // Show empty state on error instead of demo data
      setCustomers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCustomers();
    initializeBusinessTags();
  }, []);

  const initializeBusinessTags = () => {
    // Initialize business tags from TagService
    try {
      const { TagService } = require('../services/tagService');
      const tags = TagService.getBusinessTags(businessId);
      const tagNames = tags.map((tag: any) => tag.name);
      setAvailableTags(tagNames);
      console.log('✅ Initialized business tags:', tagNames);
    } catch (error) {
      console.error('❌ Error initializing business tags:', error);
      // Fallback to default tags if TagService fails
      setAvailableTags([
        'vip',
        'frequent-buyer', 
        'new-customer',
        'loyalty-member',
        'corporate',
        'referral-source',
        'high-value',
        'social-media'
      ]);
    }
  };

  const [searchTerm, setSearchTerm] = useState('');
  const [showImportModal, setShowImportModal] = useState(false);
  const [showTagManager, setShowTagManager] = useState(false);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [showInsights, setShowInsights] = useState(false);
  const [showEngagementModal, setShowEngagementModal] = useState(false);
  const [selectedCustomerForEngagement, setSelectedCustomerForEngagement] = useState<Customer | null>(null);
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [availableTags, setAvailableTags] = useState([
    'vip',
    'frequent-buyer',
    'new-customer',
    'loyalty-member',
    'corporate',
    'referral-source',
    'high-value',
    'social-media'
  ]);

  // Enhanced validation functions
  const validateEmail = (email: string): { isValid: boolean; error: string } => {
    if (!email.trim()) return { isValid: true, error: '' }; // Optional field
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(email);
    return {
      isValid,
      error: isValid ? '' : 'Please enter a valid email address (e.g., <EMAIL>)'
    };
  };

  const validatePhone = (phone: string): { isValid: boolean; error: string } => {
    if (!phone.trim()) return { isValid: true, error: '' }; // Optional field
    const phoneNumber = phone.replace(/\D/g, '');
    const isValid = phoneNumber.length >= 10;
    return {
      isValid,
      error: isValid ? '' : 'Phone number must be at least 10 digits'
    };
  };

  const validateZipCode = (zipCode: string, country: string): { isValid: boolean; error: string } => {
    if (!zipCode.trim()) return { isValid: true, error: '' }; // Optional field

    let isValid = false;
    let errorMessage = '';

    switch (country) {
      case 'United States':
        isValid = /^\d{5}$/.test(zipCode);
        errorMessage = isValid ? '' : 'Please enter exactly 5 numerical digits (e.g., 12345)';
        break;
      case 'Canada':
        isValid = /^[A-Za-z]\d[A-Za-z] ?\d[A-Za-z]\d$/.test(zipCode);
        errorMessage = isValid ? '' : 'Please enter a valid postal code (e.g., A1A 1A1)';
        break;
      case 'United Kingdom':
        isValid = /^[A-Za-z]{1,2}\d[A-Za-z\d]? ?\d[A-Za-z]{2}$/.test(zipCode);
        errorMessage = isValid ? '' : 'Please enter a valid postcode (e.g., SW1A 1AA)';
        break;
      default:
        isValid = zipCode.length >= 3; // Generic validation
        errorMessage = isValid ? '' : 'Please enter a valid postal code';
    }

    return {
      isValid,
      error: errorMessage
    };
  };

  const getZipCodeLabel = (country: string): string => {
    switch (country) {
      case 'United States': return 'Zip Code';
      case 'Canada': return 'Postal Code';
      case 'United Kingdom': return 'Postcode';
      default: return 'Postal Code';
    }
  };

  const getZipCodePlaceholder = (country: string): string => {
    switch (country) {
      case 'United States': return '12345';
      case 'Canada': return 'A1A 1A1';
      case 'United Kingdom': return 'SW1A 1AA';
      default: return 'Enter postal code';
    }
  };

  // State/Province options for different countries
  const getStateOptions = (country: string): Array<{ value: string; label: string }> => {
    switch (country) {
      case 'United States':
        return [
          { value: 'AL', label: 'Alabama' },
          { value: 'AK', label: 'Alaska' },
          { value: 'AZ', label: 'Arizona' },
          { value: 'AR', label: 'Arkansas' },
          { value: 'CA', label: 'California' },
          { value: 'CO', label: 'Colorado' },
          { value: 'CT', label: 'Connecticut' },
          { value: 'DE', label: 'Delaware' },
          { value: 'FL', label: 'Florida' },
          { value: 'GA', label: 'Georgia' },
          { value: 'HI', label: 'Hawaii' },
          { value: 'ID', label: 'Idaho' },
          { value: 'IL', label: 'Illinois' },
          { value: 'IN', label: 'Indiana' },
          { value: 'IA', label: 'Iowa' },
          { value: 'KS', label: 'Kansas' },
          { value: 'KY', label: 'Kentucky' },
          { value: 'LA', label: 'Louisiana' },
          { value: 'ME', label: 'Maine' },
          { value: 'MD', label: 'Maryland' },
          { value: 'MA', label: 'Massachusetts' },
          { value: 'MI', label: 'Michigan' },
          { value: 'MN', label: 'Minnesota' },
          { value: 'MS', label: 'Mississippi' },
          { value: 'MO', label: 'Missouri' },
          { value: 'MT', label: 'Montana' },
          { value: 'NE', label: 'Nebraska' },
          { value: 'NV', label: 'Nevada' },
          { value: 'NH', label: 'New Hampshire' },
          { value: 'NJ', label: 'New Jersey' },
          { value: 'NM', label: 'New Mexico' },
          { value: 'NY', label: 'New York' },
          { value: 'NC', label: 'North Carolina' },
          { value: 'ND', label: 'North Dakota' },
          { value: 'OH', label: 'Ohio' },
          { value: 'OK', label: 'Oklahoma' },
          { value: 'OR', label: 'Oregon' },
          { value: 'PA', label: 'Pennsylvania' },
          { value: 'RI', label: 'Rhode Island' },
          { value: 'SC', label: 'South Carolina' },
          { value: 'SD', label: 'South Dakota' },
          { value: 'TN', label: 'Tennessee' },
          { value: 'TX', label: 'Texas' },
          { value: 'UT', label: 'Utah' },
          { value: 'VT', label: 'Vermont' },
          { value: 'VA', label: 'Virginia' },
          { value: 'WA', label: 'Washington' },
          { value: 'WV', label: 'West Virginia' },
          { value: 'WI', label: 'Wisconsin' },
          { value: 'WY', label: 'Wyoming' },
          { value: 'DC', label: 'District of Columbia' }
        ];
      case 'Canada':
        return [
          { value: 'AB', label: 'Alberta' },
          { value: 'BC', label: 'British Columbia' },
          { value: 'MB', label: 'Manitoba' },
          { value: 'NB', label: 'New Brunswick' },
          { value: 'NL', label: 'Newfoundland and Labrador' },
          { value: 'NS', label: 'Nova Scotia' },
          { value: 'ON', label: 'Ontario' },
          { value: 'PE', label: 'Prince Edward Island' },
          { value: 'QC', label: 'Quebec' },
          { value: 'SK', label: 'Saskatchewan' },
          { value: 'NT', label: 'Northwest Territories' },
          { value: 'NU', label: 'Nunavut' },
          { value: 'YT', label: 'Yukon' }
        ];
      case 'United Kingdom':
        return [
          { value: 'ENG', label: 'England' },
          { value: 'SCT', label: 'Scotland' },
          { value: 'WLS', label: 'Wales' },
          { value: 'NIR', label: 'Northern Ireland' }
        ];
      case 'Australia':
        return [
          { value: 'NSW', label: 'New South Wales' },
          { value: 'VIC', label: 'Victoria' },
          { value: 'QLD', label: 'Queensland' },
          { value: 'WA', label: 'Western Australia' },
          { value: 'SA', label: 'South Australia' },
          { value: 'TAS', label: 'Tasmania' },
          { value: 'ACT', label: 'Australian Capital Territory' },
          { value: 'NT', label: 'Northern Territory' }
        ];
      default:
        return [];
    }
  };

  // Auto-populate state from zip code for US addresses
  const autoPopulateStateFromZip = async (zipCode: string, country: string) => {
    if (country !== 'United States' || !zipCode || zipCode.length < 5) return;

    // Basic zip code to state mapping for major cities/regions
    const zipToStateMap: Record<string, string> = {
      // New York
      '100': 'NY', '101': 'NY', '102': 'NY', '103': 'NY', '104': 'NY',
      // California
      '900': 'CA', '901': 'CA', '902': 'CA', '903': 'CA', '904': 'CA', '905': 'CA',
      '906': 'CA', '907': 'CA', '908': 'CA', '910': 'CA', '911': 'CA', '912': 'CA',
      '913': 'CA', '914': 'CA', '915': 'CA', '916': 'CA', '917': 'CA', '918': 'CA',
      '919': 'CA', '920': 'CA', '921': 'CA', '922': 'CA', '923': 'CA', '924': 'CA',
      '925': 'CA', '926': 'CA', '927': 'CA', '928': 'CA', '930': 'CA', '931': 'CA',
      '932': 'CA', '933': 'CA', '934': 'CA', '935': 'CA', '936': 'CA', '937': 'CA',
      '938': 'CA', '939': 'CA', '940': 'CA', '941': 'CA', '942': 'CA', '943': 'CA',
      '944': 'CA', '945': 'CA', '946': 'CA', '947': 'CA', '948': 'CA', '949': 'CA',
      '950': 'CA', '951': 'CA', '952': 'CA', '953': 'CA', '954': 'CA', '955': 'CA',
      '956': 'CA', '957': 'CA', '958': 'CA', '959': 'CA', '960': 'CA', '961': 'CA',
      // Texas
      '750': 'TX', '751': 'TX', '752': 'TX', '753': 'TX', '754': 'TX', '755': 'TX',
      '756': 'TX', '757': 'TX', '758': 'TX', '759': 'TX', '760': 'TX', '761': 'TX',
      '762': 'TX', '763': 'TX', '764': 'TX', '765': 'TX', '766': 'TX', '767': 'TX',
      '768': 'TX', '769': 'TX', '770': 'TX', '771': 'TX', '772': 'TX', '773': 'TX',
      '774': 'TX', '775': 'TX', '776': 'TX', '777': 'TX', '778': 'TX', '779': 'TX',
      '780': 'TX', '781': 'TX', '782': 'TX', '783': 'TX', '784': 'TX', '785': 'TX',
      '786': 'TX', '787': 'TX', '788': 'TX', '789': 'TX', '790': 'TX', '791': 'TX',
      '792': 'TX', '793': 'TX', '794': 'TX', '795': 'TX', '796': 'TX', '797': 'TX',
      '798': 'TX', '799': 'TX',
      // Florida
      '320': 'FL', '321': 'FL', '322': 'FL', '323': 'FL', '324': 'FL', '325': 'FL',
      '326': 'FL', '327': 'FL', '328': 'FL', '329': 'FL', '330': 'FL', '331': 'FL',
      '332': 'FL', '333': 'FL', '334': 'FL', '335': 'FL', '336': 'FL', '337': 'FL',
      '338': 'FL', '339': 'FL', '340': 'FL', '341': 'FL', '342': 'FL', '343': 'FL',
      '344': 'FL', '345': 'FL', '346': 'FL', '347': 'FL',
      // Illinois
      '600': 'IL', '601': 'IL', '602': 'IL', '603': 'IL', '604': 'IL', '605': 'IL',
      '606': 'IL', '607': 'IL', '608': 'IL', '609': 'IL', '610': 'IL', '611': 'IL',
      '612': 'IL', '613': 'IL', '614': 'IL', '615': 'IL', '616': 'IL', '617': 'IL',
      '618': 'IL', '619': 'IL', '620': 'IL', '621': 'IL', '622': 'IL', '623': 'IL',
      '624': 'IL', '625': 'IL', '626': 'IL', '627': 'IL', '628': 'IL', '629': 'IL',
      // Pennsylvania
      '150': 'PA', '151': 'PA', '152': 'PA', '153': 'PA', '154': 'PA', '155': 'PA',
      '156': 'PA', '157': 'PA', '158': 'PA', '159': 'PA', '160': 'PA', '161': 'PA',
      '162': 'PA', '163': 'PA', '164': 'PA', '165': 'PA', '166': 'PA', '167': 'PA',
      '168': 'PA', '169': 'PA', '170': 'PA', '171': 'PA', '172': 'PA', '173': 'PA',
      '174': 'PA', '175': 'PA', '176': 'PA', '177': 'PA', '178': 'PA', '179': 'PA',
      '180': 'PA', '181': 'PA', '182': 'PA', '183': 'PA', '184': 'PA', '185': 'PA',
      '186': 'PA', '187': 'PA', '188': 'PA', '189': 'PA', '190': 'PA', '191': 'PA',
      '192': 'PA', '193': 'PA', '194': 'PA', '195': 'PA', '196': 'PA'
    };

    const zipPrefix = zipCode.substring(0, 3);
    const detectedState = zipToStateMap[zipPrefix];

    if (detectedState && detectedState !== newCustomer.state) {
      setNewCustomer(prev => ({ ...prev, state: detectedState }));
    }
  };

  const getStateLabel = (country: string): string => {
    switch (country) {
      case 'United States': return 'State';
      case 'Canada': return 'Province';
      case 'United Kingdom': return 'Country';
      case 'Australia': return 'State';
      default: return 'State/Region';
    }
  };

  const formatPhoneNumber = (value: string) => {
    const phoneNumber = value.replace(/\D/g, '');
    if (phoneNumber.length >= 6) {
      return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
    } else if (phoneNumber.length >= 3) {
      return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3)}`;
    }
    return phoneNumber;
  };

  const formatZipCode = (value: string, country: string): string => {
    if (country === 'Canada') {
      const cleaned = value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
      if (cleaned.length >= 3) {
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)}`;
      }
      return cleaned;
    } else if (country === 'United States') {
      // Only allow numerical digits and limit to 5 characters
      const cleaned = value.replace(/[^0-9]/g, '');
      return cleaned.slice(0, 5);
    }
    return value;
  };

  const validateField = (field: string, value: string, country?: string): { isValid: boolean; error: string } => {
    switch (field) {
      case 'fullName':
        const isValidName = value.trim().length > 0;
        return {
          isValid: isValidName,
          error: isValidName ? '' : 'Full name is required'
        };
      case 'phone':
        return validatePhone(value);
      case 'email':
        return validateEmail(value);
      case 'zipCode':
        return validateZipCode(value, country || 'United States');
      case 'address':
        // Check if any other address fields are filled - if so, address becomes required
        const hasOtherAddressData = 
          newCustomer.addressLine2.trim() || 
          newCustomer.city.trim() || 
          newCustomer.state.trim() || 
          newCustomer.zipCode.trim();

        if (hasOtherAddressData && !value.trim()) {
          return { isValid: false, error: 'Street address is required when providing address information' };
        }
        return { isValid: true, error: '' };
      case 'state':
        // Check if any other address fields are filled - if so, state becomes required
        const hasOtherAddressDataForState = 
          newCustomer.address.trim() || 
          newCustomer.addressLine2.trim() || 
          newCustomer.city.trim() || 
          newCustomer.zipCode.trim();

        if (hasOtherAddressDataForState && !value.trim()) {
          return { isValid: false, error: 'State is required when providing address information' };
        }
        return { isValid: true, error: '' };
      default:
        return { isValid: true, error: '' };
    }
  };
  const [newTag, setNewTag] = useState('');
  const [filterByTags, setFilterByTags] = useState<string[]>([]);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [deletingCustomer, setDeletingCustomer] = useState<Customer | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [newCustomer, setNewCustomer] = useState({
    fullName: '',
    phone: '',
    email: '',
    address: '',
    addressLine2: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'United States',
    tags: [] as string[]
  });
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [fieldTouched, setFieldTouched] = useState<Record<string, boolean>>({});

  const handleFieldChange = (field: string, value: string) => {
    let processedValue = value;

    // Format specific fields
    if (field === 'zipCode') {
      processedValue = formatZipCode(value, newCustomer.country);
      // Auto-populate state from zip code
      if (processedValue.length >= 5) {
        autoPopulateStateFromZip(processedValue, newCustomer.country);
      }
    }

    setNewCustomer(prev => ({ ...prev, [field]: processedValue }));
    setFieldTouched(prev => ({ ...prev, [field]: true }));

    // Real-time validation
    if (fieldTouched[field] || validationErrors[field]) {
      const validation = validateField(field, processedValue, newCustomer.country);
      setValidationErrors(prev => ({
        ...prev,
        [field]: validation.error
      }));
    }
  };

  // Handle auto-complete detection for zip code
  const handleZipCodeInput = (e: React.FormEvent<HTMLInputElement>) => {
    const value = e.currentTarget.value;
    const processedValue = formatZipCode(value, newCustomer.country);

    // Check if value changed (auto-complete detection)
    if (processedValue !== newCustomer.zipCode && processedValue.length >= 5) {
      autoPopulateStateFromZip(processedValue, newCustomer.country);
    }

    handleFieldChange('zipCode', value);
  };

  const handleFieldBlur = (field: string, value: string) => {
    setFieldTouched(prev => ({ ...prev, [field]: true }));
    const validation = validateField(field, value, newCustomer.country);
    setValidationErrors(prev => ({
      ...prev,
      [field]: validation.error
    }));
  };

  // Check if form is valid for submission
  const isFormValid = () => {
    // Check required field: full name
    if (!newCustomer.fullName.trim()) return false;

    // Check either email or phone is provided
    const hasContact = newCustomer.email.trim() || newCustomer.phone.trim();
    if (!hasContact) return false;

    // Check for validation errors
    const nameValidation = validateField('fullName', newCustomer.fullName);
    if (!nameValidation.isValid) return false;

    // Validate contact info if provided
    if (newCustomer.email.trim()) {
      const emailValidation = validateField('email', newCustomer.email);
      if (!emailValidation.isValid) return false;
    }

    if (newCustomer.phone.trim()) {
      const phoneValidation = validateField('phone', newCustomer.phone);
      if (!phoneValidation.isValid) return false;
    }

    // Check for partial address data - if any address field is filled, require minimum fields
    const hasAnyAddressData = 
      newCustomer.address.trim() || 
      newCustomer.addressLine2.trim() || 
      newCustomer.city.trim() || 
      newCustomer.state.trim() || 
      newCustomer.zipCode.trim();

    if (hasAnyAddressData) {
      // If any address data is provided, require address, state, and zipcode at minimum
      if (!newCustomer.address.trim() || !newCustomer.state.trim() || !newCustomer.zipCode.trim()) {
        return false;
      }

      // Validate zip code format when address is being completed
      const zipValidation = validateField('zipCode', newCustomer.zipCode, newCustomer.country);
      if (!zipValidation.isValid) return false;
    }

    return true;
  };

  const toggleTag = (tag: string) => {
    setNewCustomer(prev => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter(t => t !== tag)
        : [...prev.tags, tag]
    }));
  };

  const resetForm = () => {
    setNewCustomer({
      fullName: '',
      phone: '',
      email: '',
      address: '',
      addressLine2: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'United States',
      tags: []
    });
    setEditingCustomer(null);
    setValidationErrors({});
    setFieldTouched({});
  };

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer);
    setNewCustomer({
      fullName: customer.name,
      phone: customer.phone || '',
      email: customer.email || '',
      address: customer.customFields?.address || '',
      addressLine2: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'United States',
      tags: customer.tags || []
    });
    setValidationErrors({});
    setShowModal(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const errors: Record<string, string> = {};
    let hasErrors = false;

    // Validate required field
    const nameValidation = validateField('fullName', newCustomer.fullName);
    if (!nameValidation.isValid) {
      errors.fullName = nameValidation.error;
      hasErrors = true;
    }

    // Check if either email or phone is provided
    if (!newCustomer.phone.trim() && !newCustomer.email.trim()) {
      errors.phone = 'Either phone or email is required';
      errors.email = 'Either phone or email is required';
      hasErrors = true;
    } else {
      // Clear the "either phone or email" error if at least one is provided
      // Validate phone if provided
      if (newCustomer.phone.trim()) {
        const phoneValidation = validateField('phone', newCustomer.phone);
        if (!phoneValidation.isValid) {
          errors.phone = phoneValidation.error;
          hasErrors = true;
        }
      }

      // Validate email if provided
      if (newCustomer.email.trim()) {
        const emailValidation = validateField('email', newCustomer.email);
        if (!emailValidation.isValid) {
          errors.email = emailValidation.error;
          hasErrors = true;
        }
      }
    }

    // Check for partial address data - if any address field is filled, require minimum fields
    const hasAnyAddressData = 
      newCustomer.address.trim() || 
      newCustomer.addressLine2.trim() || 
      newCustomer.city.trim() || 
      newCustomer.state.trim() || 
      newCustomer.zipCode.trim();

    if (hasAnyAddressData) {
      // If any address data is provided, require address, state, and zipcode at minimum
      if (!newCustomer.address.trim()) {
        errors.address = 'Street address is required when providing address information';
        hasErrors = true;
      }
      if (!newCustomer.state.trim()) {
        errors.state = 'State is required when providing address information';
        hasErrors = true;
      }
      if (!newCustomer.zipCode.trim()) {
        errors.zipCode = 'Zip code is required when providing address information';
        hasErrors = true;
      } else {
        // Validate zip code format when address is being completed
        const zipValidation = validateField('zipCode', newCustomer.zipCode, newCustomer.country);
        if (!zipValidation.isValid) {
          errors.zipCode = zipValidation.error;
          hasErrors = true;
        }
      }
    } else if (newCustomer.zipCode.trim()) {
      // Validate zip code format if provided independently
      const zipValidation = validateField('zipCode', newCustomer.zipCode, newCustomer.country);
      if (!zipValidation.isValid) {
        errors.zipCode = zipValidation.error;
        hasErrors = true;
      }
    }

    if (hasErrors) {
      setValidationErrors(errors);
      console.log('❌ Validation errors:', errors);
      alert('Please fix the validation errors before submitting');
      return;
    }

    console.log('✅ Validation passed, submitting customer data:', newCustomer);

    try {
      // Build full address from components
      const addressParts = [
        newCustomer.address.trim(),
        newCustomer.addressLine2.trim(),
        newCustomer.city.trim(),
        newCustomer.state.trim(),
        newCustomer.zipCode.trim(),
        newCustomer.country.trim()
      ].filter(Boolean);

      const customerData = {
        fullName: newCustomer.fullName.trim(),
        phone: newCustomer.phone.trim() || null,
        email: newCustomer.email.trim() || null,
        address: addressParts.length > 0 ? addressParts.join(', ') : null,
        tags: newCustomer.tags.length > 0 ? newCustomer.tags : null
      };

      let response;
      if (editingCustomer) {
        // Update existing customer using business-specific endpoint
        response = await fetch(`/api/businesses/${businessId}/customers/${editingCustomer.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(customerData)
        });
      } else {
        // Create new customer using business-specific endpoint
        try {
          response = await fetch(`/api/businesses/${businessId}/customers`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(customerData)
          });
          
          // If business endpoint fails, try demo endpoint as fallback
          if (!response.ok && response.status === 404) {
            console.log('⚠️ Business endpoint not found, trying demo endpoint');
            response = await fetch('/api/demo/customers', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(customerData)
            });
          }
        } catch (error) {
          console.log('⚠️ Business endpoint failed, trying demo endpoint:', error);
          response = await fetch('/api/demo/customers', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(customerData)
          });
        }
      }

      if (response.ok) {
        const responseData = await response.json();
        console.log('✅ Customer created successfully:', responseData);
        await fetchCustomers();
        setShowModal(false);
        resetForm();
        alert(`Customer ${customerData.fullName} has been ${editingCustomer ? 'updated' : 'added'} successfully!`);
      } else {
        let errorMessage = `Failed to ${editingCustomer ? 'update' : 'add'} customer`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
          console.error('❌ API Error:', errorData);
        } catch (parseError) {
          const errorText = await response.text();
          console.error('❌ Error response (text):', errorText);
          errorMessage = `Server error (${response.status}): ${errorText}`;
        }
        alert(`${errorMessage}. Please try again.`);
      }
    } catch (error) {
      console.error('❌ Network/Request error:', error);
      if (error instanceof TypeError && error.message.includes('fetch')) {
        alert('Network error: Unable to connect to the server. Please check your internet connection and try again.');
      } else {
        alert(`Failed to ${editingCustomer ? 'update' : 'add'} customer. Please try again.`);
      }
    }
  };

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesTags = filterByTags.length === 0 || 
                       filterByTags.some(tag => customer.tags.includes(tag));

    return matchesSearch && matchesTags;
  });

  const handleDeleteCustomer = (customer: Customer) => {
    setDeletingCustomer(customer);
    setShowDeleteModal(true);
  };

  const confirmDeleteCustomer = async () => {
    if (deletingCustomer) {
      try {
        // Don't try to delete demo customers via API
        if (deletingCustomer.id.startsWith('demo-')) {
          // Just remove from local state for demo customers
          setCustomers(prev => prev.filter(c => c.id !== deletingCustomer.id));
          setShowDeleteModal(false);
          setDeletingCustomer(null);
          alert(`${deletingCustomer.name} has been removed from the demo list.`);
          return;
        }

        const response = await fetch(`/api/businesses/${businessId}/customers/${deletingCustomer.id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const responseData = await response.json();
          // Remove customer from local state
          setCustomers(prev => prev.filter(c => c.id !== deletingCustomer.id));
          setShowDeleteModal(false);
          setDeletingCustomer(null);
          // Show success message
          alert(`${deletingCustomer.name} has been permanently deleted from this business.`);
        } else {
          let errorMessage = 'Unknown error occurred';
          try {
            const errorData = await response.json();
            errorMessage = errorData.message || errorMessage;
          } catch (parseError) {
            console.error('Error parsing error response:', parseError);
            errorMessage = `Server error (${response.status})`;
          }
          alert(`Failed to delete customer: ${errorMessage}`);
        }
      } catch (error) {
        console.error('Error deleting customer:', error);

        // More specific error messages
        if (error instanceof TypeError && error.message.includes('fetch')) {
          alert('Network error: Unable to connect to the server. Please check your internet connection and try again.');
        } else if (error.name === 'AbortError') {
          alert('Request timeout: The deletion request took too long. Please try again.');
        } else {
          alert('Failed to delete customer: An unexpected error occurred. Please try again.');
        }
      }
    }
  };

  const saveEditedCustomer = (updatedCustomer: Customer) => {
    // In a real app, this would make an API call to update the customer
    console.log('Updating customer:', updatedCustomer);
    setShowEditModal(false);
    setEditingCustomer(null);
    // You could update the customers state here if managing locally
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Customer Management</h1>
          <p className="text-gray-600 mt-2">Manage your customer referral network</p>
        </div>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => setShowInsights(!showInsights)}
            className={`px-6 py-3 rounded-xl font-semibold transition-colors flex items-center ${
              showInsights 
                ? 'bg-blue-600 text-white' 
                : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
            }`}
          >
            <BarChart3 className="w-5 h-5 mr-2" />
            {showInsights ? 'Hide Insights' : 'View Insights'}
          </button>
          <button
            onClick={() => setShowTagManager(true)}
            className="bg-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-purple-700 transition-colors flex items-center"
          >
            <Filter className="w-5 h-5 mr-2" />
            Manage Tags
          </button>
          <button
            onClick={() => setShowBulkActions(true)}
            className="bg-orange-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-orange-700 transition-colors flex items-center"
          >
            <Users className="w-5 h-5 mr-2" />
            Bulk Actions
          </button>
          <button
            onClick={() => setShowImportModal(true)}
            className="bg-green-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-green-700 transition-colors flex items-center"
          >
            <Upload className="w-5 h-5 mr-2" />
            Import Customers
          </button>
          <button 
            onClick={() => {
              console.log('Add Customer button clicked, setting showModal to true');
              setShowModal(true);
            }}
            className="bg-indigo-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-indigo-700 transition-colors flex items-center"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add Customer
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-xl p-6 shadow-lg">
          <div className="flex items-center justify-between mb-2">
            <Users className="w-8 h-8 text-blue-600" />
            <span className="text-sm text-green-600 font-medium">+12%</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">{customers.length}</p>
          <p className="text-gray-600">Total Customers</p>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-lg">
          <div className="flex items-center justify-between mb-2">
            <UserPlus className="w-8 h-8 text-green-600" />
            <span className="text-sm text-green-600 font-medium">+23%</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {customers.filter(c => c.status === 'active').length}
          </p>
          <p className="text-gray-600">Active Referrers</p>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-lg">
          <div className="flex items-center justify-between mb-2">
            <TrendingUp className="w-8 h-8 text-purple-600" />
            <span className="text-sm text-green-600 font-medium">+18%</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {customers.reduce((sum, c) => sum + c.totalReferrals, 0)}
          </p>
          <p className="text-gray-600">Total Referrals</p>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-lg">
          <div className="flex items-center justify-between mb-2">
            <DollarSign className="w-8 h-8 text-orange-600" />
            <span className="text-sm text-green-600 font-medium">+31%</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">
            ${customers.reduce((sum, c) => sum + c.totalEarnings, 0).toLocaleString()}
          </p>
          <p className="text-gray-600">Total Rewards Paid</p>
        </div>
      </div>

      {/* Customer Insights */}
      {showInsights && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mb-8"
        >
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <CustomerInsights customers={customers} />
            </div>
            <div>
              <SmartCustomerRecommendations 
                customers={customers}
                onAction={(recommendation) => {
                  console.log('Executing recommendation:', recommendation);
                  // Handle different recommendation types
                  switch (recommendation.type) {
                    case 'engagement':
                      alert(`Starting ${recommendation.title} for ${recommendation.customerCount} customers...`);
                      break;
                    case 'retention':
                      alert(`Launching retention campaign for ${recommendation.customerCount} customers...`);
                      break;
                    case 'reward':
                      alert(`Sending rewards to ${recommendation.customerCount} customers...`);
                      break;
                    case 'growth':
                      alert(`Activating growth strategy for ${recommendation.customerCount} customers...`);
                      break;
                    default:
                      alert(`Executing: ${recommendation.title}`);
                  }
                }}
              />
            </div>
          </div>
        </motion.div>
      )}

      {/* Search and Filters */}
      <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500"
              placeholder="Search customers..."
            />
          </div>
          <button className="flex items-center px-4 py-3 text-gray-600 border border-gray-300 rounded-xl hover:bg-gray-50">
            <Filter className="w-5 h-5 mr-2" />
            Filter
          </button>
          <button className="flex items-center px-4 py-3 text-gray-600 border border-gray-300 rounded-xl hover:bg-gray-50">
            <Download className="w-5 h-5 mr-2" />
            Export
          </button>
        </div>

        {/* Enhanced Tag Management Section */}
        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 border border-indigo-100">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Tag className="w-4 h-4 text-white" />
              </div>
              <div>
                <p className="text-sm font-semibold text-gray-900">Customer Tags</p>
                <p className="text-xs text-gray-600">Filter and organize your customers</p>
              </div>
            </div>

            {/* Enhanced Manage Tags Button */}
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full border border-gray-200">
                {availableTags.length} total tags
              </span>
              <button
                onClick={() => setShowTagManager(true)}
                className="flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl font-medium text-sm group transform hover:scale-105"
                title="Create, edit, and organize all customer tags"
              >
                <Settings className="w-4 h-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                Manage Tags
                <span className="ml-1 text-xs bg-white/20 px-1.5 py-0.5 rounded-full">
                  {availableTags.length}
                </span>
              </button>
            </div>
          </div>

          {/* Tag Filter Pills */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-xs font-medium text-gray-700">Quick Filters:</p>
              {filterByTags.length > 0 && (
                <button
                  onClick={() => setFilterByTags([])}
                  className="flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-600 border border-red-200 hover:bg-red-200 rounded-full transition-all duration-200"
                >
                  <X className="w-3 h-3 mr-1" />
                  Clear ({filterByTags.length})
                </button>
              )}
            </div>

            <div className="flex flex-wrap gap-2">
              {availableTags.slice(0, 10).map((tag) => (
                <button
                  key={tag}
                  onClick={() => {
                    setFilterByTags(prev => 
                      prev.includes(tag) 
                        ? prev.filter(t => t !== tag)
                        : [...prev, tag]
                    );
                  }}
                  className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 border-2 ${
                    filterByTags.includes(tag)
                      ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-indigo-300 shadow-md transform scale-105'
                      : 'bg-white text-gray-700 border-gray-200 hover:bg-gray-50 hover:border-indigo-200 hover:text-indigo-600'
                  }`}
                >
                  {filterByTags.includes(tag) && <CheckCircle className="w-3 h-3 inline mr-1" />}
                  {tag}
                </button>
              ))}

              {availableTags.length > 10 && (
                <button
                  onClick={() => setShowTagManager(true)}
                  className="px-3 py-1.5 rounded-lg text-sm font-medium bg-white text-indigo-600 border-2 border-dashed border-indigo-300 hover:bg-indigo-50 hover:border-indigo-400 transition-all duration-200"
                >
                  +{availableTags.length - 10} more
                </button>
              )}
            </div>

            {/* Active Filters Summary */}
            {filterByTags.length > 0 && (
              <div className="bg-white rounded-lg p-3 border border-indigo-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Filter className="w-4 h-4 text-indigo-600" />
                    <span className="text-sm font-medium text-indigo-900">
                      Active Filters ({filterByTags.length})
                    </span>
                  </div>
                  <span className="text-xs text-indigo-600 bg-indigo-100 px-2 py-1 rounded-full">
                    {filteredCustomers.length} customers match
                  </span>
                </div>
                <div className="flex flex-wrap gap-1 mt-2">
                  {filterByTags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 bg-indigo-100 text-indigo-700 rounded-md text-xs font-medium"
                    >
                      {tag}
                      <button
                        onClick={() => setFilterByTags(prev => prev.filter(t => t !== tag))}
                        className="ml-1 text-indigo-500 hover:text-indigo-700"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Error Banner */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
          <div className="flex items-center">
            <div className="w-5 h-5 bg-red-400 rounded-full mr-3"></div>
            <p className="text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="bg-white rounded-xl shadow-lg p-16 text-center">
          <div className="animate-spin w-8 h-8 border-2 border-indigo-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">Loading customers...</p>
        </div>
      )}

      {/* Customer List */}
      {!loading && (
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Customer List</h2>

            {/* Bulk Selection Controls */}
            {filteredCustomers.length > 0 && (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <input
                    type="checkbox"
                    checked={selectedCustomers.length === filteredCustomers.length && filteredCustomers.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedCustomers(filteredCustomers.map(c => c.id));
                      } else {
                        setSelectedCustomers([]);
                      }
                    }}
                    className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                  />
                  <span>Select All</span>
                </div>

                {selectedCustomers.length > 0 && (
                  <button
                    onClick={() => setSelectedCustomers([])}
                    className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    Deselect All
                  </button>
                )}

                {selectedCustomers.length > 0 && (
                  <span className="text-sm font-medium text-indigo-600">
                    {selectedCustomers.length} selected
                  </span>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Bulk Actions Bar */}
        {selectedCustomers.length > 0 && (
          <div className="bg-indigo-50 border-b border-indigo-200 px-6 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5 text-indigo-600" />
                <span className="text-sm font-medium text-indigo-900">
                  {selectedCustomers.length} customer{selectedCustomers.length !== 1 ? 's' : ''} selected
                </span>
              </div>

              <div className="flex items-center space-x-3">
                <button
                  onClick={() => {
                    // Handle bulk reward bonus
                    const selectedNames = customers
                      .filter(c => selectedCustomers.includes(c.id))
                      .map(c => c.name)
                      .join(', ');
                    alert(`Sending reward bonus to: ${selectedNames}`);
                  }}
                  className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                >
                  <Gift className="w-4 h-4 mr-2" />
                  Send Reward Bonus
                </button>

                <button
                  onClick={() => {
                    // Handle bulk email
                    const selectedEmails = customers
                      .filter(c => selectedCustomers.includes(c.id))
                      .map(c => c.email)
                      .join(';');
                    window.open(`mailto:${selectedEmails}?subject=Important Update from Your Business&body=Hello,%0D%0A%0D%0AWe have an important update for you!`);
                  }}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Send Email
                </button>

                <button
                  onClick={() => setShowBulkDeleteModal(true)}
                  className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete Selected
                </button>

                <button
                  onClick={() => setShowBulkActions(true)}
                  className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium"
                >
                  More Actions
                </button>
              </div>
            </div>
          </div>
        )}
        <div className="divide-y divide-gray-200">
          {filteredCustomers.length === 0 ? (
            <div className="p-16 text-center">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2, type: "spring" }}
                className="mb-8"
              >
                {searchTerm || filterByTags.length > 0 ? (
                  // Filtered state - no matches
                  <>
                    <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-indigo-100 rounded-3xl flex items-center justify-center mx-auto mb-6">
                      <Filter className="w-12 h-12 text-gray-400" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">No Customers Match Your Filters</h3>
                    <p className="text-lg text-gray-600 mb-8 max-w-lg mx-auto leading-relaxed">
                      Try adjusting your filter settings to see more customers, or create a new customer for this list.
                    </p>
                    <motion.button
                      onClick={() => {
                        setSearchTerm('');
                        setFilterByTags([]);
                      }}
                      className="bg-indigo-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Clear Filters
                    </motion.button>
                  </>
                ) : (
                  // Empty state - no customers at all
                  <>
                    <div className="w-24 h-24 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-3xl flex items-center justify-center mx-auto mb-6">
                      <Users className="w-12 h-12 text-indigo-600" />
                    </div>
                    <h3 className="text-3xl font-bold text-gray-900 mb-4">Ready to Build Your Customer Network?</h3>
                    <p className="text-lg text-gray-600 mb-8 max-w-lg mx-auto leading-relaxed">
                      Transform your existing customers into a powerful referral network. Upload your customer list and watch as they become your biggest advocates, sharing your business and earning rewards with unlimited potential.
                    </p>

                    {/* Enhanced CTA Buttons */}
                    <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                      <motion.button
                        onClick={() => setShowImportModal(true)}
                        className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                        whileHover={{ scale: 1.02, y: -2 }}
                        whileTap={{ scale: 0.98 }}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4 }}
                      >
                        <Upload className="w-5 h-5 mr-2" />
                        Import Customers
                      </motion.button>

                      <motion.button
                        onClick={() => setShowModal(true)}
                        className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                        whileHover={{ scale: 1.02, y: -2 }}
                        whileTap={{ scale: 0.98 }}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 }}
                      >
                        <Plus className="w-5 h-5 mr-2" />
                        Add Customer
                      </motion.button>
                    </div>

                    {/* Feature Preview Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                      <motion.div
                        className="bg-white border border-gray-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6 }}
                        whileHover={{ y: -4 }}
                      >
                        <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mb-4">
                          <Upload className="w-6 h-6 text-white" />
                        </div>
                        <h4 className="text-lg font-bold text-gray-900 mb-2">Import From CSV</h4>
                        <p className="text-sm text-gray-600 leading-relaxed">
                          Upload your existing customer list with name, email, and phone details
                        </p>
                      </motion.div>

                      <motion.div
                        className="bg-white border border-gray-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.7 }}
                        whileHover={{ y: -4 }}
                      >
                        <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mb-4">
                          <Gift className="w-6 h-6 text-white" />
                        </div>
                        <h4 className="text-lg font-bold text-gray-900 mb-2">Assign Vouchers</h4>
                        <p className="text-sm text-gray-600 leading-relaxed">
                          Create targeted campaigns and assign specific vouchers to customer segments
                        </p>
                      </motion.div>

                      <motion.div
                        className="bg-white border border-gray-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.8 }}
                        whileHover={{ y: -4 }}
                      >
                        <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mb-4">
                          <TrendingUp className="w-6 h-6 text-white" />
                        </div>
                        <h4 className="text-lg font-bold text-gray-900 mb-2">Track Performance</h4>
                        <p className="text-sm text-gray-600 leading-relaxed">
                          Monitor referrals, earnings, and engagement across your customer network
                        </p>
                      </motion.div>
                    </div>
                  </>
                )}
              </motion.div>

              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1 }}
                className="text-sm text-gray-500 mt-6"
              >
                💡 Tip: Start with yourAnalysis: The `CustomerManager` component's `confirmDeleteCustomer` function is updated to use business-specific endpoints when deleting a customer.

```
most loyal customers - they're likely to become your best referrers
              </motion.p>
            </div>
          ) : (
            filteredCustomers.map((customer) => (
            <motion.div
              key={customer.id}
              className={`p-6 hover:bg-gray-50 transition-colors ${
                selectedCustomers.includes(customer.id) ? 'bg-indigo-50 border-l-4 border-indigo-500' : ''
              }`}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <input
                    type="checkbox"
                    checked={selectedCustomers.includes(customer.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedCustomers(prev => [...prev, customer.id]);
                      } else {
                        setSelectedCustomers(prev => prev.filter(id => id !== customer.id));
                      }
                    }}
                    className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                  />
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center text-white font-semibold">
                    {customer.name.charAt(0)}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{customer.name}</h3>
                    <p className="text-gray-600 text-sm">{customer.email}</p>
                    {customer.phone && (
                      <p className="text-gray-500 text-sm">{customer.phone}</p>
                    )}
                    {/* Tags Display */}
                    {customer.tags && customer.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {customer.tags.slice(0, 3).map((tag, tagIndex) => (
                          <span
                            key={tagIndex}
                            className="px-2 py-1 bg-indigo-100 text-indigo-600 rounded text-xs font-medium"
                          >
                            {tag}
                          </span>
                        ))}
                        {customer.tags.length > 3 && (
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                            +{customer.tags.length - 3}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-8 text-sm">
                  <div className="text-center">
                    <p className="font-semibold text-gray-900">{customer.totalReferrals}</p>
                    <p className="text-gray-500">Referrals</p>
                  </div>
                  <div className="text-center">
                    <p className="font-semibold text-gray-900">${customer.totalEarnings}</p>
                    <p className="text-gray-500">Earned</p>
                  </div>
                  <div className="text-center">
                    <p className="font-semibold text-gray-900">
                      {new Date(customer.lastActivity).toLocaleDateString()}
                    </p>
                    <p className="text-gray-500">Last Active</p>
                  </div>
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                    customer.status === 'active' 
                      ? 'bg-green-100 text-green-600' 
                      : 'bg-yellow-100 text-yellow-600'
                  }`}>
                    {customer.status === 'active' ? '🟢 Active' : '🔘 Invited'}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button 
                    onClick={() => {
                      window.open(`mailto:${customer.email}?subject=Special Offer from Your Business&body=Hi ${customer.name},%0D%0A%0D%0AWe have a special offer just for you!`);
                    }}
                    className="p-2 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors"
                    title="Send email"
                  >
                    <Mail className="w-5 h-5" />
                  </button>
                  <button 
                    onClick={() => {
                      setSelectedCustomerForEngagement(customer);
                      setShowEngagementModal(true);
                    }}
                    className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                    title="Engagement tools"
                  >
                    <Zap className="w-5 h-5" />
                  </button>
                  <button 
                    onClick={() => {
                      alert(`Creating targeted voucher for ${customer.name}...`);
                    }}
                    className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors"
                    title="Create targeted voucher"
                  >
                    <Gift className="w-5 h-5" />
                  </button>
                  <button 
                    onClick={() => handleEditCustomer(customer)}
                    className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Edit customer"
                  >
                    <Edit className="w-5 h-5" />
                  </button>
                  <button 
                    onClick={() => handleDeleteCustomer(customer)}
                    className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="Delete customer"
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </motion.div>
          ))
          )}
        </div>
      </div>
      )}

      {/* Tag Manager Modal */}
      {showTagManager && (
        <TagManager
          isOpen={showTagManager}
          onClose={() => setShowTagManager(false)}
          businessId={businessId}
          onTagsUpdated={(updatedTags) => {
            // Update available tags when changes are made
            setAvailableTags(updatedTags.map(tag => tag.name));
            // Refresh customers to reflect any tag changes
            fetchCustomers();
          }}
        />
      )}

      {/* Enhanced Bulk Actions Modal */}
      {showBulkActions && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-white rounded-2xl max-w-2xl w-full p-6 max-h-[80vh] overflow-y-auto"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Advanced Bulk Actions</h3>
              <button
                onClick={() => setShowBulkActions(false)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="bg-indigo-50 rounded-lg p-4 mb-6">
              <p className="text-indigo-800 font-medium">
                {selectedCustomers.length} customer{selectedCustomers.length !== 1 ? 's' : ''} selected
              </p>
              <p className="text-indigo-600 text-sm mt-1">
                Selected: {customers.filter(c => selectedCustomers.includes(c.id)).map(c => c.name).join(', ')}
              </p>
            </div>

            <div className="space-y-6">
              {/* Quick Actions */}
              <div>
                <h4 className="text-lg font-medium text-gray-900 mb-3">Quick Actions</h4>
                <div className="grid grid-cols-2 gap-3">
                  <button 
                    onClick={() => {
                      const amount = prompt('Enter reward bonus amount ($):');
                      if (amount && !isNaN(Number(amount))) {
                        const selectedNames = customers
                          .filter(c => selectedCustomers.includes(c.id))
                          .map(c => c.name);
                        alert(`Sending $${amount} reward bonus to:\n${selectedNames.join('\n')}`);
                        setShowBulkActions(false);
                      }
                    }}
                    className="flex items-center justify-center p-4 bg-green-50 text-green-700 rounded-xl hover:bg-green-100 transition-colors"
                  >
                    <DollarSign className="w-5 h-5 mr-2" />
                    Send Reward Bonus
                  </button>

                  <button 
                    onClick={() => {
                      const selectedData = customers.filter(c => selectedCustomers.includes(c.id));
                      const csvData = selectedData.map(c => `${c.name},${c.email},${c.phone || ''},${c.totalReferrals},${c.totalEarnings}`).join('\n');
                      console.log('CSV Export:', csvData);
                      alert('Customer data exported to console');
                      setShowBulkActions(false);
                    }}
                    className="flex items-center justify-center p-4 bg-blue-50 text-blue-700 rounded-xl hover:bg-blue-100 transition-colors"
                  >
                    <Download className="w-5 h-5 mr-2" />
                    Export Data
                  </button>

                  <button 
                    onClick={() => {
                      const newStatus = confirm('Set selected customers to Active?') ? 'active' : 'inactive';
                      console.log(`Updating ${selectedCustomers.length} customers to ${newStatus}`);
                      alert(`Updated ${selectedCustomers.length} customers to ${newStatus}`);
                      setShowBulkActions(false);
                    }}
                    className="flex items-center justify-center p-4 bg-orange-50 text-orange-700 rounded-xl hover:bg-orange-100 transition-colors"
                  >
                    <Users className="w-5 h-5 mr-2" />
                    Update Status
                  </button>

                  <button 
                    onClick={() => {
                      alert('Creating targeted voucher campaign for selected customers...');
                      setShowBulkActions(false);
                    }}
                    className="flex items-center justify-center p-4 bg-purple-50 text-purple-700 rounded-xl hover:bg-purple-100 transition-colors"
                  >
                    <Gift className="w-5 h-5 mr-2" />
                    Create Campaign
                  </button>
                </div>
              </div>

              {/* Tag Management */}
              <div>
                <h4 className="text-lg font-medium text-gray-900 mb-3">Tag Management</h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Add Tags to Selected Customers</label>
                  <div className="flex flex-wrap gap-2">
                    {availableTags.slice(0, 10).map((tag) => (
                      <button
                        key={tag}
                        onClick={() => {
                          console.log(`Adding tag "${tag}" to ${selectedCustomers.length} customers`);
                          alert(`Added "${tag}" tag to ${selectedCustomers.length} customers`);
                        }}
                        className="px-3 py-1 bg-white text-gray-700 rounded-full text-sm border border-gray-200 hover:bg-indigo-50 hover:text-indigo-700 hover:border-indigo-200 transition-colors"
                      >
                        + {tag}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Danger Zone */}
              <div>
                <h4 className="text-lg font-medium text-red-600 mb-3">Danger Zone</h4>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <button 
                    onClick={() => {
                      setShowBulkActions(false);
                      setShowBulkDeleteModal(true);
                    }}
                    className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete All Selected
                  </button>
                  <p className="text-red-600 text-xs mt-2">
                    This action is permanent and cannot be undone
                  </p>
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={() => setShowBulkActions(false)}
                className="flex-1 py-3 text-gray-600 hover:text-gray-800 transition-colors font-medium"
              >
                Close
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Edit Customer Modal */}
      {showEditModal && editingCustomer && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-white rounded-2xl max-w-2xl w-full p-6 max-h-[80vh] overflow-y-auto"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Edit Customer</h3>
              <button
                onClick={() => setShowEditModal(false)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                  <input
                    type="text"
                    defaultValue={editingCustomer.name}
                    className="w-full px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500"
                    placeholder="Customer name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <input
                    type="email"
                    defaultValue={editingCustomer.email}
                    className="w-full px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                <input
                  type="tel"
                  defaultValue={editingCustomer.phone || ''}
                  className="w-full px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500"
                  placeholder="+****************"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  defaultValue={editingCustomer.status}
                  className="w-full px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                <div className="flex flex-wrap gap-2 mb-3">
                  {editingCustomer.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-indigo-100 text-indigo-600 rounded-full text-sm font-medium"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
                <div className="flex flex-wrap gap-2">
                  {availableTags.filter(tag => !editingCustomer.tags.includes(tag)).slice(0, 8).map((tag) => (
                    <button
                      key={tag}
                      className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm hover:bg-indigo-100 hover:text-indigo-600 transition-colors"
                    >
                      + {tag}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowEditModal(false)}
                className="flex-1 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => saveEditedCustomer(editingCustomer)}
                className="flex-1 bg-indigo-600 text-white py-2 rounded-xl font-medium hover:bg-indigo-700 transition-colors"
              >
                Save Changes
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && deletingCustomer && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-white rounded-2xl max-w-md w-full p-6"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
          >
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trash2 className="w-8 h-8 text-red-600" />
              </div>

              <h3 className="text-xl font-semibold text-gray-900 mb-2">Delete Customer</h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete <strong>{deletingCustomer.name}</strong>? 
                This action is permanent and cannot be undone. All customer data, referral history, 
                and earnings will be permanently removed.
              </p>

              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <div className="w-4 h-4 bg-red-400 rounded-full mt-0.5"></div>
                  </div>
                  <div className="ml-3 text-sm text-red-800">
                    <strong>Warning:</strong> This action is permanent and cannot be reversed.
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="flex-1 py-3 px-4 text-gray-600 hover:text-gray-800 transition-colors font-medium"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDeleteCustomer}
                  className="flex-1 bg-red-600 text-white py-3 px-4 rounded-xl font-medium hover:bg-red-700 transition-colors"
                >
                  Delete Customer
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Bulk Delete Confirmation Modal */}
      {showBulkDeleteModal && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-white rounded-2xl max-w-md w-full"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-red-600 to-pink-600 px-6 py-4 text-white">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                  <Trash2 className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">Delete Selected Customers</h3>
                  <p className="text-sm opacity-90">This action cannot be undone</p>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="mb-6">
                <div className="bg-gray-50 rounded-xl p-4 border border-gray-200 mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-600 rounded-lg flex items-center justify-center">
                      <Users className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-gray-900">
                        {selectedCustomers.length} customer{selectedCustomers.length !== 1 ? 's' : ''} selected
                      </h4>
                      <p className="text-sm text-gray-600">
                        {customers.filter(c => selectedCustomers.includes(c.id)).map(c => c.name).slice(0, 3).join(', ')}
                        {selectedCustomers.length > 3 && ` +${selectedCustomers.length - 3} more`}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-5 h-5 bg-red-400 rounded-full mt-0.5 flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-xs font-bold">!</span>
                    </div>
                    <div>
                      <h5 className="font-medium text-red-800 mb-1">⚠️ This action is permanent and cannot be undone:</h5>
                      <ul className="text-sm text-red-700 space-y-1">
                        <li>• Customer profiles will be permanently removed</li>
                        <li>• All referral history will be deleted</li>
                        <li>• Earnings data will be lost</li>
                        <li>• Associated records cannot be recovered</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <motion.button
                  onClick={() => setShowBulkDeleteModal(false)}
                  className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-medium transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Cancel
                </motion.button>
                <motion.button
                  onClick={async () => {
                    const selectedNames = customers
                      .filter(c => selectedCustomers.includes(c.id))
                      .map(c => c.name);

                    try {
                      // Separate demo customers from real customers
                      const demoCustomers = selectedCustomers.filter(id => id.startsWith('demo-'));
                      const realCustomers = selectedCustomers.filter(id => !id.startsWith('demo-'));

                      let totalDeleted = 0;
                      let totalFailed = 0;

                      // Handle demo customers (just remove from local state)
                      if (demoCustomers.length > 0) {
                        totalDeleted += demoCustomers.length;
                      }

                      // Handle real customers (API calls)
                      if (realCustomers.length > 0) {
                        const deletePromises = realCustomers.map(async (customerId) => {
                          try {
                            const response = await fetch(`/api/businesses/${businessId}/customers/${customerId}`, {
                              method: 'DELETE',
                              headers: {
                                'Content-Type': 'application/json',
                              },
                            });
                            return { success: response.ok, customerId };
                          } catch (error) {
                            console.error(`Error deleting customer ${customerId}:`, error);
                            return { success: false, customerId };
                          }
                        });

                        const results = await Promise.all(deletePromises);
                        const successfulDeletions = results.filter(r => r.success);
                        const failedDeletions = results.filter(r => !r.success);

                        totalDeleted += successfulDeletions.length;
                        totalFailed = failedDeletions.length;
                      }

                      // Update local state for all successful deletions
                      const successfulIds = [
                        ...demoCustomers,
                        ...realCustomers.filter(id => !selectedCustomers.includes(id) || totalFailed === 0)
                      ];

                      if (totalDeleted > 0) {
                        setCustomers(prev => prev.filter(c => !selectedCustomers.includes(c.id)));
                      }

                      setShowBulkDeleteModal(false);
                      setSelectedCustomers([]);

                      if (totalFailed === 0) {
                        alert(`Successfully deleted ${totalDeleted} customer${totalDeleted !== 1 ? 's' : ''}.`);
                      } else {
                        alert(`Deleted ${totalDeleted} customer${totalDeleted !== 1 ? 's' : ''}. ${totalFailed} customer${totalFailed !== 1 ? 's' : ''} could not be deleted. Please try again.`);
                      }
                    } catch (error) {
                      console.error('Error bulk deleting customers:', error);

                      if (error instanceof TypeError && error.message.includes('fetch')) {
                        alert('Network error: Unable to connect to the server. Please check your internet connection and try again.');
                      } else {
                        alert('Failed to delete customers: An unexpected error occurred. Please try again.');
                      }
                    }
                  }}
                  className="flex-1 px-4 py-3 bg-gradient-to-r from-red-600 to-pink-600 text-white rounded-xl font-semibold hover:from-red-700 hover:to-pink-700 transition-all shadow-lg hover:shadow-xl"
                  whileHover={{ scale: 1.02, y: -1 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Yes, Delete Forever
                </motion.button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Add Customer Modal */}
      {console.log('Modal render check - showModal:', showModal)}
      {showModal && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4 text-white sticky top-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                    <UserPlus className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">Add New Customer</h3>
                    <p className="text-sm opacity-90">Expand your referral network</p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setShowModal(false);
                    resetForm();
                  }}
                  className="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              <form onSubmit={handleSubmit}>
                <div className="space-y-6">
                  {/* Basic Information */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      value={newCustomer.fullName}
                      onChange={(e) => handleFieldChange('fullName', e.target.value)}
                      onBlur={(e) => handleFieldBlur('fullName', e.target.value)}
                      className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all ${
                        validationErrors.fullName 
                          ? 'border-red-300 focus:border-red-500' 
                          : 'border-gray-200 focus:border-indigo-500'
                      }`}
                      placeholder="Enter customer's full name"
                    />
                    {validationErrors.fullName && (
                      <p className="text-red-600 text-sm mt-1 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {validationErrors.fullName}
                      </p>
                    )}
                  </div>

                  {/* Contact Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        value={newCustomer.email}
                        onChange={(e) => handleFieldChange('email', e.target.value)}
                        onBlur={(e) => handleFieldBlur('email', e.target.value)}
                        className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all ${
                          validationErrors.email 
                            ? 'border-red-300 focus:border-red-500' 
                            : 'border-gray-200 focus:border-indigo-500'
                        }`}
                        placeholder="<EMAIL>"
                      />
                      {validationErrors.email && (
                        <p className="text-red-600 text-sm mt-1 flex items-center">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {validationErrors.email}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={newCustomer.phone}
                        onChange={(e) => {
                          const formatted = formatPhoneNumber(e.target.value);
                          handleFieldChange('phone', formatted);
                        }}
                        onBlur={(e) => handleFieldBlur('phone', e.target.value)}
                        className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all ${
                          validationErrors.phone 
                            ? 'border-red-300 focus:border-red-500' 
                            : 'border-gray-200 focus:border-indigo-500'
                        }`}
                        placeholder="************"
                      />
                      {validationErrors.phone && (
                        <p className="text-red-600 text-sm mt-1 flex items-center">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {validationErrors.phone}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 mb-4">
                    <p className="text-sm text-amber-800">
                      <strong>Note:</strong> Either email or phone number is required.
                    </p>
                  </div>

                  {/* Address Requirements Note */}
                  {(newCustomer.address.trim() || newCustomer.addressLine2.trim() || newCustomer.city.trim() || newCustomer.state.trim() || newCustomer.zipCode.trim()) && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                      <p className="text-sm text-blue-800">
                        <strong>Address Info:</strong> When providing address information, street address, state, and zip code are required at minimum.
                      </p>
                    </div>
                  )}

                  {/* Address Information */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Address (Optional)
                      </label>
                      <div className="flex items-center space-x-2">
                        {(newCustomer.address.trim() || newCustomer.addressLine2.trim() || newCustomer.city.trim() || newCustomer.state.trim() || newCustomer.zipCode.trim()) && (
                          <button
                            type="button"
                            onClick={() => {
                              setNewCustomer(prev => ({
                                ...prev,
                                address: '',
                                addressLine2: '',
                                city: '',
                                state: '',
                                zipCode: ''
                              }));
                              // Clear any address-related validation errors
                              setValidationErrors(prev => {
                                const newErrors = { ...prev };
                                delete newErrors.address;
                                delete newErrors.state;
                                delete newErrors.zipCode;
                                return newErrors;
                              });
                            }}
                            className="text-xs text-red-600 hover:text-red-700 bg-red-50 hover:bg-red-100 px-2 py-1 rounded-full transition-colors flex items-center"
                          >
                            <X className="w-3 h-3 mr-1" />
                            Clear Address
                          </button>
                        )}
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                          Helps with local targeting
                        </span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      {/* Country Selection First */}
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">Country</label>
                        <select
                          value={newCustomer.country}
                          onChange={(e) => {
                            handleFieldChange('country', e.target.value);
                            // Clear zip code when country changes to avoid validation conflicts
                            if (newCustomer.zipCode) {
                              handleFieldChange('zipCode', '');
                            }
                          }}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                        >
                          <option value="United States">🇺🇸 United States</option>
                          <option value="Canada">🇨🇦 Canada</option>
                          <option value="United Kingdom">🇬🇧 United Kingdom</option>
                          <option value="Australia">🇦🇺 Australia</option>
                          <option value="Other">🌍 Other</option>
                        </select>
                      </div>

                      {/* Street Address */}
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          Street Address
                          {(newCustomer.addressLine2.trim() || newCustomer.city.trim() || newCustomer.state.trim() || newCustomer.zipCode.trim()) && (
                            <span className="text-red-500 ml-1">*</span>
                          )}
                        </label>
                        <input
                          type="text"
                          value={newCustomer.address}
                          onChange={(e) => handleFieldChange('address', e.target.value)}
                          onBlur={(e) => handleFieldBlur('address', e.target.value)}
                          className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all ${
                            validationErrors.address 
                              ? 'border-red-300 focus:border-red-500' 
                              : 'border-gray-200 focus:border-indigo-500'
                          }`}
                          placeholder="123 Main Street"
                        />
                        {validationErrors.address && (
                          <p className="text-red-600 text-xs mt-1 flex items-center">
                            <AlertCircle className="w-3 h-3 mr-1" />
                            {validationErrors.address}
                          </p>
                        )}
                      </div>

                      {/* Address Line 2 */}
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">Address Line 2</label>
                        <input
                          type="text"
                          value={newCustomer.addressLine2}
                          onChange={(e) => handleFieldChange('addressLine2', e.target.value)}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                          placeholder="Apartment, suite, unit, building, floor, etc."
                        />
                      </div>

                      {/* City, State, Zip */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">City</label>
                          <input
                            type="text"
                            value={newCustomer.city}
                            onChange={(e) => handleFieldChange('city', e.target.value)}
                            className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                            placeholder="New York"
                          />
                        </div>

                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">
                            {getStateLabel(newCustomer.country)}
                            {(newCustomer.address.trim() || newCustomer.addressLine2.trim() || newCustomer.city.trim() || newCustomer.zipCode.trim()) && (
                              <span className="text-red-500 ml-1">*</span>
                            )}
                          </label>
                          {getStateOptions(newCustomer.country).length > 0 ? (
                            <select
                              value={newCustomer.state}
                              onChange={(e) => handleFieldChange('state', e.target.value)}
                              onBlur={(e) => handleFieldBlur('state', e.target.value)}
                              className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all bg-white ${
                                validationErrors.state 
                                  ? 'border-red-300 focus:border-red-500' 
                                  : 'border-gray-200 focus:border-indigo-500'
                              }`}
                            >
                              <option value="">Select {getStateLabel(newCustomer.country)}</option>
                              {getStateOptions(newCustomer.country).map((option) => (
                                <option key={option.value} value={option.value}>
                                  {option.label}
                                </option>
                              ))}
                            </select>
                          ) : (
                            <input
                              type="text"
                              value={newCustomer.state}
                              onChange={(e) => handleFieldChange('state', e.target.value)}
                              onBlur={(e) => handleFieldBlur('state', e.target.value)}
                              className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all ${
                                validationErrors.state 
                                  ? 'border-red-300 focus:border-red-500' 
                                  : 'border-gray-200 focus:border-indigo-500'
                              }`}
                              placeholder="Enter state/region"
                            />
                          )}
                          {validationErrors.state && (
                            <p className="text-red-600 text-xs mt-1 flex items-center">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              {validationErrors.state}
                            </p>
                          )}
                        </div>

                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">
                            {getZipCodeLabel(newCustomer.country)}
                            {(newCustomer.address.trim() || newCustomer.addressLine2.trim() || newCustomer.city.trim() || newCustomer.state.trim()) && (
                              <span className="text-red-500 ml-1">*</span>
                            )}
                          </label>
                          <input
                            type="text"
                            value={newCustomer.zipCode}
                            onChange={(e) => handleFieldChange('zipCode', e.target.value)}
                            onInput={handleZipCodeInput}
                            onBlur={(e) => handleFieldBlur('zipCode', e.target.value)}
                            maxLength={newCustomer.country === 'United States' ? 5 : undefined}
                            pattern={newCustomer.country === 'United States' ? '[0-9]{5}' : undefined}
                            inputMode={newCustomer.country === 'United States' ? 'numeric' : 'text'}
                            className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all ${
                              validationErrors.zipCode 
                                ? 'border-red-300 focus:border-red-500' 
                                  : 'border-gray-200 focus:border-indigo-500'
                            }`}
                            placeholder={getZipCodePlaceholder(newCustomer.country)}
                          />
                          {validationErrors.zipCode && (
                            <p className="text-red-600 text-xs mt-1 flex items-center">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              {validationErrors.zipCode}
                            </p>
                          )}
                          {newCustomer.country === 'United States' && !validationErrors.zipCode && (
                            <p className="text-blue-600 text-xs mt-1 flex items-center">
                              <span className="mr-1">💡</span>
                              State will auto-populate when you enter a valid zip code
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Tags Section - Enhanced with selection state */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Customer Tags (Optional)
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {availableTags.slice(0, 12).map((tag) => (
                        <button
                          key={tag}
                          type="button"
                          onClick={() => toggleTag(tag)}
                          className={`px-3 py-2 rounded-full text-sm font-medium transition-all border-2 ${
                            newCustomer.tags.includes(tag)
                              ? 'bg-indigo-100 text-indigo-700 border-indigo-200 shadow-sm'
                              : 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-200 hover:border-gray-300'
                          }`}
                        >
                          {newCustomer.tags.includes(tag) && <CheckCircle className="w-3 h-3 inline mr-1" />}
                          {tag}
                        </button>
                      ))}
                    </div>
                    {newCustomer.tags.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs text-gray-600">
                          Selected tags: {newCustomer.tags.join(', ')}
                        </p>
                      </div>
                    )}
                    <p className="text-xs text-gray-500 mt-2">
                      Click tags to select/deselect them for organizing and targeting this customer
                    </p>
                  </div>
                </div>

                <div className="flex space-x-3 mt-8 pt-6 border-t border-gray-200 sticky bottom-0 bg-white">
                  <motion.button
                    type="button"
                    onClick={() => {
                      setShowModal(false);
                      resetForm();
                    }}
                    className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-medium transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Cancel
                  </motion.button>
                  <motion.button
                    type="submit"
                    disabled={!isFormValid()}
                    className={`flex-1 px-4 py-3 rounded-xl font-semibold transition-all shadow-lg relative overflow-hidden ${
                      isFormValid()
                        ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:from-indigo-700 hover:to-purple-700 hover:shadow-xl cursor-pointer'
                        : 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white cursor-not-allowed'
                    }`}
                    whileHover={isFormValid() ? { scale: 1.02, y: -1 } : {}}
                    whileTap={isFormValid() ? { scale: 0.98 } : {}}
                  >
                    {/* Disabled overlay that preserves gradient but adds opacity */}
                    {!isFormValid() && (
                      <div className="absolute inset-0 bg-white/60 backdrop-blur-[1px]" />
                    )}
                    <span className={`relative z-10 ${!isFormValid() ? 'opacity-70' : ''}`}>
                      {editingCustomer ? 'Update Customer' : 'Add Customer'}
                    </span>
                  </motion.button>
                </div>
              </form>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Import Modal */}
      {showImportModal && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-white rounded-2xl max-w-lg w-full p-6"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
          >
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Import Customers</h3>

            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-indigo-400 transition-colors">
                <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-2">Drop your CSV file here or click to upload</p>
                <p className="text-sm text-gray-500">Supported format: CSV with name, email, phone columns</p>
                <input type="file" className="hidden" accept=".csv" />
              </div>

              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">CSV Format Requirements:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Column headers: name, email, phone (optional)</li>
                  <li>• Maximum 1,000 customers per upload</li>
                  <li>• Duplicate emails will be skipped</li>
                </ul>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowImportModal(false)}
                className="flex-1 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancel
              </button>
              <button className="flex-1 bg-indigo-600 text-white py-2 rounded-xl font-medium hover:bg-indigo-700 transition-colors">
                Import Customers
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Enhanced Customer Engagement Modal */}
      {showEngagementModal && selectedCustomerForEngagement && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-green-600 to-emerald-600 px-6 py-4 text-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                    <Zap className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold">Engage {selectedCustomerForEngagement.name}</h3>
                    <p className="text-sm opacity-90">Customer engagement toolkit</p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setShowEngagementModal(false);
                    setSelectedCustomerForEngagement(null);
                  }}
                  className="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* Customer Quick Stats */}
              <div className="bg-gray-50 rounded-xl p-4 mb-6">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{selectedCustomerForEngagement.totalReferrals}</p>
                    <p className="text-sm text-gray-600">Total Referrals</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-600">${selectedCustomerForEngagement.totalEarnings}</p>
                    <p className="text-sm text-gray-600">Total Earned</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-blue-600">
                      {selectedCustomerForEngagement.status === 'active' ? 'Active' : 'Inactive'}
                    </p>
                    <p className="text-sm text-gray-600">Status</p>
                  </div>
                </div>
              </div>

              {/* Engagement Actions */}
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900 mb-3">Quick Actions</h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <motion.button
                    onClick={() => {
                      const subject = `Special reward for ${selectedCustomerForEngagement.name}`;
                      const body = `Hi ${selectedCustomerForEngagement.name},%0D%0A%0D%0AThank you for being a valued customer! We have a special reward waiting for you.%0D%0A%0D%0ABest regards,%0D%0AYour Business Team`;
                      window.open(`mailto:${selectedCustomerForEngagement.email}?subject=${subject}&body=${body}`);
                    }}
                    className="flex items-center p-4 bg-blue-50 text-blue-700 rounded-xl hover:bg-blue-100 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Mail className="w-6 h-6 mr-3" />
                    <div className="text-left">
                      <p className="font-medium">Send Personal Email</p>
                      <p className="text-sm text-blue-600">Personalized outreach</p>
                    </div>
                  </motion.button>

                  <motion.button
                    onClick={() => {
                      alert(`Creating exclusive voucher for ${selectedCustomerForEngagement.name}...`);
                      setShowEngagementModal(false);
                    }}
                    className="flex items-center p-4 bg-purple-50 text-purple-700 rounded-xl hover:bg-purple-100 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Gift className="w-6 h-6 mr-3" />
                    <div className="text-left">
                      <p className="font-medium">Create Exclusive Voucher</p>
                      <p className="text-sm text-purple-600">Just for this customer</p>
                    </div>
                  </motion.button>

                  <motion.button
                    onClick={() => {
                      alert(`Sending push notification to ${selectedCustomerForEngagement.name}...`);
                    }}
                    className="flex items-center p-4 bg-orange-50 text-orange-700 rounded-xl hover:bg-orange-100 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Bell className="w-6 h-6 mr-3" />
                    <div className="text-left">
                      <p className="font-medium">Send Notification</p>
                      <p className="text-sm text-orange-600">Push or SMS reminder</p>
                    </div>
                  </motion.button>

                  <motion.button
                    onClick={() => {
                      const amount = prompt('Enter reward bonus amount ($):');
                      if (amount && !isNaN(Number(amount))) {
                        alert(`Sending $${amount} bonus to ${selectedCustomerForEngagement.name}!`);
                        setShowEngagementModal(false);
                      }
                    }}
                    className="flex items-center p-4 bg-green-50 text-green-700 rounded-xl hover:bg-green-100 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <DollarSign className="w-6 h-6 mr-3" />
                    <div className="text-left">
                      <p className="font-medium">Send Bonus Reward</p>
                      <p className="text-sm text-green-600">Appreciation bonus</p>
                    </div>
                  </motion.button>
                </div>

                {/* Engagement Templates */}
                <div className="mt-6">
                  <h4 className="font-semibold text-gray-900 mb-3">Message Templates</h4>
                  <div className="space-y-3">
                    {[
                      {
                        title: "Re-engagement Campaign",
                        description: "Win back inactive customers",
                        template: "We miss you! Come back and get 20% off your next purchase."
                      },
                      {
                        title: "Referral Reminder",
                        description: "Encourage more referrals",
                        template: "Share with friends and earn $10 for each successful referral!"
                      },
                      {
                        title: "Thank You Message",
                        description: "Appreciate loyal customers",
                        template: "Thank you for being an amazing customer! Here's a special treat."
                      }
                    ].map((template, index) => (
                      <motion.div
                        key={index}
                        className="bg-gray-50 rounded-lg p-3 border border-gray-200 hover:border-indigo-200 hover:bg-indigo-50 transition-colors cursor-pointer"
                        whileHover={{ scale: 1.01 }}
                        onClick={() => {
                          const subject = template.title;
                          const body = `Hi ${selectedCustomerForEngagement.name},%0D%0A%0D%0A${template.template}%0D%0A%0D%0ABest regards,%0D%0AYour Business Team`;
                          window.open(`mailto:${selectedCustomerForEngagement.email}?subject=${subject}&body=${body}`);
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900">{template.title}</p>
                            <p className="text-sm text-gray-600">{template.description}</p>
                          </div>
                          <MessageSquare className="w-4 h-4 text-gray-400" />
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex space-x-3 mt-6 pt-6 border-t border-gray-200">
                <button
                  onClick={() => {
                    setShowEngagementModal(false);
                    setSelectedCustomerForEngagement(null);
                  }}
                  className="flex-1 py-3 text-gray-600 hover:text-gray-800 transition-colors font-medium"
                >
                  Close
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
};

export default CustomerManager;