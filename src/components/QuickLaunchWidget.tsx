
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Rocket, 
  Zap, 
  Target, 
  ArrowRight, 
  Sparkles,
  Coffee,
  ShoppingBag,
  Scissors,
  Car
} from 'lucide-react';

interface QuickTemplate {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  color: string;
  description: string;
  estimatedSetup: string;
  popularity: number;
}

const QuickLaunchWidget: React.FC<{ onLaunch: (template: QuickTemplate) => void }> = ({ onLaunch }) => {
  const [selectedTemplate, setSelectedTemplate] = useState<QuickTemplate | null>(null);

  const templates: QuickTemplate[] = [
    {
      id: 'coffee-shop',
      name: 'Coffee Shop Special',
      icon: Coffee,
      color: 'from-amber-500 to-orange-600',
      description: 'Perfect for cafes and coffee shops',
      estimatedSetup: '2 min',
      popularity: 95
    },
    {
      id: 'retail-discount',
      name: 'Retail Discount',
      icon: ShoppingBag,
      color: 'from-pink-500 to-rose-600',
      description: 'Great for retail and e-commerce',
      estimatedSetup: '3 min',
      popularity: 87
    },
    {
      id: 'salon-service',
      name: 'Salon & Beauty',
      icon: Scissors,
      color: 'from-purple-500 to-pink-600',
      description: 'Ideal for salons and beauty services',
      estimatedSetup: '2 min',
      popularity: 78
    },
    {
      id: 'auto-service',
      name: 'Auto Service',
      icon: Car,
      color: 'from-blue-500 to-indigo-600',
      description: 'Perfect for auto repair and services',
      estimatedSetup: '4 min',
      popularity: 72
    }
  ];

  return (
    <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-6 border border-indigo-200">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <div className="w-12 h-12 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center mr-4">
            <Rocket className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">Quick Launch</h3>
            <p className="text-gray-600 text-sm">Start a campaign in under 5 minutes</p>
          </div>
        </div>
        <motion.div
          className="bg-white px-3 py-1 rounded-full text-sm font-semibold text-indigo-600 flex items-center"
          animate={{ scale: [1, 1.05, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <Zap className="w-4 h-4 mr-1" />
          AI Powered
        </motion.div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-6">
        {templates.map((template) => {
          const IconComponent = template.icon;
          const isSelected = selectedTemplate?.id === template.id;

          return (
            <motion.div
              key={template.id}
              className={`relative p-4 rounded-xl border-2 cursor-pointer transition-all ${
                isSelected 
                  ? 'border-indigo-300 bg-white shadow-lg' 
                  : 'border-gray-200 bg-white/50 hover:border-gray-300'
              }`}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setSelectedTemplate(template)}
            >
              <div className="flex items-center mb-3">
                <div className={`w-10 h-10 bg-gradient-to-r ${template.color} rounded-lg flex items-center justify-center mr-3`}>
                  <IconComponent className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1">
                  <div className="font-semibold text-gray-900 text-sm">{template.name}</div>
                  <div className="text-xs text-gray-500">{template.estimatedSetup} setup</div>
                </div>
              </div>
              
              <p className="text-xs text-gray-600 mb-3 leading-relaxed">{template.description}</p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center text-xs text-gray-500">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
                  {template.popularity}% success rate
                </div>
                <div className="text-xs text-indigo-600 font-medium">Popular</div>
              </div>

              {isSelected && (
                <motion.div
                  className="absolute top-2 right-2 w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                >
                  <motion.div
                    className="w-2 h-2 bg-white rounded-full"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                </motion.div>
              )}
            </motion.div>
          );
        })}
      </div>

      {selectedTemplate && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl p-4 border border-gray-200"
        >
          <div className="flex items-center justify-between mb-3">
            <div className="font-semibold text-gray-900">Ready to launch {selectedTemplate.name}?</div>
            <div className="text-sm text-gray-500">~{selectedTemplate.estimatedSetup}</div>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            AI will pre-fill campaign details, voucher content, and target audience based on your business type.
          </p>
          <motion.button
            onClick={() => onLaunch(selectedTemplate)}
            className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-3 rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all flex items-center justify-center"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Sparkles className="w-5 h-5 mr-2" />
            Launch with AI
            <ArrowRight className="w-5 h-5 ml-2" />
          </motion.button>
        </motion.div>
      )}
    </div>
  );
};

export default QuickLaunchWidget;
