
import React from 'react';
import { motion } from 'framer-motion';
import { Trophy, Star, Target, Zap, Crown, Medal } from 'lucide-react';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  progress: number;
  maxProgress: number;
  unlocked: boolean;
  reward: string;
  color: string;
}

const ReferralAchievements: React.FC = () => {
  const achievements: Achievement[] = [
    {
      id: 'first-referral',
      title: 'First Steps',
      description: 'Make your first successful referral',
      icon: Star,
      progress: 1,
      maxProgress: 1,
      unlocked: true,
      reward: '$5 bonus',
      color: 'from-yellow-400 to-orange-500'
    },
    {
      id: 'network-builder',
      title: 'Network Builder',
      description: 'Build a network of 10 active referrers',
      icon: Target,
      progress: 7,
      maxProgress: 10,
      unlocked: false,
      reward: '$25 bonus',
      color: 'from-blue-500 to-purple-600'
    },
    {
      id: 'super-sharer',
      title: 'Super Sharer',
      description: 'Share 50 deals across platforms',
      icon: Zap,
      progress: 32,
      maxProgress: 50,
      unlocked: false,
      reward: 'Premium features',
      color: 'from-green-500 to-emerald-600'
    },
    {
      id: 'influence-master',
      title: 'Influence Master',
      description: 'Generate $1000 in referral revenue',
      icon: Crown,
      progress: 650,
      maxProgress: 1000,
      unlocked: false,
      reward: 'VIP status',
      color: 'from-purple-600 to-pink-600'
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Unlock Your Potential
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Complete challenges, earn badges, and unlock exclusive rewards as you grow your referral network
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {achievements.map((achievement, index) => {
            const IconComponent = achievement.icon;
            const progressPercentage = (achievement.progress / achievement.maxProgress) * 100;

            return (
              <motion.div
                key={achievement.id}
                className={`relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border-2 ${
                  achievement.unlocked 
                    ? 'border-green-200 bg-gradient-to-br from-green-50 to-emerald-50' 
                    : 'border-gray-200 hover:border-indigo-200'
                }`}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.03, y: -5 }}
              >
                {achievement.unlocked && (
                  <motion.div
                    className="absolute -top-3 -right-3 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.5, type: "spring", stiffness: 500 }}
                  >
                    <Trophy className="w-4 h-4 text-white" />
                  </motion.div>
                )}

                <motion.div 
                  className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-br ${achievement.color} rounded-2xl flex items-center justify-center shadow-lg`}
                  whileHover={{ rotate: 5, scale: 1.1 }}
                  transition={{ duration: 0.3 }}
                >
                  <IconComponent className="w-8 h-8 text-white" />
                </motion.div>

                <h3 className="text-xl font-bold text-gray-900 mb-2 text-center">
                  {achievement.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4 text-center">
                  {achievement.description}
                </p>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex justify-between text-sm text-gray-500 mb-2">
                    <span>Progress</span>
                    <span>{achievement.progress}/{achievement.maxProgress}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <motion.div
                      className={`h-2 bg-gradient-to-r ${achievement.color} rounded-full`}
                      initial={{ width: 0 }}
                      whileInView={{ width: `${progressPercentage}%` }}
                      transition={{ duration: 1.5, delay: index * 0.2 }}
                      viewport={{ once: true }}
                    />
                  </div>
                </div>

                <div className="text-center">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    achievement.unlocked 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    <Medal className="w-4 h-4 mr-1" />
                    {achievement.reward}
                  </span>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default ReferralAchievements;
