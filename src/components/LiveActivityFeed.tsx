
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, 
  Share2, 
  DollarSign, 
  Gift, 
  TrendingUp,
  MapPin,
  Clock,
  Zap
} from 'lucide-react';

interface Activity {
  id: string;
  type: 'share' | 'conversion' | 'signup' | 'milestone';
  message: string;
  user?: string;
  location?: string;
  amount?: number;
  timestamp: Date;
  avatar?: string;
}

const LiveActivityFeed: React.FC = () => {
  const [activities, setActivities] = useState<Activity[]>([
    {
      id: '1',
      type: 'conversion',
      message: 'New customer acquired',
      user: '<PERSON>',
      location: 'Downtown',
      amount: 45,
      timestamp: new Date(Date.now() - 30000),
      avatar: '/images/woman_blonde_smile.jpg'
    },
    {
      id: '2',
      type: 'share',
      message: 'shared coffee voucher',
      user: 'Mike <PERSON>.',
      timestamp: new Date(Date.now() - 120000),
      avatar: '/images/man_beard_casual.jpg'
    },
    {
      id: '3',
      type: 'milestone',
      message: 'Campaign reached 1000 shares!',
      timestamp: new Date(Date.now() - 300000)
    }
  ]);

  const [newActivityCount, setNewActivityCount] = useState(0);

  // Simulate real-time activities
  useEffect(() => {
    const interval = setInterval(() => {
      const newActivity: Activity = {
        id: Date.now().toString(),
        type: ['share', 'conversion', 'signup'][Math.floor(Math.random() * 3)] as any,
        message: [
          'shared your coffee voucher',
          'redeemed a voucher',
          'joined through referral',
          'made their first purchase'
        ][Math.floor(Math.random() * 4)],
        user: [
          'Emma K.', 'Alex P.', 'Jordan S.', 'Taylor M.', 'Casey L.'
        ][Math.floor(Math.random() * 5)],
        location: ['Downtown', 'Midtown', 'Uptown', 'West Side'][Math.floor(Math.random() * 4)],
        amount: Math.floor(Math.random() * 100) + 20,
        timestamp: new Date(),
        avatar: `/images/uifaces-human-image (${Math.floor(Math.random() * 11) + 1}).jpg`
      };

      setActivities(prev => [newActivity, ...prev.slice(0, 9)]);
      setNewActivityCount(prev => prev + 1);
    }, 8000 + Math.random() * 12000); // Random interval 8-20 seconds

    return () => clearInterval(interval);
  }, []);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'share': return Share2;
      case 'conversion': return DollarSign;
      case 'signup': return Users;
      case 'milestone': return TrendingUp;
      default: return Gift;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'share': return 'bg-blue-100 text-blue-600';
      case 'conversion': return 'bg-green-100 text-green-600';
      case 'signup': return 'bg-purple-100 text-purple-600';
      case 'milestone': return 'bg-yellow-100 text-yellow-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const formatTimeAgo = (date: Date) => {
    const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-4">
            <Zap className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">Live Activity</h2>
            <p className="text-gray-600 text-sm">Real-time customer interactions</p>
          </div>
        </div>
        {newActivityCount > 0 && (
          <motion.div
            className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            onClick={() => setNewActivityCount(0)}
          >
            {newActivityCount} new
          </motion.div>
        )}
      </div>

      <div className="space-y-3 max-h-96 overflow-y-auto">
        <AnimatePresence>
          {activities.map((activity, index) => {
            const IconComponent = getActivityIcon(activity.type);
            const colorClass = getActivityColor(activity.type);

            return (
              <motion.div
                key={activity.id}
                className="flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-50 transition-colors relative"
                initial={{ opacity: 0, x: -20, scale: 0.95 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                exit={{ opacity: 0, x: 20, scale: 0.95 }}
                transition={{ duration: 0.4, delay: index * 0.05 }}
                layout
              >
                {/* Pulse effect for new activities */}
                {index === 0 && newActivityCount > 0 && (
                  <motion.div
                    className="absolute inset-0 bg-green-100 rounded-xl"
                    initial={{ opacity: 0.8 }}
                    animate={{ opacity: 0 }}
                    transition={{ duration: 2 }}
                  />
                )}

                {/* Avatar or Icon */}
                <div className="flex-shrink-0">
                  {activity.avatar ? (
                    <div className="relative">
                      <img 
                        src={activity.avatar} 
                        alt={activity.user}
                        className="w-10 h-10 rounded-full border-2 border-white shadow-sm"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                          e.currentTarget.nextElementSibling!.style.display = 'flex';
                        }}
                      />
                      <div className={`w-10 h-10 rounded-full ${colorClass} items-center justify-center hidden`}>
                        <IconComponent className="w-5 h-5" />
                      </div>
                    </div>
                  ) : (
                    <div className={`w-10 h-10 rounded-full ${colorClass} flex items-center justify-center`}>
                      <IconComponent className="w-5 h-5" />
                    </div>
                  )}
                </div>

                {/* Activity Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    {activity.user && (
                      <span className="font-semibold text-gray-900">{activity.user}</span>
                    )}
                    <span className="text-gray-600 text-sm">{activity.message}</span>
                  </div>
                  
                  <div className="flex items-center space-x-3 text-xs text-gray-500">
                    <div className="flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {formatTimeAgo(activity.timestamp)}
                    </div>
                    {activity.location && (
                      <div className="flex items-center">
                        <MapPin className="w-3 h-3 mr-1" />
                        {activity.location}
                      </div>
                    )}
                  </div>
                </div>

                {/* Amount or Status */}
                {activity.amount && (
                  <div className="text-right">
                    <div className="font-bold text-green-600">${activity.amount}</div>
                    <div className="text-xs text-gray-500">revenue</div>
                  </div>
                )}
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* Activity Summary */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-blue-600">23</div>
            <div className="text-xs text-gray-500">Shares today</div>
          </div>
          <div>
            <div className="text-lg font-bold text-green-600">8</div>
            <div className="text-xs text-gray-500">Conversions</div>
          </div>
          <div>
            <div className="text-lg font-bold text-purple-600">$340</div>
            <div className="text-xs text-gray-500">Revenue</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveActivityFeed;
