
import React from 'react';
import { motion } from 'framer-motion';
import { Zap, Heart, DollarSign } from 'lucide-react';

const PageLoadingState: React.FC = () => {
  return (
    <motion.div 
      className="fixed inset-0 bg-white z-50 flex items-center justify-center"
      initial={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="text-center">
        {/* Logo animation */}
        <motion.div
          className="relative mb-8"
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <motion.div
            className="w-20 h-20 mx-auto bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-4 relative"
            animate={{ 
              boxShadow: [
                "0 0 0 0 rgba(16, 185, 129, 0.4)",
                "0 0 0 20px rgba(16, 185, 129, 0)",
                "0 0 0 0 rgba(16, 185, 129, 0)"
              ]
            }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <span className="text-white font-bold text-2xl">R</span>
          </motion.div>
          
          {/* Floating icons */}
          {[
            { icon: DollarSign, delay: 0, angle: 0 },
            { icon: Heart, delay: 0.5, angle: 120 },
            { icon: Zap, delay: 1, angle: 240 }
          ].map((item, index) => {
            const IconComponent = item.icon;
            const radius = 50;
            const x = Math.cos((item.angle * Math.PI) / 180) * radius;
            const y = Math.sin((item.angle * Math.PI) / 180) * radius;
            
            return (
              <motion.div
                key={index}
                className="absolute w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg"
                style={{
                  left: '50%',
                  top: '50%',
                  marginLeft: x - 16,
                  marginTop: y - 16
                }}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ 
                  scale: [0, 1.2, 1],
                  opacity: [0, 1, 1],
                  rotate: 360
                }}
                transition={{ 
                  duration: 1.5, 
                  delay: item.delay,
                  repeat: Infinity,
                  repeatDelay: 3
                }}
              >
                <IconComponent className="w-4 h-4 text-emerald-600" />
              </motion.div>
            );
          })}
        </motion.div>

        {/* Loading text */}
        <motion.h2 
          className="text-2xl font-bold text-gray-900 mb-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          Referit
        </motion.h2>
        
        <motion.p 
          className="text-gray-600 mb-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          Loading your earning potential...
        </motion.p>

        {/* Progress bar */}
        <div className="w-64 h-2 bg-gray-200 rounded-full mx-auto overflow-hidden">
          <motion.div
            className="h-full bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: "100%" }}
            transition={{ duration: 2, ease: "easeInOut" }}
          />
        </div>
      </div>
    </motion.div>
  );
};

export default PageLoadingState;
