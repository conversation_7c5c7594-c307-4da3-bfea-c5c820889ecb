
import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, DollarSign, Users, TrendingUp } from 'lucide-react';

// Memoized Statistics Card
export const StatCard = React.memo<{
  icon: React.ComponentType<any>;
  value: string;
  label: string;
  description: string;
  color: string;
  index: number;
}>(({ icon: Icon, value, label, description, color, index }) => (
  <motion.div
    className="group relative p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 overflow-hidden"
    initial={{ opacity: 0, y: 40 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6, delay: index * 0.1 }}
    viewport={{ once: true, margin: "-10%" }}
    whileHover={{ y: -8 }}
  >
    <div className={`absolute inset-0 bg-gradient-to-br ${color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />
    
    <div className={`w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-xl bg-gradient-to-r ${color} relative z-10`}>
      <Icon className="w-8 h-8 text-white" />
    </div>
    <div className={`text-5xl font-bold mb-2 bg-gradient-to-r ${color} bg-clip-text text-transparent`}>
      {value}
    </div>
    <div className="text-gray-900 font-semibold text-lg mb-2">{label}</div>
    <div className="text-gray-500 text-sm">{description}</div>
  </motion.div>
));

// Memoized Feature Card
export const FeatureCard = React.memo<{
  step: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  features: string[];
  index: number;
}>(({ step, title, description, icon: Icon, color, features, index }) => (
  <motion.div
    className="relative group text-center"
    initial={{ opacity: 0, y: 50 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ delay: index * 0.2, duration: 0.6 }}
    viewport={{ once: true }}
    whileHover={{ y: -8 }}
  >
    <motion.div 
      className="absolute -top-8 left-1/2 transform -translate-x-1/2 w-20 h-20 rounded-3xl bg-white border-4 border-gray-100 flex items-center justify-center text-lg font-bold text-gray-500 z-20 shadow-xl"
      whileHover={{ 
        scale: 1.1, 
        borderColor: "#10b981",
        boxShadow: "0 20px 40px rgba(16, 185, 129, 0.3)"
      }}
      transition={{ duration: 0.3 }}
    >
      <span className="text-gray-700">{step}</span>
    </motion.div>

    <div className="bg-white rounded-3xl p-8 pt-16 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-100 group-hover:border-emerald-200 h-full relative overflow-hidden">
      <motion.div
        className={`absolute inset-0 bg-gradient-to-br ${color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
      />

      <motion.div 
        className={`w-20 h-20 rounded-3xl flex items-center justify-center mx-auto mb-6 bg-gradient-to-r ${color} shadow-xl relative z-10`}
        whileHover={{ 
          scale: 1.1, 
          rotate: [0, -5, 5, 0],
          boxShadow: "0 25px 50px rgba(0,0,0,0.15)"
        }}
        transition={{ duration: 0.5 }}
      >
        <Icon className="w-10 h-10 text-white" />
      </motion.div>

      <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-emerald-700 transition-colors">
        {title}
      </h3>

      <p className="text-gray-600 leading-relaxed mb-6 text-lg">
        {description}
      </p>

      <div className="space-y-2">
        {features.map((feature, idx) => (
          <motion.div
            key={idx}
            className="flex items-center justify-center text-sm text-gray-500"
            initial={{ opacity: 0, x: -10 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 + idx * 0.1 }}
          >
            <CheckCircle className="w-4 h-4 text-emerald-500 mr-2" />
            {feature}
          </motion.div>
        ))}
      </div>
    </div>
  </motion.div>
));

// Memoized Social Platform Button
export const SocialPlatformButton = React.memo<{
  platform: {
    icon: React.ComponentType<any>;
    name: string;
    color: string;
  };
  index: number;
}>(({ platform, index }) => (
  <motion.button
    className={`w-12 h-12 rounded-full ${platform.color} text-white shadow-lg hover:shadow-xl transition-shadow flex items-center justify-center`}
    initial={{ opacity: 0, x: 20 }}
    animate={{ opacity: 1, x: 0 }}
    transition={{ delay: index * 0.1 }}
    whileHover={{ scale: 1.1 }}
    whileTap={{ scale: 0.9 }}
  >
    <platform.icon className="w-5 h-5" />
  </motion.button>
));

StatCard.displayName = 'StatCard';
FeatureCard.displayName = 'FeatureCard';
SocialPlatformButton.displayName = 'SocialPlatformButton';
