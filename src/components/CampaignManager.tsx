import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import QuickBusinessSetup from './QuickBusinessSetup';
import { useBusinessProfile } from '../contexts/BusinessProfileContext';
import { TagService, CustomerTag } from '../services/tagService';
import {
  Plus,
  Target,
  Gift,
  Eye,
  Edit3,
  Edit,
  Trash2,
  Play,
  Pause,
  Archive,
  Calendar,
  Users,
  DollarSign,
  TrendingUp,
  MapPin,
  Copy,
  Sparkles,
  BarChart3,
  Upload,
  Image as ImageIcon,
  Clock,
  CheckCircle,
  AlertCircle,
  X,
  Filter,
  ArrowRight,
  ChevronRight,
  Settings,
  Star,
  Zap,
  Sun,
  TreePine,
  DoorOpen,
  Globe,
  Search,
  Share2,
  Camera,
  Brain,
  Rocket,
  Tag,
  UtensilsCrossed,
  ShoppingBag,
  Heart,
  Briefcase,
  Music,
  Plane,
  Building
} from 'lucide-react';

interface Voucher {
  id: string;
  title: string;
  description: string;
  reward: string;
  classification?: string;
  tags?: string[];
  startDate: string;
  endDate: string;
  quantityLimit: number;
  currentRedemptions: number;
  redeemableSites: string[];
  status: 'draft' | 'published' | 'unpublished' | 'archived';
  shares: number;
  conversions: number;
  createdAt: string;
  imageUrl?: string;
  useAIImage?: boolean;
  offerValue?: number;
  restrictions?: string;
  targeting: {
    type: 'public' | 'private' | 'tagged';
    selectedCustomers?: string[];
    requiredTags?: string[];
    excludedTags?: string[];
  };
  publishedAt?: string;
  unpublishedAt?: string;
  archivedAt?: string;
  pendingRevisions?: number;
  latestRevisionId?: string;
}

interface Campaign {
  id: string;
  name: string;
  goal?: string;
  notes?: string;
  vouchers: Voucher[];
  createdAt: string;
  status: 'active' | 'paused' | 'completed';
  totalShares: number;
  totalConversions: number;
  totalRevenue: number;
}

interface BusinessLocation {
  id: string;
  name: string;
  address: string;
}

const CampaignManager: React.FC = () => {
  const { profile } = useBusinessProfile();
  
  // Get real business locations from profile, with fallback to mock data
  const businessLocations: BusinessLocation[] = React.useMemo(() => {
    if (profile?.locations && profile.locations.length > 0) {
      // Convert profile locations to the format expected by CampaignManager
      const profileLocations = profile.locations.map(loc => ({
        id: loc.id,
        name: loc.name,
        address: typeof loc.address === 'string' 
          ? loc.address 
          : `${loc.address.street_address}, ${loc.address.city}, ${loc.address.state} ${loc.address.postal_code}`
      }));

      // Add primary business location if it exists
      if (profile.businessName && profile.primaryAddress) {
        const primaryLocation = {
          id: 'primary-location',
          name: profile.businessName,
          address: typeof profile.primaryAddress === 'string'
            ? profile.primaryAddress
            : `${profile.primaryAddress.street_address}, ${profile.primaryAddress.city}, ${profile.primaryAddress.state} ${profile.primaryAddress.postal_code}`
        };
        return [primaryLocation, ...profileLocations];
      }

      return profileLocations;
    }
    
    // Fallback to mock data if no profile locations exist
    return [
      { id: 'main-location', name: 'Main Store', address: '123 Main St' },
      { id: 'downtown', name: 'Downtown Branch', address: '456 City Ave' }
    ];
  }, [profile]);

  const [campaigns, setCampaigns] = useState<Campaign[]>([
    {
      id: '1',
      name: 'Holiday Season Boost',
      goal: 'Increase foot traffic during holiday season',
      vouchers: [
        {
          id: 'v1',
          title: '25% Off Holiday Special',
          description: 'Get 25% off any holiday menu item when you bring a friend',
          reward: '$10 gift card to referrer',
          classification: 'food-beverage',
          tags: ['restaurant', 'holiday', 'discount'],
          startDate: '2024-12-01',
          endDate: '2024-12-31',
          quantityLimit: 500,
          currentRedemptions: 127,
          redeemableSites: ['main-location'],
          status: 'published',
          shares: 342,
          conversions: 127,
          createdAt: '2024-11-15',
          targeting: {
            type: 'public',
            selectedCustomers: [],
            requiredTags: [],
            excludedTags: []
          }
        }
      ],
      createdAt: '2024-11-15',
      status: 'active',
      totalShares: 342,
      totalConversions: 127,
      totalRevenue: 3850
    }
  ]);

  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [showCreateCampaign, setShowCreateCampaign] = useState(false);
  const [showCreateVoucher, setShowCreateVoucher] = useState(false);
  const [voucherPreview, setVoucherPreview] = useState<Voucher | null>(null);
  
  // Voucher filters state
  const [voucherFilters, setVoucherFilters] = useState({
    draft: true,
    published: true,
    unpublished: true,
    archived: false
  });

  const [newCampaign, setNewCampaign] = useState({
    name: '',
    goal: '',
    customGoal: '',
    notes: '',
    type: 'referral',
    targetAudience: '',
    budget: '',
    expectedOutcome: ''
  });

  const [newVoucher, setNewVoucher] = useState<Partial<Voucher>>({
    title: '',
    description: '',
    reward: '',
    classification: '',
    tags: [],
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    quantityLimit: 100,
    redeemableSites: businessLocations.map(loc => loc.id),
    status: 'draft',
    imageUrl: '',
    useAIImage: false,
    offerValue: undefined,
    restrictions: '',
    targeting: {
      type: 'public',
      selectedCustomers: [],
      requiredTags: [],
      excludedTags: []
    }
  });

  // Enhanced voucher classification system with consistent design using Lucide icons
  const voucherCategories = {
    'food-beverage': {
      label: 'Food & Beverage',
      icon: 'utensils-crossed',
      iconComponent: UtensilsCrossed,
      gradient: 'from-orange-500 to-red-500',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      textColor: 'text-orange-700',
      tags: ['restaurant', 'cafe', 'bar', 'delivery', 'takeout', 'happy-hour', 'brunch', 'dinner']
    },
    'retail-shopping': {
      label: 'Retail & Shopping',
      icon: 'shopping-bag',
      iconComponent: ShoppingBag,
      gradient: 'from-pink-500 to-rose-500',
      bgColor: 'bg-pink-50',
      borderColor: 'border-pink-200',
      textColor: 'text-pink-700',
      tags: ['clothing', 'electronics', 'home-goods', 'books', 'gifts', 'accessories', 'seasonal-sale']
    },
    'health-beauty': {
      label: 'Health & Beauty',
      icon: 'heart',
      iconComponent: Heart,
      gradient: 'from-purple-500 to-indigo-500',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      textColor: 'text-purple-700',
      tags: ['spa', 'salon', 'fitness', 'wellness', 'skincare', 'massage', 'yoga']
    },
    'services': {
      label: 'Professional Services',
      icon: 'briefcase',
      iconComponent: Briefcase,
      gradient: 'from-blue-500 to-cyan-500',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      textColor: 'text-blue-700',
      tags: ['consulting', 'repair', 'cleaning', 'legal', 'finance', 'photography', 'tutoring']
    },
    'entertainment': {
      label: 'Entertainment',
      icon: 'music',
      iconComponent: Music,
      gradient: 'from-green-500 to-emerald-500',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      textColor: 'text-green-700',
      tags: ['movies', 'concerts', 'games', 'events', 'sports', 'theater', 'nightlife']
    },
    'travel-hospitality': {
      label: 'Travel & Hospitality',
      icon: 'plane',
      iconComponent: Plane,
      gradient: 'from-teal-500 to-cyan-500',
      bgColor: 'bg-teal-50',
      borderColor: 'border-teal-200',
      textColor: 'text-teal-700',
      tags: ['hotel', 'flights', 'tours', 'accommodation', 'car-rental', 'vacation']
    }
  };

  const campaignTemplates = [
    {
      name: 'Grand Opening',
      goal: 'Drive awareness and foot traffic for new location',
      type: 'new_customer',
      icon: DoorOpen, // Perfect for grand opening - represents "open" sign
      targetAudience: 'Local community within 5 miles',
      expectedOutcome: 'Acquire 100+ new customers in first month'
    },
    {
      name: 'Holiday Season Boost',
      goal: 'Increase sales during holiday shopping season',
      type: 'seasonal',
      icon: TreePine, // Christmas tree for holiday season
      targetAudience: 'Existing customers and their networks',
      expectedOutcome: '25% increase in holiday revenue'
    },
    {
      name: 'Loyalty Rewards Program',
      goal: 'Reward existing customers and encourage referrals',
      type: 'loyalty',
      icon: CheckCircle, // Represents completion/achievement in loyalty context
      targetAudience: 'VIP customers and frequent buyers',
      expectedOutcome: 'Increase customer retention by 30%'
    },
    {
      name: 'Summer Flash Sale',
      goal: 'Clear inventory and drive quick sales',
      type: 'seasonal',
      icon: Sun, // Perfect for summer-themed campaigns
      targetAudience: 'Price-conscious shoppers',
      expectedOutcome: 'Move 500+ units in 2 weeks'
    }
  ];

  const [useAI, setUseAI] = useState(false);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [showLivePreview, setShowLivePreview] = useState(true);
  const [needsBusinessSetup, setNeedsBusinessSetup] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<any[]>([]);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [customerSearchTerm, setCustomerSearchTerm] = useState('');
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  // Subscription tier limits
  const subscriptionTiers = {
    free: { quantityLimit: 100, label: 'Free' },
    basic: { quantityLimit: 250, label: 'Basic' },
    premium: { quantityLimit: 1000, label: 'Premium' }
  };

  // Mock current user tier (this would come from user context in real app)
  const [currentTier, setCurrentTier] = useState<'free' | 'basic' | 'premium'>('free');

  // Mock customer data for targeting with enhanced attributes
  const customers = [
    { 
      id: '1', 
      name: 'Sarah Johnson', 
      email: '<EMAIL>', 
      tags: ['vip', 'frequent-buyer', 'coffee-lover', 'social-media-active'],
      location: 'Main Store',
      loyaltyTier: 'Gold',
      totalSpent: 1250,
      lastVisit: '2024-01-20'
    },
    { 
      id: '2', 
      name: 'Michael Chen', 
      email: '<EMAIL>', 
      tags: ['new-customer', 'tech-enthusiast', 'weekend-shopper'],
      location: 'Downtown Branch',
      loyaltyTier: 'Silver',
      totalSpent: 480,
      lastVisit: '2024-01-18'
    },
    { 
      id: '3', 
      name: 'Emma Rodriguez', 
      email: '<EMAIL>', 
      tags: ['vip', 'loyalty-member', 'referral-champion', 'high-value'],
      location: 'Main Store',
      loyaltyTier: 'Platinum',
      totalSpent: 2100,
      lastVisit: '2024-01-22'
    },
    { 
      id: '4', 
      name: 'James Wilson', 
      email: '<EMAIL>', 
      tags: ['weekend-shopper', 'mobile-user'],
      location: 'Downtown Branch',
      loyaltyTier: 'Bronze',
      totalSpent: 125,
      lastVisit: '2024-01-15'
    },
    { 
      id: '5', 
      name: 'Lisa Thompson', 
      email: '<EMAIL>', 
      tags: ['coffee-lover', 'morning-regular', 'email-subscriber'],
      location: 'Main Store',
      loyaltyTier: 'Silver',
      totalSpent: 890,
      lastVisit: '2024-01-21'
    }
  ];

  // Business tags from TagService
  const [availableTags, setAvailableTags] = useState<string[]>([]);
  const [businessTags, setBusinessTags] = useState<CustomerTag[]>([]);
  
  // Mock business ID - in real app this would come from auth context
  const businessId = 'business-123';

  useEffect(() => {
    initializeBusinessTags();
  }, []);

  const initializeBusinessTags = () => {
    const tags = TagService.getBusinessTags(businessId);
    setBusinessTags(tags);
    setAvailableTags(tags.map(tag => tag.name));
  };

  // Filtered customers for search
  const filteredCustomers = customers.filter(customer => 
    customer.name.toLowerCase().includes(customerSearchTerm.toLowerCase()) ||
    customer.email.toLowerCase().includes(customerSearchTerm.toLowerCase()) ||
    customer.tags.some(tag => tag.toLowerCase().includes(customerSearchTerm.toLowerCase()))
  );

  const createCampaign = () => {
    if (!newCampaign.name || !newCampaign.goal) {
      return;
    }

    const finalGoal = newCampaign.goal === "Custom goal" ? newCampaign.customGoal : newCampaign.goal;

    const campaign: Campaign = {
      id: Date.now().toString(),
      name: newCampaign.name,
      goal: finalGoal,
      notes: newCampaign.notes,
      vouchers: [],
      createdAt: new Date().toISOString(),
      status: 'active',
      totalShares: 0,
      totalConversions: 0,
      totalRevenue: 0
    };

    setCampaigns(prev => [campaign, ...prev]);
    setNewCampaign({ 
      name: '', 
      goal: '', 
      customGoal: '',
      notes: '', 
      type: 'referral', 
      targetAudience: '', 
      budget: '', 
      expectedOutcome: '' 
    });
    setShowAdvancedOptions(false);
    setShowCreateCampaign(false);
    setSelectedCampaign(campaign);

    // Show success toast instead of alert
    setTimeout(() => {
      setShowSuccessToast(true);
      setTimeout(() => setShowSuccessToast(false), 4000);
    }, 200);
  };

  const generateAISuggestions = () => {
    const suggestions = {
      title: 'Get 20% Off Your Next Visit',
      description: 'Bring a friend and both of you get 20% off your entire order. Perfect for trying our new seasonal menu!',
      reward: '$15 gift card when friend makes first purchase'
    };

    setNewVoucher(prev => ({
      ...prev,
      ...suggestions
    }));
  };

  const generateAIImage = async () => {
    if (!newVoucher.title && !newVoucher.description) {
      alert('Please add a title or description first to generate an AI image');
      return;
    }

    setIsGeneratingImage(true);

    setTimeout(() => {
      const imagePrompt = `${newVoucher.title} ${newVoucher.description}`.toLowerCase();
      let generatedImage = '';

      if (imagePrompt.includes('coffee') || imagePrompt.includes('cafe')) {
        generatedImage = '/images/coffee-latte.png';
      } else if (imagePrompt.includes('food') || imagePrompt.includes('restaurant')) {
        generatedImage = '/images/latte-voucher.png';
      } else {
        generatedImage = 'data:image/svg+xml;base64,' + btoa(`
          <svg width="400" height="200" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
              </linearGradient>
            </defs>
            <rect width="400" height="200" fill="url(#grad1)" />
            <text x="200" y="100" text-anchor="middle" fill="white" font-size="24" font-weight="bold">
              ${newVoucher.title || 'Special Offer'}
            </text>
          </svg>
        `);
      }

      setNewVoucher(prev => ({
        ...prev,
        imageUrl: generatedImage,
        useAIImage: true
      }));
      setIsGeneratingImage(false);
    }, 2000);
  };

  const createVoucher = () => {
    if (!selectedCampaign) {
      console.error('No campaign selected');
      return;
    }

    if (!newVoucher.title || !newVoucher.description || !newVoucher.reward) {
      console.error('Title, description, and referrer reward are required');
      return;
    }

    // Check quantity limit before creating voucher
    if ((newVoucher.quantityLimit || 0) > subscriptionTiers[currentTier].quantityLimit) {
      setShowUpgradeModal(true);
      return;
    }

    try {
      // Check if we're editing an existing voucher
      const isEditing = newVoucher.id;
      
      if (isEditing) {
        // Find the existing voucher
        const existingVoucher = selectedCampaign.vouchers.find(v => v.id === newVoucher.id);
        
        if (existingVoucher?.status === 'published') {
          // For published vouchers, create a revision instead of direct edit
          console.log('Creating revision for published voucher:', newVoucher.id);
          
          // Update the existing voucher with new data (simulating revision application)
          const updatedVoucher: Voucher = {
            ...existingVoucher,
            title: newVoucher.title,
            description: newVoucher.description,
            reward: newVoucher.reward.toString() || '',
            classification: newVoucher.classification || '',
            tags: newVoucher.tags || [],
            startDate: newVoucher.startDate || existingVoucher.startDate,
            endDate: newVoucher.endDate || existingVoucher.endDate,
            quantityLimit: Number(newVoucher.quantityLimit) || existingVoucher.quantityLimit,
            redeemableSites: newVoucher.redeemableSites && newVoucher.redeemableSites.length > 0 
              ? newVoucher.redeemableSites 
              : existingVoucher.redeemableSites,
            imageUrl: newVoucher.imageUrl || existingVoucher.imageUrl,
            useAIImage: newVoucher.useAIImage || existingVoucher.useAIImage,
            offerValue: newVoucher.offerValue || existingVoucher.offerValue,
            restrictions: newVoucher.restrictions || existingVoucher.restrictions,
            targeting: {
              type: newVoucher.targeting?.type || existingVoucher.targeting.type,
              selectedCustomers: newVoucher.targeting?.selectedCustomers || existingVoucher.targeting.selectedCustomers,
              requiredTags: newVoucher.targeting?.requiredTags || existingVoucher.targeting.requiredTags,
              excludedTags: newVoucher.targeting?.excludedTags || existingVoucher.targeting.excludedTags
            }
          };

          // Update campaigns state
          setCampaigns(prev => prev.map(campaign => 
            campaign.id === selectedCampaign.id
              ? { 
                  ...campaign, 
                  vouchers: campaign.vouchers.map(v => v.id === newVoucher.id ? updatedVoucher : v)
                }
              : campaign
          ));

          // Update selected campaign state
          setSelectedCampaign(prev => prev ? {
            ...prev,
            vouchers: prev.vouchers.map(v => v.id === newVoucher.id ? updatedVoucher : v)
          } : prev);

          console.log('Published voucher updated via revision:', updatedVoucher);
        } else {
          // For draft/unpublished vouchers, allow direct editing
          const updatedVoucher: Voucher = {
            ...existingVoucher,
            title: newVoucher.title,
            description: newVoucher.description,
            reward: newVoucher.reward.toString() || '',
            classification: newVoucher.classification || '',
            tags: newVoucher.tags || [],
            startDate: newVoucher.startDate || existingVoucher.startDate,
            endDate: newVoucher.endDate || existingVoucher.endDate,
            quantityLimit: Number(newVoucher.quantityLimit) || existingVoucher.quantityLimit,
            redeemableSites: newVoucher.redeemableSites && newVoucher.redeemableSites.length > 0 
              ? newVoucher.redeemableSites 
              : existingVoucher.redeemableSites,
            status: voucherSaveOption === 'publish' ? 'published' : existingVoucher.status,
            publishedAt: voucherSaveOption === 'publish' && !existingVoucher.publishedAt ? new Date().toISOString() : existingVoucher.publishedAt,
            imageUrl: newVoucher.imageUrl || existingVoucher.imageUrl,
            useAIImage: newVoucher.useAIImage || existingVoucher.useAIImage,
            offerValue: newVoucher.offerValue || existingVoucher.offerValue,
            restrictions: newVoucher.restrictions || existingVoucher.restrictions,
            targeting: {
              type: newVoucher.targeting?.type || existingVoucher.targeting.type,
              selectedCustomers: newVoucher.targeting?.selectedCustomers || existingVoucher.targeting.selectedCustomers,
              requiredTags: newVoucher.targeting?.requiredTags || existingVoucher.targeting.requiredTags,
              excludedTags: newVoucher.targeting?.excludedTags || existingVoucher.targeting.excludedTags
            }
          };

          // Update campaigns state
          setCampaigns(prev => prev.map(campaign => 
            campaign.id === selectedCampaign.id
              ? { 
                  ...campaign, 
                  vouchers: campaign.vouchers.map(v => v.id === newVoucher.id ? updatedVoucher : v)
                }
              : campaign
          ));

          // Update selected campaign state
          setSelectedCampaign(prev => prev ? {
            ...prev,
            vouchers: prev.vouchers.map(v => v.id === newVoucher.id ? updatedVoucher : v)
          } : prev);

          console.log('Voucher updated successfully:', updatedVoucher);
        }
      } else {
        // Creating a new voucher
        const voucher: Voucher = {
          id: Date.now().toString(),
          title: newVoucher.title,
          description: newVoucher.description,
          reward: newVoucher.reward.toString() || '',
          classification: newVoucher.classification || '',
          tags: newVoucher.tags || [],
          startDate: newVoucher.startDate || new Date().toISOString().split('T')[0],
          endDate: newVoucher.endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          quantityLimit: Number(newVoucher.quantityLimit) || 100,
          currentRedemptions: 0,
          redeemableSites: newVoucher.redeemableSites && newVoucher.redeemableSites.length > 0 
            ? newVoucher.redeemableSites 
            : businessLocations.map(loc => loc.id),
          status: voucherSaveOption === 'publish' ? 'published' : 'draft',
          shares: 0,
          conversions: 0,
          createdAt: new Date().toISOString(),
          publishedAt: voucherSaveOption === 'publish' ? new Date().toISOString() : undefined,
          imageUrl: newVoucher.imageUrl || '',
          useAIImage: newVoucher.useAIImage || false,
          offerValue: newVoucher.offerValue,
          restrictions: newVoucher.restrictions || '',
          targeting: {
            type: newVoucher.targeting?.type || 'public',
            selectedCustomers: newVoucher.targeting?.selectedCustomers || [],
            requiredTags: newVoucher.targeting?.requiredTags || [],
            excludedTags: newVoucher.targeting?.excludedTags || []
          }
        };

        setCampaigns(prev => prev.map(campaign => 
          campaign.id === selectedCampaign.id
            ? { 
                ...campaign, 
                vouchers: [...campaign.vouchers, voucher],
                totalShares: campaign.totalShares,
                totalRevenue: campaign.totalRevenue
              }
            : campaign
        ));

        // Update selected campaign state
        setSelectedCampaign(prev => prev ? {
          ...prev,
          vouchers: [...prev.vouchers, voucher]
        } : prev);

        console.log('Voucher created successfully:', voucher);
      }

      // Reset form
      setNewVoucher({
        title: '',
        description: '',
        reward: '',
        classification: '',
        tags: [],
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        quantityLimit: 100,
        redeemableSites: businessLocations.map(loc => loc.id),
        status: 'draft',
        imageUrl: '',
        useAIImage: false,
        offerValue: undefined,
        restrictions: '',
        targeting: {
          type: 'public',
          selectedCustomers: [],
          requiredTags: [],
          excludedTags: []
        }
      });

      setVoucherSaveOption('draft'); // Reset to draft
      setShowCreateVoucher(false);
      setShowLivePreview(false);
    } catch (error) {
      console.error('Error creating/updating voucher:', error);
    }
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'active':
      case 'published':
        return { bg: 'bg-emerald-50', text: 'text-emerald-700', border: 'border-emerald-200', icon: CheckCircle };
      case 'draft':
        return { bg: 'bg-amber-50', text: 'text-amber-700', border: 'border-amber-200', icon: Clock };
      case 'unpublished':
        return { bg: 'bg-blue-50', text: 'text-blue-700', border: 'border-blue-200', icon: Pause };
      case 'archived':
        return { bg: 'bg-gray-50', text: 'text-gray-700', border: 'border-gray-200', icon: Archive };
      default:
        return { bg: 'bg-gray-50', text: 'text-gray-700', border: 'border-gray-200', icon: AlertCircle };
    }
  };

  // Enhanced confirmation dialog state
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    type: 'publish' | 'unpublish' | 'archive';
    voucher: Voucher | null;
    onConfirm: () => void;
  }>({
    isOpen: false,
    type: 'publish',
    voucher: null,
    onConfirm: () => {}
  });

  const [voucherSaveOption, setVoucherSaveOption] = useState<'draft' | 'publish'>('draft');

  // Voucher lifecycle management functions
  const publishVoucher = async (voucher: Voucher) => {
    setConfirmDialog({
      isOpen: true,
      type: 'publish',
      voucher,
      onConfirm: async () => {
        try {
          const updatedVoucher = { ...voucher, status: 'published' as const, publishedAt: new Date().toISOString() };
          
          setCampaigns(prev => prev.map(campaign => 
            campaign.id === selectedCampaign?.id
              ? {
                  ...campaign,
                  vouchers: campaign.vouchers.map(v => 
                    v.id === voucher.id ? updatedVoucher : v
                  )
                }
              : campaign
          ));

          setSelectedCampaign(prev => prev ? {
            ...prev,
            vouchers: prev.vouchers.map(v => 
              v.id === voucher.id ? updatedVoucher : v
            )
          } : prev);

          setConfirmDialog(prev => ({ ...prev, isOpen: false }));
          console.log('Voucher published successfully');
        } catch (error) {
          console.error('Error publishing voucher:', error);
        }
      }
    });
  };

  const unpublishVoucher = async (voucher: Voucher) => {
    setConfirmDialog({
      isOpen: true,
      type: 'unpublish',
      voucher,
      onConfirm: async () => {
        try {
          const updatedVoucher = { ...voucher, status: 'unpublished' as const, unpublishedAt: new Date().toISOString() };
          
          setCampaigns(prev => prev.map(campaign => 
            campaign.id === selectedCampaign?.id
              ? {
                  ...campaign,
                  vouchers: campaign.vouchers.map(v => 
                    v.id === voucher.id ? updatedVoucher : v
                  )
                }
              : campaign
          ));

          setSelectedCampaign(prev => prev ? {
            ...prev,
            vouchers: prev.vouchers.map(v => 
              v.id === voucher.id ? updatedVoucher : v
            )
          } : prev);

          setConfirmDialog(prev => ({ ...prev, isOpen: false }));
          console.log('Voucher unpublished successfully');
        } catch (error) {
          console.error('Error unpublishing voucher:', error);
        }
      }
    });
  };

  const archiveVoucher = async (voucher: Voucher) => {
    if (!['draft', 'unpublished'].includes(voucher.status)) {
      return;
    }

    setConfirmDialog({
      isOpen: true,
      type: 'archive',
      voucher,
      onConfirm: async () => {
        try {
          const updatedVoucher = { ...voucher, status: 'archived' as const, archivedAt: new Date().toISOString() };
          
          setCampaigns(prev => prev.map(campaign => 
            campaign.id === selectedCampaign?.id
              ? {
                  ...campaign,
                  vouchers: campaign.vouchers.map(v => 
                    v.id === voucher.id ? updatedVoucher : v
                  )
                }
              : campaign
          ));

          setSelectedCampaign(prev => prev ? {
            ...prev,
            vouchers: prev.vouchers.map(v => 
              v.id === voucher.id ? updatedVoucher : v
            )
          } : prev);

          setConfirmDialog(prev => ({ ...prev, isOpen: false }));
          console.log('Voucher archived successfully');
        } catch (error) {
          console.error('Error archiving voucher:', error);
        }
      }
    });
  };

  const canEditVoucher = (voucher: Voucher) => {
    // Allow editing of draft, unpublished, and published vouchers
    // Published vouchers will create revisions instead of direct edits
    return ['draft', 'unpublished', 'published'].includes(voucher.status);
  };

  const canPublishVoucher = (voucher: Voucher) => {
    return ['draft', 'unpublished'].includes(voucher.status);
  };

  const canUnpublishVoucher = (voucher: Voucher) => {
    return voucher.status === 'published';
  };

  const canArchiveVoucher = (voucher: Voucher) => {
    return ['draft', 'unpublished'].includes(voucher.status);
  };

  // Filter vouchers based on selected filters
  const filteredVouchers = selectedCampaign?.vouchers.filter(voucher => {
    return voucherFilters[voucher.status as keyof typeof voucherFilters];
  }) || [];

  // Toggle filter function
  const toggleFilter = (status: keyof typeof voucherFilters) => {
    setVoucherFilters(prev => ({
      ...prev,
      [status]: !prev[status]
    }));
  };

  const VoucherPreviewModal = ({ voucher, onClose }: { voucher: Voucher; onClose: () => void }) => {
    const category = voucher.classification ? voucherCategories[voucher.classification] : null;

    return (
      <motion.div
        className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            onClose();
          }
        }}
      >
        <motion.div
          className="bg-white rounded-3xl max-w-md w-full overflow-hidden shadow-2xl"
          initial={{ scale: 0.9, y: 20 }}
          animate={{ scale: 1, y: 0 }}
          exit={{ scale: 0.9, y: 20 }}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4 text-white">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Voucher Preview</h3>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onClose();
                }}
                className="p-1 hover:bg-white/20 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <p className="text-indigo-100 text-sm mt-1">How customers will see this offer</p>
          </div>

          {/* Preview Content */}
          <div className="p-6">
            <div className="bg-white border-2 border-gray-100 rounded-2xl overflow-hidden shadow-sm">
              {/* Voucher Image */}
              {voucher.imageUrl && (
                <div className="h-32 relative overflow-hidden">
                  <img 
                    src={voucher.imageUrl} 
                    alt={voucher.title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.parentElement!.style.background = category?.gradient 
                        ? `linear-gradient(135deg, ${category.gradient.replace('from-', '').replace('to-', ', ')})` 
                        : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent flex items-center justify-center">
                    <Gift className="w-8 h-8 text-white drop-shadow-lg" />
                  </div>
                  {/* Referrer Reward Badge */}
                  {voucher.reward && (
                    <div className="absolute top-3 right-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg border-2 border-white">
                      <Gift className="w-3 h-3 inline mr-1" />
                      Earn ${voucher.reward}
                    </div>
                  )}
                </div>
              )}

              <div className="p-5">
                <div className="flex items-center justify-between mb-3">
                  <span className="inline-flex items-center px-3 py-1 bg-emerald-50 text-emerald-700 rounded-full text-xs font-medium border border-emerald-200">
                    <Sparkles className="w-3 h-3 mr-1" />
                    Limited Time
                  </span>
                  <span className="text-xs text-gray-500 font-medium">
                    Expires {new Date(voucher.endDate).toLocaleDateString()}
                  </span>
                </div>

                <h4 className="text-xl font-bold text-gray-900 mb-2 leading-tight">{voucher.title}</h4>
                <p className="text-sm text-gray-600 mb-4 leading-relaxed">{voucher.description}</p>

                <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 mb-4">
                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div className="text-center">
                      <div className="text-gray-500 mb-1">Quantity Left</div>
                      <div className="font-bold text-gray-900">{voucher.quantityLimit - voucher.currentRedemptions}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-gray-500 mb-1">Duration Left</div>
                      <div className="font-bold text-gray-900">
                        {Math.ceil((new Date(voucher.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days
                      </div>
                    </div>
                  </div>
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-1" />
                        <span>{voucher.currentRedemptions}/{voucher.quantityLimit} claimed</span>
                      </div>
                      <div className="flex items-center">
                        <TrendingUp className="w-4 h-4 mr-1" />
                        <span>{voucher.shares} shares</span>
                      </div>
                    </div>
                  </div>
                </div>

                <button className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-3 rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02]">
                  Share This Deal
                </button>
              </div>
            </div>

            <button
              onClick={onClose}
              className="w-full mt-4 bg-gray-100 text-gray-700 py-3 rounded-xl font-medium hover:bg-gray-200 transition-colors"
            >
              Close Preview
            </button>
          </div>
        </motion.div>
      </motion.div>
    );
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {!selectedCampaign ? (
        /* Campaign Overview */
        <div className="space-y-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">Campaign Manager</h1>
              <p className="text-lg text-gray-600">Create and manage your referral campaigns</p>
            </div>
            <motion.button
              onClick={() => {
                // Check if business profile is complete
                const businessProfile = localStorage.getItem('business_profile');
                if (!businessProfile) {
                  setNeedsBusinessSetup(true);
                  return;
                }
                setShowCreateCampaign(true);
              }}
              className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white px-8 py-4 rounded-2xl font-bold hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 flex items-center shadow-xl hover:shadow-2xl transform hover:scale-[1.02] border-2 border-emerald-500/20"
              whileHover={{ y: -3, boxShadow: "0 20px 40px rgba(16, 185, 129, 0.3)" }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="w-6 h-6 mr-3 bg-white/20 rounded-lg flex items-center justify-center">
                <Target className="w-4 h-4" />
              </div>
              <div className="text-left">
                <div className="text-sm font-bold">New Campaign</div>
                <div className="text-xs text-emerald-100">Strategy & goals</div>
              </div>
            </motion.button>
          </div>

          {/* Enhanced Campaigns Grid */}
          <div className="grid gap-8">
            {campaigns.map((campaign, index) => {
              const statusConfig = getStatusConfig(campaign.status);
              const StatusIcon = statusConfig.icon;

              return (
                <motion.div
                  key={campaign.id}
                  className="group bg-white rounded-3xl shadow-lg border border-gray-100 p-8 cursor-pointer hover:shadow-2xl transition-all duration-500 hover:border-indigo-200"
                  onClick={() => setSelectedCampaign(campaign)}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ y: -4, scale: 1.01 }}
                >
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                        <Target className="w-8 h-8 text-white" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900 group-hover:text-indigo-600 transition-colors">{campaign.name}</h3>
                        <p className="text-gray-600 mt-1">{campaign.goal}</p>
                      </div>
                    </div>
                    <div className={`flex items-center px-4 py-2 rounded-full text-sm font-medium ${statusConfig.bg} ${statusConfig.text} ${statusConfig.border} border`}>
                      <StatusIcon className="w-4 h-4 mr-2" />
                      {campaign.status}
                    </div>
                  </div>

                  <div className="grid grid-cols-4 gap-6 mb-6">
                    {[
                      { label: 'Vouchers', value: campaign.vouchers.length, icon: Gift, color: 'text-indigo-600' },
                      { label: 'Total Shares', value: campaign.totalShares, icon: TrendingUp, color: 'text-emerald-600' },
                      { label: 'Conversions', value: campaign.totalConversions, icon: Users, color: 'text-purple-600' },
                      { label: 'Revenue', value: `$${campaign.totalRevenue.toLocaleString()}`, icon: DollarSign, color: 'text-amber-600' }
                    ].map((stat, i) => {
                      const StatIcon = stat.icon;
                      return (
                        <div key={i} className="text-center bg-gray-50 rounded-2xl p-4 group-hover:bg-gradient-to-br group-hover:from-gray-50 group-hover:to-indigo-50 transition-all duration-300">
                          <StatIcon className={`w-6 h-6 mx-auto mb-2 ${stat.color}`} />
                          <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                          <p className="text-sm text-gray-600 font-medium">{stat.label}</p>
                        </div>
                      );
                    })}
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500 bg-gray-50 rounded-xl p-4">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-2" />
                      <span>Created {new Date(campaign.createdAt).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center">
                      <BarChart3 className="w-4 h-4 mr-2" />
                      <span>Performance: Excellent</span>
                    </div>
                  </div>
                </motion.div>
              );
            })
            }
          </div>
        </div>
      ) : (
        /* Enhanced Campaign Detail View */
        <div className="space-y-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <motion.button
                onClick={() => setSelectedCampaign(null)}
                className="p-3 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-xl transition-all"
                whileHover={{ x: -4 }}
                whileTap={{ scale: 0.95 }}
              >
                ← Back
              </motion.button>
              <div>
                <h1 className="text-4xl font-bold text-gray-900">{selectedCampaign.name}</h1>
                <p className="text-lg text-gray-600 mt-1">{selectedCampaign.goal}</p>
              </div>
            </div>
            <motion.button
              onClick={() => setShowCreateVoucher(true)}
              className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-2xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:scale-[1.02] border border-indigo-400/30"
              whileHover={{ y: -2, boxShadow: "0 15px 30px rgba(99, 102, 241, 0.25)" }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="w-6 h-6 mr-3 bg-white/20 rounded-lg flex items-center justify-center">
                <Gift className="w-4 h-4" />
              </div>
              <div className="text-left">
                <div className="text-sm font-semibold">Add Voucher</div>
                <div className="text-xs text-indigo-200">Offer & rewards</div>
              </div>
            </motion.button>
          </div>

          {/* Enhanced Campaign Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[
              { icon: Gift, title: "Active Vouchers", value: selectedCampaign.vouchers.length, change: "+12%", color: "indigo" },
              { icon: TrendingUp, title: "Total Shares", value: selectedCampaign.totalShares, change: "+23%", color: "emerald" },
              { icon: Users, title: "Conversions", value: selectedCampaign.totalConversions, change: "+18%", color: "purple" },
              { icon: DollarSign, title: "Revenue", value: `$${selectedCampaign.totalRevenue.toLocaleString()}`, change: "+31%", color: "amber" }
            ].map((stat, index) => {
              const StatIcon = stat.icon;
              return (
                <motion.div
                  key={index}
                  className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ y: -2, scale: 1.02 }}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 rounded-xl bg-${stat.color}-100 flex items-center justify-center`}>
                      <StatIcon className={`w-6 h-6 text-${stat.color}-600`} />
                    </div>
                    <span className="text-sm text-emerald-600 font-semibold bg-emerald-50 px-2 py-1 rounded-lg">
                      {stat.change}
                    </span>
                  </div>
                  <h3 className="text-gray-600 text-sm font-medium mb-1">{stat.title}</h3>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </motion.div>
              );
            })}
          </div>

          {/* Enhanced Vouchers List */}
          <div className="bg-white rounded-3xl shadow-lg border border-gray-100">
            <div className="p-8 border-b border-gray-100">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Campaign Vouchers</h2>
                <div className="flex items-center space-x-3">
                  <span className="text-sm text-gray-500">{selectedCampaign.vouchers.length} total vouchers</span>
                </div>
              </div>

              {/* Voucher Status Filters */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Filter className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700 mr-3">Show:</span>
                  
                  {/* Filter Buttons */}
                  <div className="flex items-center space-x-2">
                    {Object.entries({
                      draft: { label: 'Draft', icon: Clock, color: 'amber' },
                      published: { label: 'Published', icon: CheckCircle, color: 'emerald' },
                      unpublished: { label: 'Unpublished', icon: Pause, color: 'blue' },
                      archived: { label: 'Archived', icon: Archive, color: 'gray' }
                    }).map(([status, config]) => {
                      const IconComponent = config.icon;
                      const isActive = voucherFilters[status as keyof typeof voucherFilters];
                      const count = selectedCampaign.vouchers.filter(v => v.status === status).length;
                      
                      return (
                        <motion.button
                          key={status}
                          onClick={() => toggleFilter(status as keyof typeof voucherFilters)}
                          className={`flex items-center px-3 py-2 rounded-xl text-sm font-medium transition-all duration-300 border-2 ${
                            isActive
                              ? `bg-${config.color}-100 text-${config.color}-700 border-${config.color}-300 shadow-sm`
                              : 'bg-gray-50 text-gray-500 border-gray-200 hover:bg-gray-100 hover:border-gray-300'
                          }`}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          title={`${isActive ? 'Hide' : 'Show'} ${config.label.toLowerCase()} vouchers`}
                        >
                          <IconComponent className="w-4 h-4 mr-2" />
                          <span>{config.label}</span>
                          {count > 0 && (
                            <span className={`ml-2 px-2 py-0.5 rounded-full text-xs font-bold ${
                              isActive
                                ? `bg-${config.color}-200 text-${config.color}-800`
                                : 'bg-gray-200 text-gray-600'
                            }`}>
                              {count}
                            </span>
                          )}
                        </motion.button>
                      );
                    })}
                  </div>
                </div>

                {/* Filter Summary */}
                <div className="flex items-center space-x-3 text-sm">
                  <span className="text-gray-500">
                    Showing {filteredVouchers.length} of {selectedCampaign.vouchers.length}
                  </span>
                  {filteredVouchers.length !== selectedCampaign.vouchers.length && (
                    <motion.button
                      onClick={() => setVoucherFilters({
                        draft: true,
                        published: true,
                        unpublished: true,
                        archived: false
                      })}
                      className="text-indigo-600 hover:text-indigo-700 font-medium transition-colors"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Reset filters
                    </motion.button>
                  )}
                </div>
              </div>
            </div>

            <div className="divide-y divide-gray-100">
              {filteredVouchers.length === 0 && selectedCampaign.vouchers.length === 0 ? (
                <div className="p-16 text-center">
                  <motion.div
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.2, type: "spring" }}
                    className="mb-8"
                  >
                    <div className="w-24 h-24 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-3xl flex items-center justify-center mx-auto mb-6">
                      <Gift className="w-12 h-12 text-indigo-600" />
                    </div>
                    <h3 className="text-3xl font-bold text-gray-900 mb-4">Ready to Create Your First Voucher?</h3>
                    <p className="text-lg text-gray-600 mb-8 max-w-lg mx-auto leading-relaxed">
                      Vouchers are the heart of your campaign. They're what customers will share to earn rewards and bring you new business.
                    </p>
                  </motion.div>

                  <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-10">
                    {[
                      { icon: Target, title: "Define Your Offer", desc: "Create an irresistible deal that customers want to share" },
                      { icon: Users, title: "Set Rewards", desc: "Decide what referrers earn when they bring new customers" },
                      { icon: TrendingUp, title: "Watch It Grow", desc: "Track shares, conversions, and revenue in real-time" }
                    ].map((step, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 + index * 0.1 }}
                        className="bg-gradient-to-br from-gray-50 to-indigo-50 rounded-2xl p-6 border border-gray-100"
                      >
                        <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <step.icon className="w-6 h-6 text-white" />
                        </div>
                        <h4 className="text-lg font-bold text-gray-900 mb-2">{step.title}</h4>
                        <p className="text-sm text-gray-600 leading-relaxed">{step.desc}</p>
                      </motion.div>
                    ))}
                  </div>

                  <motion.button
                    onClick={() => setShowCreateVoucher(true)}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-10 py-4 rounded-2xl font-bold text-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-[1.02] flex items-center mx-auto"
                    whileHover={{ y: -2 }}
                    whileTap={{ scale: 0.98 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                  >
                    <Plus className="w-6 h-6 mr-3" />
                    Create Your First Voucher
                    <ArrowRight className="w-5 h-5 ml-3" />
                  </motion.button>

                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1 }}
                    className="text-sm text-gray-500 mt-6"
                  >
                    💡 Tip: Start with a simple discount offer to test the waters
                  </motion.p>
                </div>
              ) : filteredVouchers.length === 0 ? (
                <div className="p-16 text-center">
                  <motion.div
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.2, type: "spring" }}
                    className="mb-8"
                  >
                    <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-indigo-100 rounded-3xl flex items-center justify-center mx-auto mb-6">
                      <Filter className="w-12 h-12 text-gray-400" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">No Vouchers Match Your Filters</h3>
                    <p className="text-lg text-gray-600 mb-8 max-w-lg mx-auto leading-relaxed">
                      Try adjusting your filter settings to see more vouchers, or create a new voucher for this campaign.
                    </p>
                  </motion.div>

                  <motion.button
                    onClick={() => setVoucherFilters({
                      draft: true,
                      published: true,
                      unpublished: true,
                      archived: true
                    })}
                    className="bg-gray-100 text-gray-700 px-6 py-3 rounded-xl font-medium hover:bg-gray-200 transition-colors mr-4"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Show All Vouchers
                  </motion.button>

                  <motion.button
                    onClick={() => setShowCreateVoucher(true)}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-3 rounded-xl font-medium hover:from-indigo-700 hover:to-purple-700 transition-all"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Create New Voucher
                  </motion.button>
                </div>
              ) : (
                filteredVouchers.map((voucher, index) => {
                  const statusConfig = getStatusConfig(voucher.status);
                  const StatusIcon = statusConfig.icon;
                  const category = voucher.classification ? voucherCategories[voucher.classification] : null;

                  return (
                    <motion.div
                      key={voucher.id}
                      className={`p-8 transition-all duration-300 group ${
                        voucher.status === 'archived' 
                          ? 'opacity-60 hover:opacity-80 bg-gray-50/50 hover:bg-gray-100/50' 
                          : 'hover:bg-gray-50'
                      }`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center space-x-4">
                              <h3 className={`text-xl font-bold transition-colors ${
                                voucher.status === 'archived' 
                                  ? 'text-gray-500 group-hover:text-gray-600' 
                                  : 'text-gray-900 group-hover:text-indigo-600'
                              }`}>
                                {voucher.title}
                                {voucher.status === 'archived' && (
                                  <span className="ml-2 text-sm font-normal text-gray-400">(Archived)</span>
                                )}
                              </h3>
                              <div className={`flex items-center px-3 py-1 rounded-full text-xs font-medium ${statusConfig.bg} ${statusConfig.text} ${statusConfig.border} border`}>
                                <StatusIcon className="w-3 h-3 mr-1" />
                                {voucher.status}
                              </div>
                              
                              {/* Pending revisions indicator */}
                              {voucher.status === 'published' && voucher.pendingRevisions && voucher.pendingRevisions > 0 && (
                                <div className="flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-50 text-orange-700 border border-orange-200">
                                  <Edit className="w-3 h-3 mr-1" />
                                  {voucher.pendingRevisions} pending edit{voucher.pendingRevisions !== 1 ? 's' : ''}
                                </div>
                              )}

                              {/* Auto-expiry warnings */}
                              {voucher.status === 'published' && (
                                <>
                                  {new Date(voucher.endDate) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) && (
                                    <div className="flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-50 text-yellow-700 border border-yellow-200">
                                      <Clock className="w-3 h-3 mr-1" />
                                      Expires soon
                                    </div>
                                  )}
                                  {voucher.currentRedemptions >= voucher.quantityLimit * 0.9 && (
                                    <div className="flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-50 text-red-700 border border-red-200">
                                      <AlertCircle className="w-3 h-3 mr-1" />
                                      Low stock
                                    </div>
                                  )}
                                </>
                              )}
                            </div>
                            {category && (
                              <div className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${category.bgColor} ${category.textColor} ${category.borderColor} border`}>
                                <category.iconComponent className="w-4 h-4 mr-2" />
                                <span>{category.label}</span>
                              </div>
                            )}
                          </div>

                          <p className={`mb-4 leading-relaxed ${
                            voucher.status === 'archived' ? 'text-gray-400' : 'text-gray-600'
                          }`}>
                            {voucher.description}
                          </p>

                          {voucher.tags && voucher.tags.length > 0 && (
                            <div className="flex flex-wrap gap-2 mb-4">
                              {voucher.tags.slice(0, 4).map((tag, tagIndex) => (
                                <span
                                  key={tagIndex}
                                  className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium hover:bg-gray-200 transition-colors"
                                >
                                  #{tag}
                                </span>
                              ))}
                              {voucher.tags.length > 4 && (
                                <span className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                                  +{voucher.tags.length - 4} more
                                </span>
                              )}
                            </div>
                          )}

                          <div className={`grid grid-cols-2 md:grid-cols-4 gap-6 rounded-xl p-4 ${
                            voucher.status === 'archived' ? 'bg-gray-100/50' : 'bg-gray-50'
                          }`}>
                            {[
                              { label: 'Shares', value: voucher.shares, icon: TrendingUp },
                              { label: 'Conversions', value: voucher.conversions, icon: Users },
                              { label: 'Redemptions', value: `${voucher.currentRedemptions}/${voucher.quantityLimit}`, icon: Gift },
                              { label: 'Expires', value: new Date(voucher.endDate).toLocaleDateString(), icon: Calendar }
                            ].map((metric, i) => {
                              const MetricIcon = metric.icon;
                              return (
                                <div key={i} className="text-center">
                                  <MetricIcon className={`w-4 h-4 mx-auto mb-1 ${
                                    voucher.status === 'archived' ? 'text-gray-400' : 'text-gray-500'
                                  }`} />
                                  <p className={`text-sm mb-1 ${
                                    voucher.status === 'archived' ? 'text-gray-400' : 'text-gray-500'
                                  }`}>
                                    {metric.label}
                                  </p>
                                  <p className={`font-bold ${
                                    voucher.status === 'archived' ? 'text-gray-500' : 'text-gray-900'
                                  }`}>
                                    {metric.value}
                                  </p>
                                </div>
                              );
                            })}
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 ml-8">
                          {/* Preview button */}
                          <motion.button
                            onClick={() => setVoucherPreview(voucher)}
                            className="p-3 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-xl transition-all"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            title="Preview voucher"
                          >
                            <Eye className="w-5 h-5" />
                          </motion.button>

                          {/* Edit button - for draft/unpublished/published */}
                          {canEditVoucher(voucher) && (
                            <motion.button
                              onClick={() => {
                                setNewVoucher({
                                  id: voucher.id, // Store the original voucher ID for revision handling
                                  title: voucher.title,
                                  description: voucher.description,
                                  reward: voucher.reward,
                                  classification: voucher.classification,
                                  tags: voucher.tags,
                                  startDate: voucher.startDate,
                                  endDate: voucher.endDate,
                                  quantityLimit: voucher.quantityLimit,
                                  redeemableSites: voucher.redeemableSites,
                                  status: voucher.status,
                                  imageUrl: voucher.imageUrl,
                                  useAIImage: voucher.useAIImage,
                                  targeting: voucher.targeting
                                });
                                setShowCreateVoucher(true);
                              }}
                              className="p-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-xl transition-all"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              title={voucher.status === 'published' ? 'Edit voucher (will create revision)' : 'Edit voucher'}
                            >
                              <Edit3 className="w-5 h-5" />
                            </motion.button>
                          )}

                          {/* Publish button - for draft/unpublished */}
                          {canPublishVoucher(voucher) && (
                            <motion.button
                              onClick={() => publishVoucher(voucher)}
                              className="p-3 text-gray-600 hover:text-emerald-600 hover:bg-emerald-50 rounded-xl transition-all"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              title="Publish voucher"
                            >
                              <Play className="w-5 h-5" />
                            </motion.button>
                          )}

                          {/* Unpublish button - for published */}
                          {canUnpublishVoucher(voucher) && (
                            <motion.button
                              onClick={() => unpublishVoucher(voucher)}
                              className="p-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-xl transition-all"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              title="Unpublish voucher"
                            >
                              <Pause className="w-5 h-5" />
                            </motion.button>
                          )}

                          {/* Archive button - for draft/unpublished */}
                          {canArchiveVoucher(voucher) && (
                            <motion.button
                              onClick={() => archiveVoucher(voucher)}
                              className="p-3 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-xl transition-all"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              title="Archive voucher"
                            >
                              <Archive className="w-5 h-5" />
                            </motion.button>
                          )}

                          {/* Duplicate button */}
                          <motion.button
                            onClick={() => {
                              setNewVoucher({
                                title: `${voucher.title} (Copy)`,
                                description: voucher.description,
                                reward: voucher.reward,
                                classification: voucher.classification,
                                tags: voucher.tags,
                                startDate: new Date().toISOString().split('T')[0],
                                endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                                quantityLimit: voucher.quantityLimit,
                                redeemableSites: voucher.redeemableSites,
                                status: 'draft',
                                imageUrl: voucher.imageUrl,
                                useAIImage: voucher.useAIImage,
                                targeting: voucher.targeting
                              });
                              setShowCreateVoucher(true);
                            }}
                            className="p-3 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-xl transition-all"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            title="Duplicate voucher"
                          >
                            <Copy className="w-5 h-5" />
                          </motion.button>
                        </div>
                      </div>
                    </motion.div>
                  );
                })
              )}
            </div>
          </div>
        </div>
      )}

      {/* Redesigned Create Campaign Modal */}
      <AnimatePresence>
        {showCreateCampaign && (
          <motion.div
            className="fixed inset-0 bg-gradient-to-br from-black/70 via-black/60 to-black/70 backdrop-blur-lg flex items-start justify-center z-50 p-4 pt-8 overflow-y-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowCreateCampaign(false);
              }
            }}
          >
            <motion.div
              className="bg-white rounded-3xl max-w-5xl w-full min-h-fit max-h-[calc(100vh-4rem)] overflow-hidden shadow-2xl border border-gray-100 flex flex-col my-4"
              initial={{ scale: 0.9, opacity: 0, y: 30 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.9, opacity: 0, y: 30 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Enhanced Header with Gradient */}
              <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 px-8 py-6 text-white relative overflow-hidden">
                <div className="absolute inset-0 bg-black/10"></div>
                <div className="relative flex items-center justify-between">
                  <div>
                    <h3 className="text-3xl font-bold text-white mb-2">Create New Campaign</h3>
                    <p className="text-indigo-100 text-lg">Design your referral strategy with precision</p>
                  </div>
                  <motion.button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setShowCreateCampaign(false);
                    }}
                    className="relative p-4 hover:bg-white/20 rounded-xl transition-all duration-300 backdrop-blur-sm cursor-pointer touch-manipulation"
                    style={{ minWidth: '48px', minHeight: '48px' }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <X className="w-6 h-6 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                  </motion.button>
                </div>

                {/* Decorative Elements */}
                <div className="absolute -right-12 -top-12 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
                <div className="absolute -left-8 -bottom-8 w-24 h-24 bg-white/5 rounded-full blur-lg"></div>
              </div>

              <div className="overflow-y-auto flex-1">
                <div className="p-8 space-y-8">
                  {/* Campaign Template Selection */}
                  <div>
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h4 className="text-2xl font-bold text-gray-900">Choose Your Campaign Type</h4>
                        <p className="text-gray-600 mt-1">Select a proven template or start from scratch</p>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <Sparkles className="w-4 h-4" />
                        <span>AI-Powered Templates</span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
                      {campaignTemplates.map((template, index) => (
                        <motion.div
                          key={index}
                          className={`group border-2 rounded-xl p-5 cursor-pointer transition-all duration-300 hover:shadow-lg ${
                            newCampaign.name === template.name
                              ? 'border-indigo-500 bg-gradient-to-br from-indigo-50 to-purple-50 shadow-md'
                              : 'border-gray-200 hover:border-indigo-300 bg-white hover:bg-gradient-to-br hover:from-gray-50 hover:to-indigo-50'
                          }`}
                          onClick={() => setNewCampaign(prev => ({
                            ...prev,
                            name: template.name,
                            goal: template.goal,
                            type: template.type,
                            targetAudience: template.targetAudience,
                            expectedOutcome: template.expectedOutcome
                          }))}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          whileHover={{ y: -2 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <div className="w-12 h-12 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <template.icon className="w-6 h-6 text-indigo-600" />
                              </div>
                              <div>
                                <h5 className="text-lg font-bold text-gray-900 group-hover:text-indigo-600 transition-colors">
                                  {template.name}
                                </h5>
                              </div>
                            </div>
                            {newCampaign.name === template.name && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center"
                              >
                                <CheckCircle className="w-4 h-4 text-white" />
                              </motion.div>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 leading-relaxed mb-3">{template.goal}</p>
                          <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                            <div className="text-xs">
                              <span className="text-gray-500">Ideal for:</span>
                              <span className="text-gray-700 font-medium ml-2">{template.targetAudience}</span>
                            </div>
                            <div className="text-xs">
                              <span className="text-gray-500">Typical results:</span>
                              <span className="text-gray-700 font-medium ml-2">{template.expectedOutcome}</span>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>

                    {/* Custom Campaign Option */}
                    <motion.div
                      className={`border-2 border-dashed rounded-2xl p-6 cursor-pointer transition-all duration-300 hover:scale-[1.02] ${
                        newCampaign.name && !campaignTemplates.some(t => t.name === newCampaign.name)
                          ? 'border-purple-400 bg-gradient-to-br from-purple-50 to-pink-50 shadow-lg'
                          : 'border-gray-300 hover:border-purple-400 bg-gray-50 hover:bg-gradient-to-br hover:from-purple-50 hover:to-pink-50'
                      }`}
                      onClick={() => setNewCampaign(prev => ({
                        ...prev,
                        name: '',
                        goal: '',
                        type: 'referral',
                        targetAudience: '',
                        expectedOutcome: ''
                      }))}
                      whileHover={{ y: -2 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl flex items-center justify-center">
                          <Plus className="w-8 h-8 text-purple-600" />
                        </div>
                        <div className="flex-1">
                          <h5 className="text-xl font-bold text-gray-900">Custom Campaign</h5>
                          <p className="text-gray-600 mt-2">Build your unique strategy from the ground up with complete customization</p>
                        </div>
                        <ArrowRight className="w-6 h-6 text-purple-600" />
                      </div>
                    </motion.div>
                  </div>

                  {/* Campaign Configuration */}
                  <div className="bg-gray-50 rounded-3xl p-8 space-y-8">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <Settings className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h4 className="text-2xl font-bold text-gray-900">Campaign Configuration</h4>
                        <p className="text-gray-600">Fine-tune your campaign details</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                      <div className="space-y-6">
                        <div>
                          <label className="block text-sm font-bold text-gray-800 mb-3">
                            Campaign Name <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            value={newCampaign.name}
                            onChange={(e) => setNewCampaign(prev => ({ ...prev, name: e.target.value }))}
                            className="w-full px-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500 transition-all duration-300 text-lg font-medium placeholder-gray-400"
                            placeholder="e.g., Spring Grand Opening Celebration"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-bold text-gray-800 mb-3">Campaign Type</label>
                          <select
                            value={newCampaign.type}
                            onChange={(e) => setNewCampaign(prev => ({ ...prev, type: e.target.value }))}
                            className="w-full px-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500 transition-all duration-300 text-lg font-medium"
                          >
                            <option value="referral">🎯 Referral Campaign</option>
                            <option value="loyalty">⭐ Loyalty Program</option>
                            <option value="seasonal">🎄 Seasonal Promotion</option>
                            <option value="new_customer">🎉 New Customer Acquisition</option>
                            <option value="retention">💝 Customer Retention</option>
                            <option value="reactivation">🔄 Customer Reactivation</option>
                          </select>
                        </div>
                      </div>

                      <div className="space-y-6">
                        <div>
                          <label className="block text-sm font-bold text-gray-800 mb-3">
                            Primary Goal <span className="text-red-500">*</span>
                          </label>
                          <select
                            value={newCampaign.goal}
                            onChange={(e) => setNewCampaign(prev => ({ ...prev, goal: e.target.value }))}
                            className="w-full px-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500 transition-all duration-300 text-lg font-medium"
                          >
                            <option value="">Select your primary objective...</option>
                            <option value="Increase foot traffic">🚶 Increase foot traffic</option>
                            <option value="Boost online sales">💻 Boost online sales</option>
                            <option value="Build brand awareness">📢 Build brand awareness</option>
                            <option value="Customer retention">🤝 Improve customer retention</option>
                            <option value="New customer acquisition">👥 Acquire new customers</option>
                            <option value="Launch new product">🚀 Launch new product/service</option>
                            <option value="Clear inventory">📦 Clear seasonal inventory</option>
                            <option value="Custom goal">✨ Custom goal (specify below)</option>
                          </select>

                          {newCampaign.goal === "Custom goal" && (
                            <motion.textarea
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              value={newCampaign.customGoal || ''}
                              onChange={(e) => setNewCampaign(prev => ({ ...prev, customGoal: e.target.value }))}
                              rows={4}
                              className="w-full px-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500 transition-all duration-300 resize-none mt-4 text-lg font-medium placeholder-gray-400"
                              placeholder="Describe your specific campaign objective in detail..."
                            />
                          )}
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-bold text-gray-800 mb-3">Campaign Notes (Optional)</label>
                      <textarea
                        value={newCampaign.notes}
                        onChange={(e) => setNewCampaign(prev => ({ ...prev, notes: e.target.value }))}
                        rows={4}
                        className="w-full px-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500 transition-all duration-300 resize-none text-lg font-medium placeholder-gray-400"
                        placeholder="Add any additional context, target metrics, or special considerations for this campaign..."
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Footer - Fixed positioning */}
              <div className="border-t border-gray-200 bg-gradient-to-r from-gray-50 to-indigo-50 px-8 py-6 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                      <span>Ready to launch your campaign</span>
                    </div>
                  </div>
                  <div className="flex space-x-4">
                    <motion.button
                      onClick={() => setShowCreateCampaign(false)}
                      className="px-6 py-3 text-gray-700 hover:text-gray-900 transition-all duration-300 font-semibold rounded-xl hover:bg-white/50"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Cancel
                    </motion.button>
                    <motion.button
                      onClick={createCampaign}
                      disabled={!newCampaign.name || !newCampaign.goal}
                      className="px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-bold hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                      whileHover={{ y: -2, scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-center space-x-2">
                        <Target className="w-4 h-4" />
                        <span>Create Campaign</span>
                        <ChevronRight className="w-4 h-4" />
                      </div>
                    </motion.button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Enhanced Create Voucher Modal */}
      <AnimatePresence>
        {showCreateVoucher && (
          <motion.div
            className="fixed inset-0 bg-gradient-to-br from-black/70 via-black/60 to-black/70 backdrop-blur-lg flex items-start justify-center z-50 p-4 pt-8 overflow-y-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowCreateVoucher(false);
              }
            }}
          >
            <motion.div
              className="bg-white rounded-3xl max-w-7xl w-full min-h-fit max-h-[calc(100vh-4rem)] overflow-hidden shadow-2xl border border-gray-100 flex flex-col my-4"
              initial={{ scale: 0.9, opacity: 0, y: 30 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.9, opacity: 0, y: 30 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Enhanced Header with Gradient */}
              <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 px-8 py-6 text-white relative overflow-hidden">
                <div className="absolute inset-0 bg-black/10"></div>
                <div className="relative flex items-center justify-between">
                  <div>
                    <h3 className="text-3xl font-bold text-white mb-2">
                      {newVoucher.id ? (
                        selectedCampaign?.vouchers.find(v => v.id === newVoucher.id)?.status === 'published' 
                          ? 'Edit Live Voucher' 
                          : 'Edit Voucher'
                      ) : 'Create Irresistible Voucher'}
                    </h3>
                    <p className="text-indigo-100 text-lg">
                      {newVoucher.id ? (
                        selectedCampaign?.vouchers.find(v => v.id === newVoucher.id)?.status === 'published' 
                          ? 'Your changes will be applied immediately to the live voucher'
                          : 'Make changes to your voucher design'
                      ) : 'Design offers that customers can\'t resist sharing'}
                    </p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2 text-sm text-indigo-100">
                      <Sparkles className="w-4 h-4" />
                      <span>AI-Powered Design</span>
                    </div>
                    <motion.button
                      onClick={() => setUseAI(!useAI)}
                      className={`px-4 py-2 rounded-xl text-sm font-medium transition-all backdrop-blur-sm ${
                        useAI ? 'bg-white/25 text-white border border-white/20' : 'bg-white/15 text-indigo-100 hover:bg-white/25'
                      }`}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Brain className="w-4 h-4 inline mr-2" />
                      AI Assistant
                    </motion.button>
                    {useAI && (
                      <motion.button
                        onClick={generateAISuggestions}
                        className="px-4 py-2 bg-white text-indigo-600 rounded-xl text-sm font-medium hover:bg-gray-100 transition-colors shadow-lg"
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Rocket className="w-4 h-4 inline mr-2" />
                        Generate Ideas
                      </motion.button>
                    )}
                    <motion.button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setShowCreateVoucher(false);
                      }}
                      className="relative p-4 hover:bg-white/20 rounded-xl transition-all duration-300 backdrop-blur-sm cursor-pointer touch-manipulation"
                      style={{ minWidth: '48px', minHeight: '48px' }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <X className="w-6 h-6 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                    </motion.button>
                  </div>
                </div>

                {/* Decorative Elements */}
                <div className="absolute -right-12 -top-12 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
                <div className="absolute -left-8 -bottom-8 w-24 h-24 bg-white/5 rounded-full blur-lg"></div>
              </div>

              <div className="overflow-y-auto flex-1">
                <div className="p-8 space-y-8">
                  <div className={`${showLivePreview ? 'grid lg:grid-cols-3 gap-8' : ''}`}>
                    {/* Enhanced Form Section */}
                    <div className={`${showLivePreview ? 'lg:col-span-2' : ''} space-y-8`}>
                      {/* Basic Details Section */}
                      <div className="bg-gray-50 rounded-3xl p-8 border border-gray-200">
                        <div className="flex items-center space-x-3 mb-6">
                          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                            <Gift className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h4 className="text-2xl font-bold text-gray-900">Offer Details</h4>
                            <p className="text-gray-600">Create a compelling offer that drives action</p>
                          </div>
                        </div>

                        <div className="space-y-6">
                          <div>
                            <label className="block text-sm font-bold text-gray-800 mb-3">
                              Offer Title <span className="text-red-500">*</span>
                            </label>
                            <input
                              type="text"
                              value={newVoucher.title || ''}
                              onChange={(e) => setNewVoucher(prev => ({ ...prev, title: e.target.value }))}
                              className="w-full px-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500 transition-all duration-300 text-lg font-medium placeholder-gray-400"
                              placeholder="e.g., Get 25% Off Your Next Coffee + Free Pastry"
                            />
                            <p className="text-xs text-gray-500 mt-2">Make it specific and benefit-focused. Numbers grab attention!</p>
                          </div>

                          <div>
                            <label className="block text-sm font-bold text-gray-800 mb-3">
                              Detailed Description <span className="text-red-500">*</span>
                            </label>
                            <textarea
                              value={newVoucher.description || ''}
                              onChange={(e) => setNewVoucher(prev => ({ ...prev, description: e.target.value }))}
                              rows={4}
                              className="w-full px-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500 transition-all duration-300 resize-none text-lg font-medium placeholder-gray-400"
                              placeholder="Describe the offer, any conditions, and why it's valuable. Be clear about what customers get and any restrictions."
                            />
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-gray-800 mb-3">Offer Value (Optional)</label>
                              <div className="relative">
                                <DollarSign className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                <input
                                  type="number"
                                  value={newVoucher.offerValue || ''}
                                  onChange={(e) => setNewVoucher(prev => ({ ...prev, offerValue: parseFloat(e.target.value) }))}
                                  className="w-full pl-12 pr-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500 transition-all duration-300 text-lg font-medium placeholder-gray-400"
                                  placeholder="25.00"
                                />
                              </div>
                              <p className="text-xs text-gray-500 mt-2">Track revenue impact of this offer</p>
                            </div>

                            <div>
                              <label className="block text-sm font-bold text-gray-800 mb-3">
                                Referrer Reward <span className="text-red-500">*</span>
                              </label>
                              <div className="relative">
                                <DollarSign className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                <input
                                  type="number"
                                  min="0"
                                  step="0.01"
                                  value={newVoucher.reward || ''}
                                  onChange={(e) => setNewVoucher(prev => ({ ...prev, reward: e.target.value }))}
                                  className="w-full pl-12 pr-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500 transition-all duration-300 text-lg font-medium placeholder-gray-400"
                                  placeholder="15.00"
                                />
                              </div>
                              <p className="text-xs text-emerald-600 mt-2 font-medium">⚡ This reward drives your entire referral program - make it irresistible!</p>
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-bold text-gray-800 mb-3">
                              Voucher Restrictions (Optional)
                            </label>
                            <textarea
                              value={newVoucher.restrictions || ''}
                              onChange={(e) => setNewVoucher(prev => ({ ...prev, restrictions: e.target.value }))}
                              rows={3}
                              className="w-full px-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500 transition-all duration-300 resize-none text-lg font-medium placeholder-gray-400"
                              placeholder="e.g., Valid Mon-Thu only. Cannot be combined with other offers. One per customer. Expires 30 days from issue."
                            />
                            <p className="text-xs text-gray-500 mt-2">Fine print, limitations, and legal terms. Keep it clear and fair.</p>
                          </div>
                        </div>
                      </div>

                      {/* Target Audience Section */}
                      <div className="bg-gray-50 rounded-3xl p-8 border border-gray-200">
                        <div className="flex items-center space-x-3 mb-6">
                          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                            <Target className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h4 className="text-2xl font-bold text-gray-900">Target Audience</h4>
                            <p className="text-gray-600">Choose who can access this voucher</p>
                          </div>
                        </div>

                        <div className="space-y-6">
                          {/* Audience Type Selection */}
                          <div>
                            <label className="block text-sm font-bold text-gray-800 mb-4">Audience Type</label>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              {[
                                { 
                                  type: 'public', 
                                  title: 'All Customers', 
                                  desc: 'Anyone can access this voucher',
                                  icon: Globe,
                                  color: 'from-blue-500 to-indigo-600'
                                },
                                { 
                                  type: 'tagged', 
                                  title: 'Specific Segments', 
                                  desc: 'Target by customer tags',
                                  icon: Filter,
                                  color: 'from-purple-500 to-pink-600'
                                },
                                { 
                                  type: 'private', 
                                  title: 'Selected Customers', 
                                  desc: 'Hand-picked customers only',
                                  icon: Users,
                                  color: 'from-emerald-500 to-teal-600'
                                }
                              ].map((audienceType) => {
                                const IconComponent = audienceType.icon;
                                return (
                                  <motion.div
                                    key={audienceType.type}
                                    className={`border-2 rounded-2xl p-6 cursor-pointer transition-all duration-300 ${
                                      newVoucher.targeting?.type === audienceType.type
                                        ? 'border-emerald-500 bg-emerald-50 shadow-lg'
                                        : 'border-gray-200 hover:border-emerald-300 bg-white hover:shadow-md'
                                    }`}
                                    onClick={() => setNewVoucher(prev => ({
                                      ...prev,
                                      targeting: { ...prev.targeting, type: audienceType.type as any }
                                    }))}
                                    whileHover={{ y: -2, scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                  >
                                    <div className="text-center">
                                      <div className={`w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-to-r ${audienceType.color} flex items-center justify-center shadow-lg`}>
                                        <IconComponent className="w-6 h-6 text-white" />
                                      </div>
                                      <h5 className="text-lg font-bold text-gray-900 mb-2">{audienceType.title}</h5>
                                      <p className="text-sm text-gray-600">{audienceType.desc}</p>
                                    </div>
                                  </motion.div>
                                );
                              })}
                            </div>
                          </div>

                          {/* Conditional targeting options */}
                          {newVoucher.targeting?.type === 'tagged' && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              className="space-y-6"
                            >
                              {/* Customer Tags with Categories */}
                              <div>
                                <label className="block text-sm font-bold text-gray-800 mb-3">Customer Tags</label>
                                
                                {businessTags.length > 0 ? (
                                  <div className="space-y-4">
                                    {/* Group tags by category */}
                                    {['lifecycle', 'value', 'behavior', 'engagement', 'demographics', 'custom'].map(category => {
                                      const categoryTags = businessTags.filter(tag => tag.category === category);
                                      if (categoryTags.length === 0) return null;
                                      
                                      const categoryLabels = {
                                        lifecycle: '🔄 Customer Journey',
                                        value: '💎 Value Tier',
                                        behavior: '📊 Behavior',
                                        engagement: '🚀 Engagement',
                                        demographics: '👥 Demographics',
                                        custom: '🔧 Custom Tags'
                                      };
                                      
                                      return (
                                        <div key={category}>
                                          <p className="text-xs font-medium text-gray-600 mb-2">
                                            {categoryLabels[category as keyof typeof categoryLabels]}
                                          </p>
                                          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
                                            {categoryTags.map((tag) => (
                                              <motion.button
                                                key={tag.id}
                                                type="button"
                                                onClick={() => {
                                                  const currentTags = newVoucher.targeting?.requiredTags || [];
                                                  if (currentTags.includes(tag.name)) {
                                                    setNewVoucher(prev => ({
                                                      ...prev,
                                                      targeting: {
                                                        ...prev.targeting!,
                                                        requiredTags: currentTags.filter(t => t !== tag.name)
                                                      }
                                                    }));
                                                  } else {
                                                    setNewVoucher(prev => ({
                                                      ...prev,
                                                      targeting: {
                                                        ...prev.targeting!,
                                                        requiredTags: [...currentTags, tag.name]
                                                      }
                                                    }));
                                                  }
                                                }}
                                                className={`px-4 py-3 rounded-xl text-sm font-medium transition-all border-2 ${
                                                  (newVoucher.targeting?.requiredTags || []).includes(tag.name)
                                                    ? 'bg-emerald-100 text-emerald-700 border-emerald-300'
                                                    : 'bg-white text-gray-600 hover:bg-gray-50 border-gray-200 hover:border-gray-300'
                                                }`}
                                                whileHover={{ scale: 1.05 }}
                                                whileTap={{ scale: 0.95 }}
                                                title={tag.description}
                                              >
                                                {tag.name.replace('-', ' ')}
                                                {tag.isDefault && (
                                                  <span className="ml-1 text-xs opacity-75">✨</span>
                                                )}
                                              </motion.button>
                                            ))}
                                          </div>
                                        </div>
                                      );
                                    })}
                                  </div>
                                ) : (
                                  <div className="bg-gray-50 border border-gray-200 rounded-xl p-6 text-center">
                                    <Target className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                                    <p className="text-sm text-gray-600 mb-2">No customer tags available</p>
                                    <p className="text-xs text-gray-500">Create customer tags to enable targeted vouchers</p>
                                  </div>
                                )}
                              </div>

                              {/* Location Filters */}
                              <div>
                                <label className="block text-sm font-bold text-gray-800 mb-3">Location Preferences</label>
                                <div className="grid grid-cols-2 gap-3">
                                  {['Main Store', 'Downtown Branch'].map((location) => (
                                    <motion.button
                                      key={location}
                                      type="button"
                                      onClick={() => {
                                        const currentTags = newVoucher.targeting?.requiredTags || [];
                                        const locationTag = `location-${location.toLowerCase().replace(' ', '-')}`;
                                        if (currentTags.includes(locationTag)) {
                                          setNewVoucher(prev => ({
                                            ...prev,
                                            targeting: {
                                              ...prev.targeting!,
                                              requiredTags: currentTags.filter(t => t !== locationTag)
                                            }
                                          }));
                                        } else {
                                          setNewVoucher(prev => ({
                                            ...prev,
                                            targeting: {
                                              ...prev.targeting!,
                                              requiredTags: [...currentTags, locationTag]
                                            }
                                          }));
                                        }
                                      }}
                                      className={`px-4 py-3 rounded-xl text-sm font-medium transition-all border-2 flex items-center ${
                                        (newVoucher.targeting?.requiredTags || []).includes(`location-${location.toLowerCase().replace(' ', '-')}`)
                                          ? 'bg-blue-100 text-blue-700 border-blue-300'
                                          : 'bg-white text-gray-600 hover:bg-gray-50 border-gray-200 hover:border-gray-300'
                                      }`}
                                      whileHover={{ scale: 1.05 }}
                                      whileTap={{ scale: 0.95 }}
                                    >
                                      <MapPin className="w-4 h-4 mr-2" />
                                      {location}
                                    </motion.button>
                                  ))}
                                </div>
                              </div>

                              {/* Loyalty Tier Filters */}
                              <div>
                                <label className="block text-sm font-bold text-gray-800 mb-3">Loyalty Tiers</label>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                                  {['Bronze', 'Silver', 'Gold', 'Platinum'].map((tier) => (
                                    <motion.button
                                      key={tier}
                                      type="button"
                                      onClick={() => {
                                        const currentTags = newVoucher.targeting?.requiredTags || [];
                                        const tierTag = `tier-${tier.toLowerCase()}`;
                                        if (currentTags.includes(tierTag)) {
                                          setNewVoucher(prev => ({
                                            ...prev,
                                            targeting: {
                                              ...prev.targeting!,
                                              requiredTags: currentTags.filter(t => t !== tierTag)
                                            }
                                          }));
                                        } else {
                                          setNewVoucher(prev => ({
                                            ...prev,
                                            targeting: {
                                              ...prev.targeting!,
                                              requiredTags: [...currentTags, tierTag]
                                            }
                                          }));
                                        }
                                      }}
                                      className={`px-4 py-3 rounded-xl text-sm font-medium transition-all border-2 flex items-center ${
                                        (newVoucher.targeting?.requiredTags || []).includes(`tier-${tier.toLowerCase()}`)
                                          ? 'bg-purple-100 text-purple-700 border-purple-300'
                                          : 'bg-white text-gray-600 hover:bg-gray-50 border-gray-200 hover:border-gray-300'
                                      }`}
                                      whileHover={{ scale: 1.05 }}
                                      whileTap={{ scale: 0.95 }}
                                    >
                                      <Star className="w-4 h-4 mr-2" />
                                      {tier}
                                    </motion.button>
                                  ))}
                                </div>
                              </div>

                              {/* Selected Filters Summary */}
                              {newVoucher.targeting?.requiredTags && newVoucher.targeting.requiredTags.length > 0 && (
                                <div className="bg-emerald-50 rounded-xl p-4 border border-emerald-200">
                                  <h5 className="font-medium text-emerald-800 mb-2">Active Filters:</h5>
                                  <div className="flex flex-wrap gap-2">
                                    {newVoucher.targeting.requiredTags.map((tag) => (
                                      <span
                                        key={tag}
                                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-emerald-100 text-emerald-700 border border-emerald-300"
                                      >
                                        {tag.replace('-', ' ').replace('tier ', '').replace('location ', '')}
                                        <button
                                          type="button"
                                          onClick={() => {
                                            setNewVoucher(prev => ({
                                              ...prev,
                                              targeting: {
                                                ...prev.targeting!,
                                                requiredTags: prev.targeting!.requiredTags!.filter(t => t !== tag)
                                              }
                                            }));
                                          }}
                                          className="ml-2 text-emerald-600 hover:text-emerald-800 transition-colors"
                                        >
                                          <X className="w-3 h-3" />
                                        </button>
                                      </span>
                                    ))}
                                  </div>
                                  <p className="text-xs text-emerald-600 mt-2">
                                    Estimated reach: {customers.filter(c => 
                                      newVoucher.targeting!.requiredTags!.some(tag => 
                                        c.tags.includes(tag) || 
                                        tag.includes(c.location.toLowerCase().replace(' ', '-')) ||
                                        tag.includes(c.loyaltyTier.toLowerCase())
                                      )
                                    ).length} customers
                                  </p>
                                </div>
                              )}
                            </motion.div>
                          )}

                          {newVoucher.targeting?.type === 'private' && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              className="space-y-4"
                            >
                              <div>
                                <label className="block text-sm font-bold text-gray-800 mb-3">Search Customers</label>
                                <div className="relative">
                                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                  <input
                                    type="text"
                                    value={customerSearchTerm}
                                    onChange={(e) => setCustomerSearchTerm(e.target.value)}
                                    placeholder="Search by name, email, or tags..."
                                    className="w-full pl-12 pr-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-emerald-100 focus:border-emerald-500 transition-all text-lg"
                                  />
                                </div>

                                {/* Customer Statistics */}
                                <div className="flex items-center justify-between text-sm text-gray-500 mb-3 mt-3">
                                  <span>{filteredCustomers.length} customers found</span>
                                  <span>{newVoucher.targeting?.selectedCustomers?.length || 0} selected</span>
                                </div>

                                <div className="bg-white rounded-xl border border-gray-200 max-h-64 overflow-y-auto">
                                  {filteredCustomers.length === 0 ? (
                                    <div className="p-8 text-center">
                                      <Users className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                                      <p className="text-gray-500">No customers match your search</p>
                                      <button
                                        onClick={() => setCustomerSearchTerm('')}
                                        className="text-emerald-600 hover:text-emerald-700 text-sm font-medium mt-2"
                                      >
                                        Clear search
                                      </button>
                                    </div>
                                  ) : (
                                    filteredCustomers.map((customer) => (
                                      <div key={customer.id} className="flex items-center justify-between p-4 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                                        <div className="flex items-center space-x-3 flex-1">
                                          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center text-white text-sm font-bold">
                                            {customer.name.charAt(0)}
                                          </div>
                                          <div className="flex-1 min-w-0">
                                            <div className="font-medium text-gray-900">{customer.name}</div>
                                            <div className="text-sm text-gray-500">{customer.email}</div>
                                            <div className="flex items-center space-x-4 text-xs text-gray-400 mt-1">
                                              <span>{customer.location}</span>
                                              <span>{customer.loyaltyTier}</span>
                                              <span>${customer.totalSpent}</span>
                                            </div>
                                            {customer.tags.length > 0 && (
                                              <div className="flex flex-wrap gap-1 mt-2">
                                                {customer.tags.slice(0, 3).map((tag, tagIndex) => (
                                                  <span
                                                    key={tagIndex}
                                                    className="px-2 py-1 bg-indigo-100 text-indigo-600 rounded text-xs font-medium"
                                                  >
                                                    {tag}
                                                  </span>
                                                ))}
                                                {customer.tags.length > 3 && (
                                                  <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                                                    +{customer.tags.length - 3}
                                                  </span>
                                                )}
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                        <input
                                          type="checkbox"
                                          checked={(newVoucher.targeting?.selectedCustomers || []).includes(customer.id)}
                                          onChange={(e) => {
                                            const currentSelected = newVoucher.targeting?.selectedCustomers || [];
                                            if (e.target.checked) {
                                              setNewVoucher(prev => ({
                                                ...prev,
                                                targeting: {
                                                  ...prev.targeting!,
                                                  selectedCustomers: [...currentSelected, customer.id]
                                                }
                                              }));
                                            } else {
                                              setNewVoucher(prev => ({
                                                ...prev,
                                                targeting: {
                                                  ...prev.targeting!,
                                                  selectedCustomers: currentSelected.filter(id => id !== customer.id)
                                                }
                                              }));
                                            }
                                          }}
                                          className="w-5 h-5 text-emerald-600 border-gray-300 rounded focus:ring-emerald-500"
                                        />
                                      </div>
                                    ))
                                  )}
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </div>
                      </div>

                      {/* Enhanced Voucher Classification */}
                      <div className="bg-gray-50 rounded-3xl p-8 border border-gray-200">
                        <div className="flex items-center space-x-3 mb-6">
                          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                            <Star className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h4 className="text-2xl font-bold text-gray-900">Category & Tags</h4>
                            <p className="text-gray-600">Help customers discover your offer</p>
                          </div>
                        </div>

                        <div className="space-y-6">
                          <div>
                            <label className="block text-sm font-bold text-gray-800 mb-4">Voucher Category</label>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                              {Object.entries(voucherCategories).map(([key, category]) => {
                                const IconComponent = category.iconComponent;
                                const isSelected = newVoucher.classification === key;

                                return (
                                  <motion.div
                                    key={key}
                                    className={`relative border-2 rounded-xl p-6 cursor-pointer transition-all group ${
                                      isSelected
                                        ? 'border-indigo-500 bg-indigo-50 shadow-lg ring-1 ring-indigo-200'
                                        : 'border-gray-200 hover:border-indigo-300 hover:shadow-md bg-white'
                                    }`}
                                    whileHover={{ scale: 1.02, y: -2 }}
                                    whileTap={{ scale: 0.98 }}
                                    onClick={() => setNewVoucher(prev => ({ ...prev, classification: key }))}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.1 * Object.keys(voucherCategories).indexOf(key) }}
                                  >
                                    {isSelected && (
                                      <motion.div
                                        className="absolute top-3 right-3 w-6 h-6 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-md z-10"
                                        initial={{ scale: 0, rotate: -180 }}
                                        animate={{ scale: 1, rotate: 0 }}
                                        transition={{ type: "spring", stiffness: 500, delay: 0.2 }}
                                      >
                                        <CheckCircle className="w-3.5 h-3.5 text-white" />
                                      </motion.div>
                                    )}

                                    <div className="relative text-center">
                                      <motion.div 
                                        className={`w-12 h-12 mx-auto mb-4 rounded-xl flex items-center justify-center transition-all duration-300 ${
                                          isSelected 
                                            ? 'bg-gradient-to-br from-indigo-500 to-purple-600 text-white shadow-lg' 
                                            : 'bg-gray-100 text-gray-600 group-hover:bg-indigo-100 group-hover:text-indigo-600'
                                        }`}
                                        animate={isSelected ? { rotate: [0, 5, -5, 0] } : {}}
                                        transition={{ duration: 0.5 }}
                                      >
                                        <IconComponent className="w-6 h-6" />
                                      </motion.div>
                                      <h3 className={`text-sm font-semibold mb-2 transition-colors ${
                                        isSelected ? 'text-indigo-700' : 'text-gray-900'
                                      }`}>
                                        {category.label}
                                      </h3>
                                      <div className="flex items-center justify-center text-xs text-gray-500 bg-gray-50 rounded-full px-3 py-1 mb-3">
                                        <Tag className="w-3 h-3 mr-1" />
                                        {category.tags.length} tags
                                      </div>

                                      {/* Preview tags */}
                                      <div className="flex flex-wrap gap-1 justify-center">
                                        {category.tags.slice(0, 2).map((tag, index) => (
                                          <span 
                                            key={index}
                                            className="text-xs bg-white text-gray-600 px-2 py-1 rounded-md border border-gray-200"
                                          >
                                            {tag.replace('-', ' ')}
                                          </span>
                                        ))}
                                        {category.tags.length > 2 && (
                                          <span className="text-xs text-gray-400 px-2 py-1">
                                            +{category.tags.length - 2}
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  </motion.div>
                                );
                              })}
                            </div>
                          </div>

                          {/* Enhanced Tags System */}
                          {newVoucher.classification && (
                            <div>
                              <label className="block text-sm font-bold text-gray-800 mb-4">
                                Tags (Help customers find your voucher)
                              </label>
                              <div className="space-y-4">
                                {/* Suggested Tags */}
                                <div>
                                  <p className="text-xs text-gray-500 mb-3">Suggested tags for {voucherCategories[newVoucher.classification].label}:</p>
                                  <div className="flex flex-wrap gap-2">
                                    {voucherCategories[newVoucher.classification].tags.map((tag) => (
                                      <motion.button
                                        key={tag}
                                        type="button"
                                        onClick={() => {
                                          const currentTags = newVoucher.tags || [];
                                          if (currentTags.includes(tag)) {
                                            setNewVoucher(prev => ({
                                              ...prev,
                                              tags: currentTags.filter(t => t !== tag)
                                            }));
                                          } else {
                                            setNewVoucher(prev => ({
                                              ...prev,
                                              tags: [...currentTags, tag]
                                            }));
                                          }
                                        }}
                                        className={`px-4 py-2 rounded-full text-sm font-medium transition-all border-2 ${
                                          (newVoucher.tags || []).includes(tag)
                                            ? `${voucherCategories[newVoucher.classification].bgColor} ${voucherCategories[newVoucher.classification].textColor} ${voucherCategories[newVoucher.classification].borderColor}`
                                            : 'bg-white text-gray-600 hover:bg-gray-50 border-gray-200 hover:border-gray-300'
                                        }`}
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                      >
                                        {tag.replace('-', ' ')}
                                      </motion.button>
                                    ))}
                                  </div>
                                </div>

                                {/* Selected Tags Display */}
                                {newVoucher.tags && newVoucher.tags.length > 0 && (
                                  <div>
                                    <p className="text-xs text-gray-500 mb-3">Selected tags:</p>
                                    <div className="flex flex-wrap gap-2">
                                      {newVoucher.tags.map((tag, index) => (
                                        <span
                                          key={index}
                                          className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-indigo-100 text-indigo-700 border border-indigo-200"
                                        >
                                          #{tag}
                                          <button
                                            type="button"
                                            onClick={() => {
                                              setNewVoucher(prev => ({
                                                ...prev,
                                                tags: prev.tags?.filter((_, i) => i !== index)
                                              }));
                                            }}
                                            className="ml-2 text-indigo-500 hover:text-indigo-700 transition-colors"
                                          >
                                            <X className="w-4 h-4" />
                                          </button>
                                        </span>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Enhanced Image Section */}
                      <div className="bg-gray-50 rounded-3xl p-8 border border-gray-200">
                        <div className="flex items-center space-x-3 mb-6">
                          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                            <Camera className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h4 className="text-2xl font-bold text-gray-900">Visual Appeal</h4>
                            <p className="text-gray-600">Make your voucher stand out visually</p>
                          </div>
                        </div>

                        <div className="space-y-6">
                          {/* Image Preview */}
                          {newVoucher.imageUrl && (
                            <div className="relative rounded-2xl overflow-hidden border-2 border-gray-200">
                              <img 
                                src={newVoucher.imageUrl} 
                                alt="Voucher preview"
                                className="w-full h-48 object-cover"
                              />
                              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent flex items-end justify-end p-4">
                                <button
                                  onClick={() => setNewVoucher(prev => ({ ...prev, imageUrl: '', useAIImage: false }))}
                                  className="bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors shadow-lg"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                          )}

                          {/* Image Upload Options */}
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <label className="group relative flex flex-col items-center justify-center h-40 border-2 border-dashed border-gray-300 rounded-2xl cursor-pointer hover:border-yellow-400 hover:bg-yellow-50 transition-all duration-300 overflow-hidden">
                              <input
                                type="file"
                                accept="image/*"
                                className="absolute inset-0 opacity-0 cursor-pointer"
                                onChange={(e) => {
                                  const file = e.target.files?.[0];
                                  if (file) {
                                    const reader = new FileReader();
                                    reader.onload = (e) => {
                                      setNewVoucher(prev => ({
                                        ...prev,
                                        imageUrl: e.target?.result as string,
                                        useAIImage: false
                                      }));
                                    };
                                    reader.readAsDataURL(file);
                                  }
                                }}
                              />
                              <div className="text-center z-10">
                                <Upload className="w-12 h-12 text-gray-400 group-hover:text-yellow-500 mb-3 transition-colors mx-auto" />
                                <div className="text-lg font-bold text-gray-700 group-hover:text-yellow-600 transition-colors mb-2">Upload Image</div>
                                <div className="text-sm text-gray-500">Drop files here or click to browse</div>
                              </div>
                              <div className="absolute inset-0 bg-gradient-to-br from-yellow-100/50 to-orange-100/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </label>

                            <motion.button
                              type="button"
                              onClick={generateAIImage}
                              disabled={isGeneratingImage}
                              className="relative flex flex-col items-center justify-center h-40 border-2 border-dashed border-purple-300 rounded-2xl hover:border-purple-400 hover:bg-purple-50 transition-all duration-300 disabled:opacity-50 group overflow-hidden"
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                            >
                              {isGeneratingImage ? (
                                <div className="text-center z-10">
                                  <div className="w-12 h-12 mx-auto mb-3 animate-spin border-4 border-purple-500 border-t-transparent rounded-full"></div>
                                  <div className="text-lg font-bold text-purple-600">Creating Magic...</div>
                                  <div className="text-sm text-purple-500">AI is generating your image</div>
                                </div>
                              ) : (
                                <div className="text-center z-10">
                                  <Sparkles className="w-12 h-12 text-purple-500 group-hover:text-purple-600 mb-3 transition-colors mx-auto" />
                                  <div className="text-lg font-bold text-purple-600 group-hover:text-purple-700 transition-colors mb-2">AI Generate</div>
                                  <div className="text-sm text-purple-500">Let AI create the perfect image</div>
                                </div>
                              )}
                              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/50 to-pink-100/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </motion.button>
                          </div>

                          <div className="bg-indigo-50 rounded-2xl p-4 border border-indigo-200">
                            <div className="flex items-start space-x-3">
                              <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center flex-shrink-0">
                                <span className="text-white text-sm font-bold">💡</span>
                              </div>
                              <div>
                                <h5 className="font-bold text-indigo-900 mb-1">Pro Tips for Great Voucher Images</h5>
                                <ul className="text-sm text-indigo-800 space-y-1">
                                  <li>• Use high-quality, appetizing images for food offers</li>
                                  <li>• Include your brand colors and logo for recognition</li>
                                  <li>• Show the product or service being offered</li>
                                  <li>• Keep text on images minimal and readable</li>
                                </ul>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Location Selection */}
                      <div className="bg-gray-50 rounded-3xl p-8 border border-gray-200">
                        <div className="flex items-center space-x-3 mb-6">
                          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                            <MapPin className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h4 className="text-2xl font-bold text-gray-900">Redemption Locations</h4>
                            <p className="text-gray-600">Choose where customers can redeem this voucher</p>
                          </div>
                        </div>

                        <div className="space-y-6">
                          {/* All Locations Toggle */}
                          <div className="bg-white rounded-2xl p-6 border border-gray-200">
                            <div className="flex items-center justify-between mb-4">
                              <div className="flex items-center">
                                <input
                                  type="checkbox"
                                  id="allLocations"
                                  checked={(newVoucher.redeemableSites || []).length === businessLocations.length}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setNewVoucher(prev => ({
                                        ...prev,
                                        redeemableSites: businessLocations.map(loc => loc.id)
                                      }));
                                    } else {
                                      setNewVoucher(prev => ({
                                        ...prev,
                                        redeemableSites: []
                                      }));
                                    }
                                  }}
                                  className="w-5 h-5 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 mr-3"
                                />
                                <div>
                                  <label htmlFor="allLocations" className="text-lg font-semibold text-gray-900 cursor-pointer">
                                    All Business Locations
                                  </label>
                                  <p className="text-sm text-gray-600">Allow redemption at any of your business locations</p>
                                </div>
                              </div>
                              <div className="flex items-center px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-sm font-medium">
                                <Globe className="w-4 h-4 mr-1" />
                                {businessLocations.length} locations
                              </div>
                            </div>

                            {(newVoucher.redeemableSites || []).length === businessLocations.length && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: 'auto' }}
                                className="bg-indigo-50 border border-indigo-200 rounded-xl p-4"
                              >
                                <div className="flex items-center text-indigo-800">
                                  <CheckCircle className="w-5 h-5 mr-2" />
                                  <span className="font-medium">All locations selected</span>
                                </div>
                                <p className="text-sm text-indigo-600 mt-1">
                                  This voucher can be redeemed at any of your {businessLocations.length} business locations
                                </p>
                              </motion.div>
                            )}
                          </div>

                          {/* Individual Location Selection */}
                          <div className="bg-white rounded-2xl p-6 border border-gray-200">
                            <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                              <Building className="w-5 h-5 mr-2 text-gray-600" />
                              Select Specific Locations
                            </h5>
                            <p className="text-sm text-gray-600 mb-6">
                              Choose individual locations where this voucher can be redeemed. Useful for location-specific offers or capacity management.
                            </p>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {businessLocations.map((location) => {
                                const isSelected = (newVoucher.redeemableSites || []).includes(location.id);
                                
                                return (
                                  <motion.div
                                    key={location.id}
                                    className={`relative border-2 rounded-xl p-5 cursor-pointer transition-all duration-300 ${
                                      isSelected
                                        ? 'border-emerald-500 bg-emerald-50 shadow-md ring-1 ring-emerald-200'
                                        : 'border-gray-200 hover:border-emerald-300 hover:shadow-sm bg-white'
                                    }`}
                                    onClick={() => {
                                      const currentSites = newVoucher.redeemableSites || [];
                                      if (isSelected) {
                                        setNewVoucher(prev => ({
                                          ...prev,
                                          redeemableSites: currentSites.filter(id => id !== location.id)
                                        }));
                                      } else {
                                        setNewVoucher(prev => ({
                                          ...prev,
                                          redeemableSites: [...currentSites, location.id]
                                        }));
                                      }
                                    }}
                                    whileHover={{ scale: 1.02, y: -2 }}
                                    whileTap={{ scale: 0.98 }}
                                  >
                                    {isSelected && (
                                      <motion.div
                                        className="absolute top-3 right-3 w-6 h-6 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full flex items-center justify-center shadow-md z-10"
                                        initial={{ scale: 0, rotate: -180 }}
                                        animate={{ scale: 1, rotate: 0 }}
                                        transition={{ type: "spring", stiffness: 500, delay: 0.1 }}
                                      >
                                        <CheckCircle className="w-3.5 h-3.5 text-white" />
                                      </motion.div>
                                    )}

                                    <div className="flex items-start space-x-4">
                                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 ${
                                        isSelected
                                          ? 'bg-gradient-to-br from-emerald-500 to-teal-600 text-white shadow-lg'
                                          : 'bg-gray-100 text-gray-600'
                                      }`}>
                                        <Building className="w-6 h-6" />
                                      </div>
                                      
                                      <div className="flex-1 min-w-0">
                                        <h6 className={`text-lg font-semibold mb-1 transition-colors ${
                                          isSelected ? 'text-emerald-700' : 'text-gray-900'
                                        }`}>
                                          {location.name}
                                        </h6>
                                        <p className="text-sm text-gray-600 mb-2">
                                          {location.address}
                                        </p>
                                        
                                        {/* Location Stats/Info */}
                                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                                          <span className="flex items-center">
                                            <Users className="w-3 h-3 mr-1" />
                                            Active
                                          </span>
                                          <span className="flex items-center">
                                            <Calendar className="w-3 h-3 mr-1" />
                                            Operating
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  </motion.div>
                                );
                              })}
                            </div>

                            {/* Selection Summary */}
                            <div className="mt-6 pt-6 border-t border-gray-200">
                              <div className="flex items-center justify-between">
                                <div className="text-sm text-gray-600">
                                  <span className="font-medium">Selected Locations:</span>
                                  <span className="ml-2">
                                    {(newVoucher.redeemableSites || []).length} of {businessLocations.length}
                                  </span>
                                </div>
                                
                                {(newVoucher.redeemableSites || []).length > 0 && (
                                  <div className="flex items-center space-x-2">
                                    <motion.button
                                      type="button"
                                      onClick={() => setNewVoucher(prev => ({ ...prev, redeemableSites: [] }))}
                                      className="text-sm text-red-600 hover:text-red-700 font-medium transition-colors"
                                      whileHover={{ scale: 1.05 }}
                                      whileTap={{ scale: 0.95 }}
                                    >
                                      Clear All
                                    </motion.button>
                                    <span className="text-gray-300">|</span>
                                    <motion.button
                                      type="button"
                                      onClick={() => setNewVoucher(prev => ({
                                        ...prev,
                                        redeemableSites: businessLocations.map(loc => loc.id)
                                      }))}
                                      className="text-sm text-emerald-600 hover:text-emerald-700 font-medium transition-colors"
                                      whileHover={{ scale: 1.05 }}
                                      whileTap={{ scale: 0.95 }}
                                    >
                                      Select All
                                    </motion.button>
                                  </div>
                                )}
                              </div>

                              {/* Show selected locations list */}
                              {(newVoucher.redeemableSites || []).length > 0 && (newVoucher.redeemableSites || []).length < businessLocations.length && (
                                <motion.div
                                  initial={{ opacity: 0, height: 0 }}
                                  animate={{ opacity: 1, height: 'auto' }}
                                  className="mt-4 p-4 bg-emerald-50 border border-emerald-200 rounded-xl"
                                >
                                  <h6 className="text-sm font-medium text-emerald-800 mb-2">Selected for redemption:</h6>
                                  <div className="flex flex-wrap gap-2">
                                    {(newVoucher.redeemableSites || []).map(siteId => {
                                      const location = businessLocations.find(loc => loc.id === siteId);
                                      return location ? (
                                        <span
                                          key={siteId}
                                          className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-700 border border-emerald-300"
                                        >
                                          <MapPin className="w-3 h-3 mr-1" />
                                          {location.name}
                                        </span>
                                      ) : null;
                                    })}
                                  </div>
                                </motion.div>
                              )}
                            </div>
                          </div>

                          {/* Location Strategy Tips */}
                          <div className="bg-blue-50 rounded-2xl p-6 border border-blue-200">
                            <div className="flex items-start space-x-3">
                              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                <span className="text-white text-sm font-bold">💡</span>
                              </div>
                              <div>
                                <h5 className="font-bold text-blue-900 mb-2">Location Strategy Tips</h5>
                                <ul className="text-sm text-blue-800 space-y-1">
                                  <li>• <strong>Grand Openings:</strong> Limit to the new location only</li>
                                  <li>• <strong>Capacity Management:</strong> Exclude busy locations during peak times</li>
                                  <li>• <strong>Inventory Control:</strong> Only include locations with sufficient stock</li>
                                  <li>• <strong>Testing:</strong> Start with one location before rolling out everywhere</li>
                                </ul>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Schedule & Limits */}
                      <div className="bg-gray-50 rounded-3xl p-8 border border-gray-200">
                        <div className="flex items-center space-x-3 mb-6">
                          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                            <Clock className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h4 className="text-2xl font-bold text-gray-900">Schedule & Limits</h4>
                            <p className="text-gray-600">Set availability and usage limits</p>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <div>
                            <label className="block text-sm font-bold text-gray-800 mb-3">Start Date</label>
                            <input
                              type="date"
                              value={newVoucher.startDate || ''}
                              onChange={(e) => setNewVoucher(prev => ({ ...prev, startDate: e.target.value }))}
                              className="w-full px-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all text-lg font-medium"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-bold text-gray-800 mb-3">End Date</label>
                            <input
                              type="date"
                              value={newVoucher.endDate || ''}
                              onChange={(e) => setNewVoucher(prev => ({ ...prev, endDate: e.target.value }))}
                              className="w-full px-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all text-lg font-medium"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-bold text-gray-800 mb-3">
                              Usage Limit
                              <span className="ml-2 text-xs font-normal text-gray-500">
                                (Max: {subscriptionTiers[currentTier].quantityLimit} on {subscriptionTiers[currentTier].label} plan)
                              </span>
                            </label>
                            <div className="relative">
                              <input
                                type="number"
                                min="1"
                                max={subscriptionTiers[currentTier].quantityLimit}
                                value={newVoucher.quantityLimit || 100}
                                onChange={(e) => {
                                  const value = parseInt(e.target.value);
                                  if (value > subscriptionTiers[currentTier].quantityLimit) {
                                    setShowUpgradeModal(true);
                                    return;
                                  }
                                  setNewVoucher(prev => ({ ...prev, quantityLimit: value }));
                                }}
                                className={`w-full px-5 py-4 border-2 rounded-2xl focus:ring-4 transition-all text-lg font-medium ${
                                  (newVoucher.quantityLimit || 0) > subscriptionTiers[currentTier].quantityLimit
                                    ? 'border-red-300 focus:border-red-500 focus:ring-red-100 bg-red-50'
                                    : 'border-gray-200 focus:border-blue-500 focus:ring-blue-100'
                                }`}
                                placeholder="100"
                              />
                              {(newVoucher.quantityLimit || 0) > subscriptionTiers[currentTier].quantityLimit && (
                                <motion.div
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="absolute right-3 top-1/2 transform -translate-y-1/2"
                                >
                                  <motion.button
                                    onClick={() => setShowUpgradeModal(true)}
                                    className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-xs font-bold hover:from-purple-600 hover:to-pink-600 transition-all shadow-lg"
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                  >
                                    Upgrade
                                  </motion.button>
                                </motion.div>
                              )}
                            </div>
                            
                            {/* Tier Benefits CTA */}
                            {currentTier !== 'premium' && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: 'auto' }}
                                className="mt-3 p-3 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-xl"
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-2">
                                    <Zap className="w-4 h-4 text-purple-600" />
                                    <span className="text-sm font-medium text-purple-700">
                                      Want more quantity? 
                                      {currentTier === 'free' && ' Basic plan allows up to 250 vouchers'}
                                      {currentTier === 'basic' && ' Premium plan allows up to 1,000 vouchers'}
                                    </span>
                                  </div>
                                  <motion.button
                                    onClick={() => setShowUpgradeModal(true)}
                                    className="text-xs bg-purple-600 text-white px-3 py-1 rounded-full font-bold hover:bg-purple-700 transition-colors"
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                  >
                                    Upgrade
                                  </motion.button>
                                </div>
                              </motion.div>
                            )}

                            {/* Current tier status */}
                            <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
                              <span>Current limit: {subscriptionTiers[currentTier].quantityLimit} vouchers</span>
                              <span className={`px-2 py-1 rounded-full font-medium ${
                                currentTier === 'free' ? 'bg-gray-100 text-gray-600' :
                                currentTier === 'basic' ? 'bg-blue-100 text-blue-600' :
                                'bg-purple-100 text-purple-600'
                              }`}>
                                {subscriptionTiers[currentTier].label} Plan
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Live Preview Section */}
                    {showLivePreview && (
                      <div className="lg:sticky lg:top-8 space-y-6">
                        <div className="bg-white rounded-3xl p-6 border border-gray-200 shadow-lg">
                          <div className="flex items-center justify-between mb-4">
                            <h4 className="text-xl font-bold text-gray-700 flex items-center">
                              <Eye className="w-6 h-6 mr-3 text-indigo-600" />
                              Live Preview
                            </h4>
                            <div className="w-3 h-3 bg-emerald-500 rounded-full animate-pulse"></div>
                          </div>

                          <div className="bg-gradient-to-br from-gray-50 to-indigo-50 rounded-2xl p-6 border border-gray-200">
                            <div className="bg-white border-2 border-gray-200 rounded-2xl overflow-hidden shadow-xl max-w-sm mx-auto transform hover:scale-105 transition-transform duration-300">
                              {/* Preview Image */}
                              {newVoucher.imageUrl ? (
                                <div className="h-40 relative overflow-hidden">
                                  <img 
                                    src={newVoucher.imageUrl} 
                                    alt={newVoucher.title || 'Voucher'}
                                    className="w-full h-full object-cover"
                                  />
                                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent flex items-center justify-center">
                                    <Gift className="w-10 h-10 text-white drop-shadow-lg" />
                                  </div>
                                  {/* Referrer Reward Badge */}
                                  {newVoucher.reward && (
                                    <div className="absolute top-3 right-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg border-2 border-white">
                                      <Gift className="w-3 h-3 inline mr-1" />
                                      Earn ${newVoucher.reward}
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <div className="h-40 bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center relative">
                                  <div className="text-center text-white">
                                    <Gift className="w-12 h-12 mx-auto mb-2" />
                                    <div className="text-sm font-medium">Add an image</div>
                                  </div>
                                  {/* Referrer Reward Badge */}
                                  {newVoucher.reward && (
                                    <div className="absolute top-3 right-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg border-2 border-white">
                                      <Gift className="w-3 h-3 inline mr-1" />
                                      Earn ${newVoucher.reward}
                                    </div>
                                  )}
                                </div>
                              )}

                              <div className="p-6">
                                <div className="flex items-center justify-between mb-4">
                                  <span className="px-3 py-1 bg-emerald-50 text-emerald-700 rounded-full text-xs font-bold border border-emerald-200">
                                    <Sparkles className="w-3 h-3 inline mr-1" />
                                    Limited Time
                                  </span>
                                  {newVoucher.offerValue && (
                                    <span className="px-3 py-1 bg-yellow-50 text-yellow-700 rounded-full text-xs font-bold border border-yellow-200">
                                      ${newVoucher.offerValue} Value
                                    </span>
                                  )}
                                </div>

                                <h5 className="text-xl font-bold text-gray-900 mb-3 leading-tight">
                                  {newVoucher.title || 'Your Amazing Offer Title'}
                                </h5>
                                <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                                  {newVoucher.description || 'Your compelling offer description that explains the value and creates urgency...'}
                                </p>

                                {/* Voucher Stats */}
                                <div className="bg-gray-50 border border-gray-200 rounded-xl p-3 mb-4">
                                  <div className="grid grid-cols-2 gap-3 text-xs">
                                    <div className="text-center">
                                      <div className="text-gray-500 mb-1">Quantity Left</div>
                                      <div className="font-bold text-gray-900">{newVoucher.quantityLimit || 100}</div>
                                    </div>
                                    <div className="text-center">
                                      <div className="text-gray-500 mb-1">Duration</div>
                                      <div className="font-bold text-gray-900">
                                        {newVoucher.startDate && newVoucher.endDate 
                                          ? `${Math.ceil((new Date(newVoucher.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days`
                                          : '30 days'
                                        }
                                      </div>
                                    </div>
                                  </div>
                                  
                                  {/* Redemption Locations */}
                                  {(newVoucher.redeemableSites || []).length > 0 && (
                                    <div className="mt-3 pt-3 border-t border-gray-200">
                                      <div className="text-center">
                                        <div className="text-gray-500 mb-1 text-xs">Redeemable At</div>
                                        <div className="flex items-center justify-center">
                                          <MapPin className="w-3 h-3 text-gray-600 mr-1" />
                                          <span className="font-bold text-gray-900 text-xs">
                                            {(newVoucher.redeemableSites || []).length === businessLocations.length 
                                              ? 'All Locations'
                                              : `${(newVoucher.redeemableSites || []).length} Location${(newVoucher.redeemableSites || []).length !== 1 ? 's' : ''}`
                                            }
                                          </span>
                                        </div>
                                        {(newVoucher.redeemableSites || []).length > 0 && (newVoucher.redeemableSites || []).length < businessLocations.length && (
                                          <div className="mt-1 text-xs text-gray-500">
                                            {(newVoucher.redeemableSites || []).slice(0, 2).map(siteId => {
                                              const location = businessLocations.find(loc => loc.id === siteId);
                                              return location?.name;
                                            }).filter(Boolean).join(', ')}
                                            {(newVoucher.redeemableSites || []).length > 2 && ` +${(newVoucher.redeemableSites || []).length - 2} more`}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                </div>

                                {/* Restrictions preview */}
                                {newVoucher.restrictions && (
                                  <div className="bg-gray-50 border border-gray-200 rounded-xl p-3 mb-4">
                                    <p className="text-xs text-gray-600 leading-relaxed">
                                      <span className="font-medium">Terms:</span> {newVoucher.restrictions}
                                    </p>
                                  </div>
                                )}

                                {/* Tags preview */}
                                {newVoucher.tags && newVoucher.tags.length > 0 && (
                                  <div className="flex flex-wrap gap-1 mb-4">
                                    {newVoucher.tags.slice(0, 3).map((tag, index) => (
                                      <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                                        #{tag}
                                      </span>
                                    ))}
                                  </div>
                                )}

                                <div className="space-y-3">
                                  <button className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-4 rounded-xl text-sm font-bold hover:from-indigo-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl transform hover:scale-105 duration-200">
                                    <Share2 className="w-4 h-4 inline mr-2" />
                                    Share & Earn Now
                                  </button>

                                  <div className="text-center">
                                    <div className="text-xs text-gray-500 mb-2">
                                      {newVoucher.targeting?.type === 'public' ? 'Available to all customers' :
                                       newVoucher.targeting?.type === 'tagged' ? 'Available to specific segments' :
                                       'Available to selected customers'}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Enhanced Targeting Stats */}
                        <div className="bg-white rounded-3xl p-6 border border-gray-200 shadow-lg">
                          <h5 className="text-lg font-bold text-gray-700 mb-4 flex items-center">
                            <Target className="w-5 h-5 mr-2 text-purple-600" />
                            Targeting Analytics
                          </h5>
                          <div className="space-y-4">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600">Potential Reach</span>
                              <span className="font-bold text-indigo-600">
                                {newVoucher.targeting?.type === 'public' ? `${customers.length} customers` :
                                 newVoucher.targeting?.type === 'tagged' ? 
                                   `${customers.filter(c => 
                                     newVoucher.targeting!.requiredTags!.some(tag => 
                                       c.tags.includes(tag) || 
                                       tag.includes(c.location.toLowerCase().replace(' ', '-')) ||
                                       tag.includes(c.loyaltyTier.toLowerCase())
                                     )
                                   ).length} customers` :
                                 `${newVoucher.targeting?.selectedCustomers?.length || 0} customers`}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600">Max Revenue Impact</span>
                              <span className="font-bold text-emerald-600">
                                {newVoucher.offerValue ? `$${(newVoucher.offerValue * (newVoucher.quantityLimit || 100)).toLocaleString()}` : 'Not specified'}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600">Campaign Duration</span>
                              <span className="font-bold text-purple-600">
                                {newVoucher.startDate && newVoucher.endDate 
                                  ? `${Math.ceil((new Date(newVoucher.endDate).getTime() - new Date(newVoucher.startDate).getTime()) / (1000 * 60 * 60 * 24))} days`
                                  : 'Not set'
                                }
                              </span>
                            </div>

                            {/* Audience Breakdown */}
                            {newVoucher.targeting?.type === 'tagged' && newVoucher.targeting.requiredTags && newVoucher.targeting.requiredTags.length > 0 && (
                              <div className="pt-3 border-t border-gray-200">
                                <p className="text-sm font-medium text-gray-700 mb-2">Audience Breakdown:</p>
                                <div className="space-y-2">
                                  {newVoucher.targeting.requiredTags.map(tag => {
                                    const matchingCustomers = customers.filter(c => 
                                      c.tags.includes(tag) || 
                                      tag.includes(c.location.toLowerCase().replace(' ', '-')) ||
                                      tag.includes(c.loyaltyTier.toLowerCase())
                                    ).length;
                                    return (
                                      <div key={tag} className="flex justify-between text-xs">
                                        <span className="text-gray-600 capitalize">{tag.replace('-', ' ')}</span>
                                        <span className="font-medium text-gray-800">{matchingCustomers} customers</span>
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Redemption Locations Widget */}
                        <div className="bg-white rounded-3xl p-6 border border-gray-200 shadow-lg">
                          <h5 className="text-lg font-bold text-gray-700 mb-4 flex items-center">
                            <MapPin className="w-5 h-5 mr-2 text-emerald-600" />
                            Redemption Locations
                          </h5>
                          <div className="space-y-3">
                            {(newVoucher.redeemableSites || []).length === 0 ? (
                              <div className="text-center py-8">
                                <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                                  <Building className="w-6 h-6 text-gray-400" />
                                </div>
                                <p className="text-sm text-gray-500">No locations selected</p>
                                <p className="text-xs text-gray-400">Choose where this voucher can be redeemed</p>
                              </div>
                            ) : (newVoucher.redeemableSites || []).length === businessLocations.length ? (
                              <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl p-4 border border-emerald-200">
                                <div className="flex items-center justify-center mb-2">
                                  <Globe className="w-5 h-5 text-emerald-600 mr-2" />
                                  <span className="font-bold text-emerald-700">All Locations</span>
                                </div>
                                <p className="text-sm text-emerald-600 text-center">
                                  Redeemable at all {businessLocations.length} business locations
                                </p>
                                <div className="mt-3 grid gap-1">
                                  {businessLocations.slice(0, 3).map((location, index) => (
                                    <div key={location.id} className="flex items-center text-xs text-emerald-600">
                                      <div className="w-2 h-2 bg-emerald-400 rounded-full mr-2"></div>
                                      <span className="truncate">{location.name}</span>
                                    </div>
                                  ))}
                                  {businessLocations.length > 3 && (
                                    <div className="flex items-center text-xs text-emerald-500">
                                      <div className="w-2 h-2 bg-emerald-300 rounded-full mr-2"></div>
                                      <span>+{businessLocations.length - 3} more locations</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            ) : (
                              <div className="space-y-2">
                                <div className="flex items-center justify-between mb-3">
                                  <span className="text-sm font-medium text-gray-700">Selected Locations</span>
                                  <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-medium">
                                    {(newVoucher.redeemableSites || []).length} of {businessLocations.length}
                                  </span>
                                </div>
                                {(newVoucher.redeemableSites || []).map((siteId) => {
                                  const location = businessLocations.find(loc => loc.id === siteId);
                                  return location ? (
                                    <div key={siteId} className="flex items-center p-3 bg-gray-50 rounded-xl border border-gray-200">
                                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <Building className="w-4 h-4 text-white" />
                                      </div>
                                      <div className="ml-3 flex-1 min-w-0">
                                        <p className="text-sm font-medium text-gray-900 truncate">{location.name}</p>
                                        <p className="text-xs text-gray-500 truncate">{location.address}</p>
                                      </div>
                                      <div className="w-2 h-2 bg-emerald-500 rounded-full flex-shrink-0"></div>
                                    </div>
                                  ) : null;
                                })}
                              </div>
                            )}

                            {/* Quick Stats */}
                            <div className="bg-gray-50 rounded-xl p-4 border border-gray-200 mt-4">
                              <div className="grid grid-cols-2 gap-4 text-center">
                                <div>
                                  <div className="text-lg font-bold text-gray-900">
                                    {(newVoucher.redeemableSites || []).length}
                                  </div>
                                  <div className="text-xs text-gray-500">Active Locations</div>
                                </div>
                                <div>
                                  <div className="text-lg font-bold text-gray-900">
                                    {Math.round(((newVoucher.redeemableSites || []).length / businessLocations.length) * 100)}%
                                  </div>
                                  <div className="text-xs text-gray-500">Coverage</div>
                                </div>
                              </div>
                            </div>

                            {/* Strategy Insight */}
                            {(newVoucher.redeemableSites || []).length > 0 && (
                              <div className="bg-blue-50 rounded-xl p-3 border border-blue-200">
                                <div className="flex items-start space-x-2">
                                  <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-white text-xs font-bold">💡</span>
                                  </div>
                                  <div>
                                    <p className="text-xs text-blue-800 font-medium mb-1">Strategy Tip:</p>
                                    <p className="text-xs text-blue-700 leading-relaxed">
                                      {(newVoucher.redeemableSites || []).length === businessLocations.length
                                        ? "Maximum reach strategy - customers can redeem anywhere, increasing convenience and conversion likelihood."
                                        : (newVoucher.redeemableSites || []).length === 1
                                        ? "Focused approach - great for location-specific promotions or testing new offers."
                                        : "Selective strategy - balances reach with operational control. Consider capacity and inventory."
                                      }
                                    </p>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Live Preview Toggle */}
                        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-4 border border-indigo-200">
                          <div className="flex items-center justify-between">
                            <div>
                              <h6 className="font-medium text-indigo-800">Real-time Updates</h6>
                              <p className="text-xs text-indigo-600">Preview updates as you make changes</p>
                            </div>
                            <div className="w-6 h-6 bg-emerald-500 rounded-full animate-pulse"></div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Enhanced Footer - Fixed positioning */}
              <div className="border-t border-gray-200 bg-gradient-to-r from-gray-50 to-indigo-50 px-8 py-6 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                    <div className="text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                        <span>Ready to launch your irresistible offer</span>
                      </div>
                    </div>
                    
                    {/* Save Option Selection */}
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-gray-700">Save as:</span>
                      <div className="flex items-center space-x-2">
                        <motion.button
                          onClick={() => setVoucherSaveOption('draft')}
                          className={`px-4 py-2 rounded-xl text-sm font-medium transition-all ${
                            voucherSaveOption === 'draft'
                              ? 'bg-amber-100 text-amber-700 border-2 border-amber-300'
                              : 'bg-white text-gray-600 border-2 border-gray-200 hover:border-amber-300'
                          }`}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Clock className="w-4 h-4 inline mr-1" />
                          Draft
                        </motion.button>
                        <motion.button
                          onClick={() => setVoucherSaveOption('publish')}
                          className={`px-4 py-2 rounded-xl text-sm font-medium transition-all ${
                            voucherSaveOption === 'publish'
                              ? 'bg-emerald-100 text-emerald-700 border-2 border-emerald-300'
                              : 'bg-white text-gray-600 border-2 border-gray-200 hover:border-emerald-300'
                          }`}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Play className="w-4 h-4 inline mr-1" />
                          Publish
                        </motion.button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex space-x-4">
                    <motion.button
                      onClick={() => setShowCreateVoucher(false)}
                      className="px-6 py-3 text-gray-700 hover:text-gray-900 transition-all duration-300 font-semibold rounded-xl hover:bg-white/50"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Cancel
                    </motion.button>
                    <motion.button
                      onClick={() => {
                        if (!newVoucher.title?.trim()) {
                          alert('Please enter a voucher title');
                          return;
                        }
                        if (!newVoucher.description?.trim()) {
                          alert('Please enter a voucher description');
                          return;
                        }
                        if (!newVoucher.reward?.toString().trim()) {
                          alert('Please enter a referrer reward amount');
                          return;
                        }
                        createVoucher();
                      }}
                      disabled={!newVoucher.title || !newVoucher.description || !newVoucher.reward}
                      className={`px-8 py-3 rounded-xl font-bold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl ${
                        newVoucher.id && selectedCampaign?.vouchers.find(v => v.id === newVoucher.id)?.status === 'published'
                          ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700'
                          : voucherSaveOption === 'draft'
                          ? 'bg-gradient-to-r from-amber-600 to-orange-600 text-white hover:from-amber-700 hover:to-orange-700'
                          : 'bg-gradient-to-r from-emerald-600 to-teal-600 text-white hover:from-emerald-700 hover:to-teal-700'
                      }`}
                      whileHover={{ y: -2, scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-center space-x-2">
                        {newVoucher.id && selectedCampaign?.vouchers.find(v => v.id === newVoucher.id)?.status === 'published' ? (
                          <>
                            <Edit className="w-4 h-4" />
                            <span>Apply Changes</span>
                          </>
                        ) : voucherSaveOption === 'draft' ? (
                          <>
                            <Clock className="w-4 h-4" />
                            <span>Save Draft</span>
                          </>
                        ) : (
                          <>
                            <Play className="w-4 h-4" />
                            <span>Create & Publish</span>
                          </>
                        )}
                        <ChevronRight className="w-4 h-4" />
                      </div>
                    </motion.button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Business Setup Modal */}
      <AnimatePresence>
        {needsBusinessSetup && (
          <div className="fixed inset-0 z-50">
            <QuickBusinessSetup
              context="campaign"
              onComplete={(businessData) => {
                console.log('Business setup completed:', businessData);
                setNeedsBusinessSetup(false);
                createCampaign();
              }}
              onSkip={() => {
                setNeedsBusinessSetup(false);
                createCampaign();
              }}
            />
          </div>
        )}
      </AnimatePresence>

      {/* Voucher Preview Modal */}
      <AnimatePresence>
        {voucherPreview && (
          <VoucherPreviewModal 
            voucher={voucherPreview} 
            onClose={() => setVoucherPreview(null)} 
          />
        )}
      </AnimatePresence>

      {/* Enhanced Confirmation Dialog */}
      <AnimatePresence>
        {confirmDialog.isOpen && (
          <motion.div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setConfirmDialog(prev => ({ ...prev, isOpen: false }));
              }
            }}
          >
            <motion.div
              className="bg-white rounded-3xl max-w-md w-full overflow-hidden shadow-2xl"
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
            >
              {/* Header */}
              <div className={`px-6 py-4 text-white ${
                confirmDialog.type === 'publish' 
                  ? 'bg-gradient-to-r from-emerald-600 to-teal-600'
                  : confirmDialog.type === 'unpublish'
                  ? 'bg-gradient-to-r from-blue-600 to-indigo-600'
                  : 'bg-gradient-to-r from-red-600 to-pink-600'
              }`}>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                    {confirmDialog.type === 'publish' && <Play className="w-6 h-6" />}
                    {confirmDialog.type === 'unpublish' && <Pause className="w-6 h-6" />}
                    {confirmDialog.type === 'archive' && <Archive className="w-6 h-6" />}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold">
                      {confirmDialog.type === 'publish' && 'Publish Voucher'}
                      {confirmDialog.type === 'unpublish' && 'Unpublish Voucher'}
                      {confirmDialog.type === 'archive' && 'Archive Voucher'}
                    </h3>
                    <p className="text-sm opacity-90">
                      {confirmDialog.type === 'publish' && 'Make your voucher live'}
                      {confirmDialog.type === 'unpublish' && 'Take your voucher offline'}
                      {confirmDialog.type === 'archive' && 'Permanently disable voucher'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="mb-6">
                  <div className="bg-gray-50 rounded-xl p-4 border border-gray-200 mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <Gift className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-semibold text-gray-900 truncate">{confirmDialog.voucher?.title}</h4>
                        <p className="text-sm text-gray-600">
                          Current status: <span className="font-medium capitalize">{confirmDialog.voucher?.status}</span>
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {confirmDialog.type === 'publish' && (
                      <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-4">
                        <div className="flex items-start space-x-3">
                          <CheckCircle className="w-5 h-5 text-emerald-600 flex-shrink-0 mt-0.5" />
                          <div>
                            <h5 className="font-medium text-emerald-800 mb-1">What happens when you publish:</h5>
                            <ul className="text-sm text-emerald-700 space-y-1">
                              <li>• Voucher becomes visible to your target audience</li>
                              <li>• Customers can start sharing and earning rewards</li>
                              <li>• Analytics tracking begins immediately</li>
                              <li>• You can still edit by creating staged revisions</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}

                    {confirmDialog.type === 'unpublish' && (
                      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                        <div className="flex items-start space-x-3">
                          <AlertCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                          <div>
                            <h5 className="font-medium text-blue-800 mb-1">What happens when you unpublish:</h5>
                            <ul className="text-sm text-blue-700 space-y-1">
                              <li>• Voucher becomes invisible to customers</li>
                              <li>• Existing shares remain but can't be redeemed</li>
                              <li>• You can edit freely and republish later</li>
                              <li>• All analytics data is preserved</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}

                    {confirmDialog.type === 'archive' && (
                      <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                        <div className="flex items-start space-x-3">
                          <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
                          <div>
                            <h5 className="font-medium text-red-800 mb-1">⚠️ This action cannot be undone:</h5>
                            <ul className="text-sm text-red-700 space-y-1">
                              <li>• Voucher will be permanently disabled</li>
                              <li>• Cannot be restored or republished</li>
                              <li>• Hidden from campaign lists by default</li>
                              <li>• Analytics data will be preserved</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex space-x-3">
                  <motion.button
                    onClick={() => setConfirmDialog(prev => ({ ...prev, isOpen: false }))}
                    className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-medium transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Cancel
                  </motion.button>
                  <motion.button
                    onClick={confirmDialog.onConfirm}
                    className={`flex-1 px-4 py-3 text-white rounded-xl font-semibold transition-all shadow-lg hover:shadow-xl ${
                      confirmDialog.type === 'publish'
                        ? 'bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700'
                        : confirmDialog.type === 'unpublish'
                        ? 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700'
                        : 'bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700'
                    }`}
                    whileHover={{ scale: 1.02, y: -1 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {confirmDialog.type === 'publish' && 'Yes, Publish Voucher'}
                    {confirmDialog.type === 'unpublish' && 'Yes, Unpublish'}
                    {confirmDialog.type === 'archive' && 'Yes, Archive Forever'}
                  </motion.button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Upgrade Modal */}
      <AnimatePresence>
        {showUpgradeModal && (
          <motion.div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowUpgradeModal(false);
              }
            }}
          >
            <motion.div
              className="bg-white rounded-3xl max-w-4xl w-full overflow-hidden shadow-2xl"
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
            >
              {/* Header */}
              <div className="bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 px-8 py-6 text-white relative overflow-hidden">
                <div className="absolute inset-0 bg-black/10"></div>
                <div className="relative flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                      <Star className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-3xl font-bold text-white mb-2">Upgrade to Premium</h3>
                      <p className="text-indigo-100 text-lg">Unlock higher quantity limits and advanced features</p>
                    </div>
                  </div>
                  <motion.button
                    onClick={() => setShowUpgradeModal(false)}
                    className="p-4 hover:bg-white/20 rounded-xl transition-all duration-300 backdrop-blur-sm"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <X className="w-6 h-6 text-white" />
                  </motion.button>
                </div>
              </div>

              {/* Content */}
              <div className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Free Plan */}
                  <div className={`border-2 rounded-2xl p-6 relative ${
                    currentTier === 'free' 
                      ? 'border-gray-400 bg-gray-50' 
                      : 'border-gray-200 bg-white opacity-75'
                  }`}>
                    <div className="text-center mb-6">
                      <h4 className="text-2xl font-bold text-gray-900 mb-2">Free</h4>
                      <div className="text-4xl font-bold text-gray-900 mb-2">$0</div>
                      <p className="text-gray-600">per month</p>
                    </div>

                    <div className="space-y-4 mb-6">
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span>2 locations</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span>5 active campaigns</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span className="font-semibold">100 voucher quantity limit</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span>Basic analytics</span>
                      </div>
                    </div>

                    {currentTier === 'free' && (
                      <div className="bg-gray-200 text-gray-600 py-3 rounded-xl text-center font-semibold">
                        Current Plan
                      </div>
                    )}
                  </div>

                  {/* Basic Plan */}
                  <div className={`border-2 rounded-2xl p-6 relative ${
                    currentTier === 'basic' 
                      ? 'border-blue-400 bg-blue-50' 
                      : 'border-blue-200 bg-white hover:border-blue-300 hover:shadow-lg transition-all cursor-pointer'
                  }`}>
                    <div className="text-center mb-6">
                      <h4 className="text-2xl font-bold text-gray-900 mb-2">Basic</h4>
                      <div className="text-4xl font-bold text-blue-600 mb-2">$29</div>
                      <p className="text-gray-600">per month</p>
                    </div>

                    <div className="space-y-4 mb-6">
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span>5 locations</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span>15 active campaigns</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span className="font-semibold">250 voucher quantity limit</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span>Advanced analytics</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span>Email support</span>
                      </div>
                    </div>

                    {currentTier === 'basic' ? (
                      <div className="bg-blue-200 text-blue-700 py-3 rounded-xl text-center font-semibold">
                        Current Plan
                      </div>
                    ) : (
                      <motion.button
                        onClick={() => {
                          setCurrentTier('basic');
                          setShowUpgradeModal(false);
                          setNewVoucher(prev => ({ ...prev, quantityLimit: Math.min(prev.quantityLimit || 100, 250) }));
                        }}
                        className="w-full bg-blue-600 text-white py-3 rounded-xl font-semibold hover:bg-blue-700 transition-colors"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        Choose Basic
                      </motion.button>
                    )}
                  </div>

                  {/* Premium Plan */}
                  <div className="border-2 rounded-2xl p-6 relative border-purple-300 bg-gradient-to-br from-purple-50 to-pink-50 hover:border-purple-400 hover:shadow-xl transition-all cursor-pointer">
                    <div className="absolute top-3 right-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                      MOST POPULAR
                    </div>
                    
                    <div className="text-center mb-6">
                      <h4 className="text-2xl font-bold text-gray-900 mb-2">Premium</h4>
                      <div className="text-4xl font-bold text-purple-600 mb-2">$99</div>
                      <p className="text-gray-600">per month</p>
                    </div>

                    <div className="space-y-4 mb-6">
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span>Unlimited locations</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span>Unlimited campaigns</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span className="font-semibold">1,000 voucher quantity limit</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span>Real-time analytics</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span>Priority support</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span>Custom branding</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <CheckCircle className="w-5 h-5 text-emerald-500 mr-3" />
                        <span>API access</span>
                      </div>
                    </div>

                    {currentTier === 'premium' ? (
                      <div className="bg-purple-200 text-purple-700 py-3 rounded-xl text-center font-semibold">
                        Current Plan
                      </div>
                    ) : (
                      <motion.button
                        onClick={() => {
                          setCurrentTier('premium');
                          setShowUpgradeModal(false);
                          setNewVoucher(prev => ({ ...prev, quantityLimit: Math.min(prev.quantityLimit || 100, 1000) }));
                        }}
                        className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 rounded-xl font-semibold hover:from-purple-700 hover:to-pink-700 transition-all shadow-lg hover:shadow-xl"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        Choose Premium
                      </motion.button>
                    )}
                  </div>
                </div>

                {/* Quantity Limit Highlight */}
                <div className="mt-8 p-6 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-2xl">
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <Zap className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h5 className="font-bold text-yellow-800 mb-2">Voucher Quantity Limits by Plan</h5>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div className="text-center p-3 bg-white rounded-xl border border-yellow-200">
                          <div className="font-bold text-gray-900">Free Plan</div>
                          <div className="text-yellow-600 font-semibold">100 vouchers max</div>
                        </div>
                        <div className="text-center p-3 bg-white rounded-xl border border-yellow-200">
                          <div className="font-bold text-gray-900">Basic Plan</div>
                          <div className="text-blue-600 font-semibold">250 vouchers max</div>
                        </div>
                        <div className="text-center p-3 bg-white rounded-xl border border-yellow-200">
                          <div className="font-bold text-gray-900">Premium Plan</div>
                          <div className="text-purple-600 font-semibold">1,000 vouchers max</div>
                        </div>
                      </div>
                      <p className="text-yellow-700 mt-3 text-sm">
                        Higher quantity limits mean more customers can claim your vouchers, leading to greater reach and more referrals for your business.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Success Toast */}
      <AnimatePresence>
        {showSuccessToast && (
          <motion.div
            className="fixed top-6 right-6 bg-gradient-to-r from-emerald-500 to-teal-600 text-white px-8 py-6 rounded-2xl shadow-2xl z-50 max-w-md"
            initial={{ opacity: 0, y: -100, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -100, scale: 0.9 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
          >
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                <CheckCircle className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-lg font-bold text-white mb-1">Campaign Created Successfully! 🎉</h4>
                <p className="text-emerald-100 text-sm leading-relaxed">
                  Your campaign is ready to go. Add vouchers below to start driving referrals and grow your business.
                </p>
                <motion.button
                  onClick={() => setShowCreateVoucher(true)}
                  className="mt-3 bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create First Voucher
                </motion.button>
              </div>
              <button
                onClick={() => setShowSuccessToast(false)}
                className="text-white/70 hover:text-white transition-colors flex-shrink-0"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CampaignManager;