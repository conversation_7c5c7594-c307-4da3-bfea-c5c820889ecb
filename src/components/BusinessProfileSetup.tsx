import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Building,
  MapPin,
  Globe,
  Phone,
  Upload,
  Plus,
  Trash2,
  Clock,
  Save,
  ArrowRight,
  Map,
  Navigation,
  X,
  AlertCircle,
  CheckCircle2,
  Mail,
  Calendar,
  Star,
  Users,
  Tag,
  Edit3,
  UtensilsCrossed as Restaurant,
  ShoppingBag,
  Heart,
  Briefcase,
  Music,
  Plane
} from 'lucide-react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { businessProfileService } from '../services/businessProfileService';

// MapBox access token
const MAPBOX_TOKEN = 'pk.eyJ1IjoicmVmZXJpdCIsImEiOiJjbWM3dHc0dG4wemZkMmxwczJqdDlscWluIn0.lSVSivp2KObgNsO_oP_4YA';

// Set MapBox token globally
mapboxgl.accessToken = MAPBOX_TOKEN;

// Icon component mapping for business categories
const getCategoryIcon = (iconName: string) => {
  const iconMap = {
    restaurant: Restaurant,
    'shopping-bag': ShoppingBag,
    heart: Heart,
    briefcase: Briefcase,
    music: Music,
    plane: Plane
  };

  const IconComponent = iconMap[iconName as keyof typeof iconMap];
  return IconComponent || Building;
};

// Business categories that will be used for all vouchers from this business
const businessCategories = {
  'food-beverage': {
    label: 'Food & Beverage',
    icon: 'restaurant',
    color: 'orange',
    tags: ['restaurant', 'cafe', 'bar', 'delivery', 'takeout', 'happy-hour', 'brunch', 'dinner']
  },
  'retail-shopping': {
    label: 'Retail & Shopping',
    icon: 'shopping-bag',
    color: 'pink',
    tags: ['clothing', 'electronics', 'home-goods', 'books', 'gifts', 'accessories', 'seasonal-sale']
  },
  'health-beauty': {
    label: 'Health & Beauty',
    icon: 'heart',
    color: 'purple',
    tags: ['spa', 'salon', 'fitness', 'wellness', 'skincare', 'massage', 'yoga']
  },
  'services': {
    label: 'Professional Services',
    icon: 'briefcase',
    color: 'blue',
    tags: ['consulting', 'repair', 'cleaning', 'legal', 'finance', 'photography', 'tutoring']
  },
  'entertainment': {
    label: 'Entertainment',
    icon: 'music',
    color: 'green',
    tags: ['movies', 'concerts', 'games', 'events', 'sports', 'theater', 'nightlife']
  },
  'travel-hospitality': {
    label: 'Travel & Hospitality',
    icon: 'plane',
    color: 'teal',
    tags: ['hotel', 'flights', 'tours', 'accommodation', 'car-rental', 'vacation']
  }
};

interface BusinessLocation {
  id: string;
  name: string;
  address: string;
  addressLine2?: string;
  city: string;
  state: string;
  zipCode: string;
  country?: string;
  phone?: string;
  website?: string;
  usePrimaryWebsite?: boolean;
  operatingHours: {
    [key: string]: { open: string; close: string; closed: boolean };
  };
  usePrimaryHours?: boolean;
  businessType?: 'physical' | 'service';
  businessCategory?: string;
  businessTags?: string[];
  usePrimaryCategory?: boolean;
  usePrimaryTags?: boolean;
  isPrimary?: boolean;
  canBeDeleted?: boolean;
  coordinates?: { lat: number; lng: number };
  formattedAddress?: string;
}

interface BusinessProfileData {
  businessName: string;
  primaryAddress: string;
  website: string;
  description: string;
  phoneNumber: string;
  logoUrl: string;
  contactEmail: string;
  locations: BusinessLocation[];
  businessCategory?: string;
  businessTags?: string[];
  businessType?: 'physical' | 'service';
  serviceArea?: string;
  primaryHours?: {
    [key: string]: { open: string; close: string; closed: boolean };
  };
  primaryAddressCoordinates?: { lat: number; lng: number };
  formattedAddress?: string;
  timezone?: string;
  isOpen24Hours?: boolean;
  subscriptionPlan?: 'free' | 'basic' | 'premium' | 'enterprise';
  locationLimit?: number;
  maxFreeLocations?: number;
  primaryProfileLocked?: boolean;
}

interface BusinessProfileSetupProps {
  onComplete: (profileData: BusinessProfileData) => void;
  existingData?: Partial<BusinessProfileData>;
}

const BusinessProfileSetup: React.FC<BusinessProfileSetupProps> = ({
  onComplete,
  existingData
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [dragOver, setDragOver] = useState(false);
  const [customTagInput, setCustomTagInput] = useState('');
  const [showNewLocationForm, setShowNewLocationForm] = useState(false);
  const [editingLocationId, setEditingLocationId] = useState<string | null>(null);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const primaryMapContainer = useRef<HTMLDivElement>(null);
  const newLocationMapContainer = useRef<HTMLDivElement>(null);
  const primaryMap = useRef<mapboxgl.Map | null>(null);
  const newLocationMap = useRef<mapboxgl.Map | null>(null);

  const [profileData, setProfileData] = useState<BusinessProfileData>({
    businessName: existingData?.businessName || '',
    primaryAddress: existingData?.primaryAddress || '',
    website: existingData?.website || '',
    description: existingData?.description || '',
    phoneNumber: existingData?.phoneNumber || '',
    logoUrl: existingData?.logoUrl || '',
    contactEmail: existingData?.contactEmail || '',
    locations: existingData?.locations || [],
    businessCategory: existingData?.businessCategory || '',
    businessTags: existingData?.businessTags || [],
    businessType: existingData?.businessType || 'physical', // 'physical' or 'service'
    serviceArea: existingData?.serviceArea || '',
    primaryHours: existingData?.primaryHours || {
      monday: { open: '09:00', close: '17:00', closed: false },
      tuesday: { open: '09:00', close: '17:00', closed: false },
      wednesday: { open: '09:00', close: '17:00', closed: false },
      thursday: { open: '09:00', close: '17:00', closed: false },
      friday: { open: '09:00', close: '17:00', closed: false },
      saturday: { open: '10:00', close: '16:00', closed: false },
      sunday: { open: '10:00', close: '16:00', closed: true }
    },
    primaryAddressCoordinates: existingData?.primaryAddressCoordinates || undefined,
    formattedAddress: existingData?.formattedAddress || '',
    timezone: existingData?.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
    isOpen24Hours: existingData?.isOpen24Hours || false,
    subscriptionPlan: existingData?.subscriptionPlan || 'free',
    locationLimit: existingData?.locationLimit || 2, // Free tier gets 2 locations
    maxFreeLocations: 2,
    primaryProfileLocked: existingData?.primaryProfileLocked || false
  });

  const [newLocation, setNewLocation] = useState<Partial<BusinessLocation>>({
    name: '',
    address: '',
    addressLine2: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'United States',
    phone: '',
    website: '',
    usePrimaryWebsite: true, // Default to using primary website
    usePrimaryHours: true, // Default to using primary hours
    businessType: 'physical',
    businessCategory: '',
    businessTags: [],
    usePrimaryCategory: true, // Default to using primary category
    usePrimaryTags: true, // Default to using primary tags
    isPrimary: false,
    canBeDeleted: true,
    isOpen24Hours: false,
    operatingHours: {
      monday: { open: '09:00', close: '17:00', closed: false },
      tuesday: { open: '09:00', close: '17:00', closed: false },
      wednesday: { open: '09:00', close: '17:00', closed: false },
      thursday: { open: '09:00', close: '17:00', closed: false },
      friday: { open: '09:00', close: '17:00', closed: false },
      saturday: { open: '10:00', close: '16:00', closed: false },
      sunday: { open: '10:00', close: '16:00', closed: true }
    }
  });

  // Address validation and geocoding
  const [addressValidation, setAddressValidation] = useState<{
    isValidating: boolean;
    isValid: boolean | null;
    suggestion: string | null;
    coordinates?: { lat: number; lng: number };
  }>({
    isValidating: false,
    isValid: null,
    suggestion: null
  });

  // Autocomplete suggestions
  const [addressSuggestions, setAddressSuggestions] = useState<Array<{
    display_name: string;
    lat: number;
    lon: number;
  }>>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // New location autocomplete suggestions
  const [newLocationSuggestions, setNewLocationSuggestions] = useState<Array<{
    display_name: string;
    lat: number;
    lon: number;
  }>>([]);
  const [showNewLocationSuggestions, setShowNewLocationSuggestions] = useState(false);

  // New location address validation
  const [newLocationValidation, setNewLocationValidation] = useState<{
    isValidating: boolean;
    isValid: boolean | null;
    suggestion: string | null;
    coordinates?: { lat: number; lng: number };
  }>({
    isValidating: false,
    isValid: null,
    suggestion: null
  });

  // Autocomplete search function
  const searchAddresses = async (query: string) => {
    if (!query || query.length < 3) {
      setAddressSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=5&addressdetails=1&countrycodes=us`
      );

      if (response.ok) {
        const data = await response.json();
        setAddressSuggestions(data);
        setShowSuggestions(true);
      }
    } catch (error) {
      console.error('Autocomplete search failed:', error);
    }
  };

  // Autocomplete search for new locations
  const searchAddressesForNewLocation = async (query: string) => {
    if (!query || query.length < 3) {
      setNewLocationSuggestions([]);
      setShowNewLocationSuggestions(false);
      return;
    }

    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=5&addressdetails=1&countrycodes=us`
      );

      if (response.ok) {
        const data = await response.json();
        setNewLocationSuggestions(data);
        setShowNewLocationSuggestions(true);
      }
    } catch (error) {
      console.error('New location autocomplete search failed:', error);
    }
  };

  // Real geocoding function using OpenStreetMap Nominatim (free) or Google Maps
  const geocodeAddress = async (address: string, city: string, state: string, zipCode: string) => {
    const fullAddress = `${address}, ${city}, ${state} ${zipCode}`.trim();

    try {
      setNewLocationValidation({ isValidating: true, isValid: null, suggestion: null });

      // Using OpenStreetMap Nominatim (free alternative to Google Maps)
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(fullAddress)}&limit=1&addressdetails=1`
      );

      if (!response.ok) throw new Error('Geocoding failed');

      const data = await response.json();

      if (data && data.length > 0) {
        const result = data[0];
        setNewLocationValidation({
          isValidating: false,
          isValid: true,
          suggestion: result.display_name,
          coordinates: { lat: parseFloat(result.lat), lng: parseFloat(result.lon) }
        });

        return {
          lat: parseFloat(result.lat),
          lng: parseFloat(result.lon),
          formatted_address: result.display_name
        };
      } else {
        setNewLocationValidation({
          isValidating: false,
          isValid: false,
          suggestion: 'Address not found. Please verify the details.'
        });
        throw new Error('Address not found');
      }
    } catch (error) {
      setNewLocationValidation({
        isValidating: false,
        isValid: false,
        suggestion: 'Unable to validate address. Please check your input.'
      });

      // Return fallback coordinates if validation fails
      return {
        lat: 0,
        lng: 0,
        formatted_address: fullAddress
      };
    }
  };

  // Service area validation state
  const [serviceAreaValidation, setServiceAreaValidation] = useState<{
    isValidating: boolean;
    isValid: boolean | null;
    suggestion: string | null;
    coordinates?: { lat: number; lng: number };
  }>({
    isValidating: false,
    isValid: null,
    suggestion: null
  });

  // Generic address validation function for consistency
  const validateAddressWithOpenStreetMap = async (address: string, type: 'address' | 'serviceArea' = 'address') => {
    if (!address || address.length < 3) {
      return { isValid: null, suggestion: null, coordinates: null };
    }

    try {
      // For service areas, include region and area search parameters
      const searchParams = type === 'serviceArea' 
        ? `q=${encodeURIComponent(address)}&limit=1&addressdetails=1&extratags=1&namedetails=1&countrycodes=us`
        : `q=${encodeURIComponent(address)}&limit=1&addressdetails=1&countrycodes=us`;

      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&${searchParams}`
      );

      const data = await response.json();

      if (data && data.length > 0) {
        const result = data[0];
        const coordinates = { lat: parseFloat(result.lat), lng: parseFloat(result.lon) };
        
        // For service areas, validate that it's a recognized city, county, or state
        if (type === 'serviceArea') {
          const addressComponents = result.address;
          const validServiceAreaTypes = ['city', 'town', 'village', 'county', 'state', 'country'];
          const hasValidType = Object.keys(addressComponents).some(key => 
            validServiceAreaTypes.includes(key) || 
            key.includes('administrative') ||
            result.type === 'administrative' ||
            result.class === 'boundary'
          );

          if (!hasValidType && !result.display_name.includes('County') && !result.display_name.includes('State')) {
            return {
              isValid: false,
              suggestion: 'Please enter a valid city, county, or state for your service area.',
              coordinates: null
            };
          }
        }

        return {
          isValid: true,
          suggestion: result.display_name,
          coordinates
        };
      } else {
        return {
          isValid: false,
          suggestion: type === 'serviceArea' 
            ? 'Service area not recognized. Please enter a valid city, county, or state.'
            : 'Address not recognized. Please verify the details.',
          coordinates: null
        };
      }
    } catch (error) {
      return {
        isValid: false,
        suggestion: type === 'serviceArea' 
          ? 'Unable to validate service area.'
          : 'Unable to validate address.',
        coordinates: null
      };
    }
  };

  // Debounced address validation for real-time feedback
  const validatePrimaryAddress = async (address: string) => {
    if (!address || address.length < 10) {
      setAddressValidation({ isValidating: false, isValid: null, suggestion: null });
      return;
    }

    setAddressValidation({ isValidating: true, isValid: null, suggestion: null });
    
    const result = await validateAddressWithOpenStreetMap(address, 'address');
    
    setAddressValidation({
      isValidating: false,
      isValid: result.isValid,
      suggestion: result.suggestion,
      coordinates: result.coordinates
    });
  };

  // Service area validation
  const validateServiceArea = async (serviceArea: string) => {
    if (!serviceArea || serviceArea.length < 3) {
      setServiceAreaValidation({ isValidating: false, isValid: null, suggestion: null });
      return;
    }

    setServiceAreaValidation({ isValidating: true, isValid: null, suggestion: null });
    
    const result = await validateAddressWithOpenStreetMap(serviceArea, 'serviceArea');
    
    setServiceAreaValidation({
      isValidating: false,
      isValid: result.isValid,
      suggestion: result.suggestion,
      coordinates: result.coordinates
    });
  };

  // Debounce the validation and autocomplete
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (profileData.primaryAddress) {
        validatePrimaryAddress(profileData.primaryAddress);
        searchAddresses(profileData.primaryAddress);
      }
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [profileData.primaryAddress]);

  // Debounce service area validation
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (profileData.serviceArea && profileData.businessType === 'service') {
        validateServiceArea(profileData.serviceArea);
      }
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [profileData.serviceArea, profileData.businessType]);

  // Debounce new location address validation
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (newLocation.address && newLocation.address.length > 10) {
        validateNewLocationAddress();
      } else {
        setNewLocationValidation({ isValidating: false, isValid: null, suggestion: null });
      }
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [newLocation.address]);

  // Validate new location address
  const validateNewLocationAddress = async () => {
    if (!newLocation.address) {
      setNewLocationValidation({ isValidating: false, isValid: null, suggestion: null });
      return;
    }

    setNewLocationValidation({ isValidating: true, isValid: null, suggestion: null });
    
    const result = await validateAddressWithOpenStreetMap(newLocation.address, 'address');
    
    setNewLocationValidation({
      isValidating: false,
      isValid: result.isValid,
      suggestion: result.suggestion,
      coordinates: result.coordinates
    });
  };

  const addLocation = async () => {
    if (newLocation.name && newLocation.address) {
      // Check if address validation passed
      if (newLocationValidation.isValid !== true) {
        alert('Please enter a valid address. The address validation must pass before adding the location.');
        return;
      }

      // Check location limits
      const currentLocationCount = profileData.locations.length + 1; // +1 for primary location
      if (currentLocationCount >= (profileData.locationLimit || 2) && profileData.subscriptionPlan === 'free') {
        alert(`You've reached the limit of ${profileData.maxFreeLocations} locations on the free plan. Upgrade to add more locations!`);
        return;
      }

      // Parse address from the validated suggestion or input
      const addressParts = newLocationValidation.suggestion 
        ? newLocationValidation.suggestion.split(', ')
        : newLocation.address!.split(', ');

      const location: BusinessLocation = {
        id: Date.now().toString(),
        name: newLocation.name!,
        address: addressParts[0] || newLocation.address!,
        addressLine2: newLocation.addressLine2 || '',
        city: addressParts[1] || '',
        state: addressParts[2] || '',
        zipCode: addressParts[3] || '',
        country: 'United States',
        phone: newLocation.phone || '',
        website: newLocation.usePrimaryWebsite ? '' : (newLocation.website || ''),
        usePrimaryWebsite: newLocation.usePrimaryWebsite!,
        operatingHours: newLocation.usePrimaryHours ?
          (profileData.primaryHours || newLocation.operatingHours!) :
          newLocation.operatingHours!,
        usePrimaryHours: newLocation.usePrimaryHours!,
        businessType: newLocation.usePrimaryCategory ? profileData.businessType : newLocation.businessType,
        businessCategory: newLocation.usePrimaryCategory ? profileData.businessCategory : newLocation.businessCategory,
        businessTags: newLocation.usePrimaryTags ? profileData.businessTags : newLocation.businessTags,
        usePrimaryCategory: newLocation.usePrimaryCategory!,
        usePrimaryTags: newLocation.usePrimaryTags!,
        isPrimary: false,
        canBeDeleted: true,
        coordinates: newLocationValidation.coordinates || { lat: 0, lng: 0 },
        formattedAddress: newLocationValidation.suggestion || `${newLocation.address}, ${newLocation.city}, ${newLocation.state} ${newLocation.zipCode}`
      };

      setProfileData(prev => ({
        ...prev,
        locations: [...prev.locations, location]
      }));

      // Reset form
      setNewLocation({
        name: '',
        address: '',
        addressLine2: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'United States',
        phone: '',
        website: '',
        usePrimaryWebsite: true,
        usePrimaryHours: true,
        businessType: 'physical',
        businessCategory: '',
        businessTags: [],
        usePrimaryCategory: true,
        usePrimaryTags: true,
        isPrimary: false,
        canBeDeleted: true,
        isOpen24Hours: false,
        operatingHours: {
          monday: { open: '09:00', close: '17:00', closed: false },
          tuesday: { open: '09:00', close: '17:00', closed: false },
          wednesday: { open: '09:00', close: '17:00', closed: false },
          thursday: { open: '09:00', close: '17:00', closed: false },
          friday: { open: '09:00', close: '17:00', closed: false },
          saturday: { open: '10:00', close: '16:00', closed: false },
          sunday: { open: '10:00', close: '16:00', closed: true }
        }
      });

      // Reset location validation state
      setNewLocationValidation({
        isValidating: false,
        isValid: null,
        suggestion: null
      });

      // Close the form
      setShowNewLocationForm(false);
    }
  };

  const removeLocation = (locationId: string) => {
    setProfileData(prev => ({
      ...prev,
      locations: prev.locations.filter(loc => loc.id !== locationId)
    }));
  };

  const editLocation = (locationId: string) => {
    const locationToEdit = profileData.locations.find(loc => loc.id === locationId);
    if (locationToEdit) {
      // Convert location back to newLocation format for editing
      setNewLocation({
        name: locationToEdit.name,
        address: locationToEdit.address,
        addressLine2: locationToEdit.addressLine2,
        city: locationToEdit.city,
        state: locationToEdit.state,
        zipCode: locationToEdit.zipCode,
        country: locationToEdit.country,
        phone: locationToEdit.phone,
        website: locationToEdit.website,
        usePrimaryWebsite: locationToEdit.usePrimaryWebsite,
        usePrimaryHours: locationToEdit.usePrimaryHours,
        businessType: locationToEdit.businessType,
        businessCategory: locationToEdit.businessCategory,
        businessTags: locationToEdit.businessTags,
        usePrimaryCategory: locationToEdit.usePrimaryCategory,
        usePrimaryTags: locationToEdit.usePrimaryTags,
        isPrimary: false,
        canBeDeleted: true,
        isOpen24Hours: false,
        operatingHours: locationToEdit.operatingHours
      });
      
      // Set validation state for the address
      setNewLocationValidation({
        isValidating: false,
        isValid: true,
        suggestion: locationToEdit.formattedAddress || `${locationToEdit.address}, ${locationToEdit.city}, ${locationToEdit.state} ${locationToEdit.zipCode}`,
        coordinates: locationToEdit.coordinates
      });
      
      setEditingLocationId(locationId);
      setShowNewLocationForm(true);
    }
  };

  const updateLocation = async () => {
    if (editingLocationId && newLocation.name && newLocation.address) {
      // Check if address validation passed
      if (newLocationValidation.isValid !== true) {
        alert('Please enter a valid address. The address validation must pass before updating the location.');
        return;
      }

      // Parse address from the validated suggestion or input
      const addressParts = newLocationValidation.suggestion 
        ? newLocationValidation.suggestion.split(', ')
        : newLocation.address!.split(', ');

      const updatedLocation: BusinessLocation = {
        id: editingLocationId,
        name: newLocation.name!,
        address: addressParts[0] || newLocation.address!,
        addressLine2: newLocation.addressLine2 || '',
        city: addressParts[1] || '',
        state: addressParts[2] || '',
        zipCode: addressParts[3] || '',
        country: 'United States',
        phone: newLocation.phone || '',
        website: newLocation.usePrimaryWebsite ? '' : (newLocation.website || ''),
        usePrimaryWebsite: newLocation.usePrimaryWebsite!,
        operatingHours: newLocation.usePrimaryHours ?
          (profileData.primaryHours || newLocation.operatingHours!) :
          newLocation.operatingHours!,
        usePrimaryHours: newLocation.usePrimaryHours!,
        businessType: newLocation.usePrimaryCategory ? profileData.businessType : newLocation.businessType,
        businessCategory: newLocation.usePrimaryCategory ? profileData.businessCategory : newLocation.businessCategory,
        businessTags: newLocation.usePrimaryTags ? profileData.businessTags : newLocation.businessTags,
        usePrimaryCategory: newLocation.usePrimaryCategory!,
        usePrimaryTags: newLocation.usePrimaryTags!,
        isPrimary: false,
        canBeDeleted: true,
        coordinates: newLocationValidation.coordinates || { lat: 0, lng: 0 },
        formattedAddress: newLocationValidation.suggestion || `${newLocation.address}, ${newLocation.city}, ${newLocation.state} ${newLocation.zipCode}`
      };

      setProfileData(prev => ({
        ...prev,
        locations: prev.locations.map(loc => 
          loc.id === editingLocationId ? updatedLocation : loc
        )
      }));

      // Reset form and editing state
      setNewLocation({
        name: '',
        address: '',
        addressLine2: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'United States',
        phone: '',
        website: '',
        usePrimaryWebsite: true,
        usePrimaryHours: true,
        businessType: 'physical',
        businessCategory: '',
        businessTags: [],
        usePrimaryCategory: true,
        usePrimaryTags: true,
        isPrimary: false,
        canBeDeleted: true,
        isOpen24Hours: false,
        operatingHours: {
          monday: { open: '09:00', close: '17:00', closed: false },
          tuesday: { open: '09:00', close: '17:00', closed: false },
          wednesday: { open: '09:00', close: '17:00', closed: false },
          thursday: { open: '09:00', close: '17:00', closed: false },
          friday: { open: '09:00', close: '17:00', closed: false },
          saturday: { open: '10:00', close: '16:00', closed: false },
          sunday: { open: '10:00', close: '16:00', closed: true }
        }
      });

      // Reset location validation state
      setNewLocationValidation({
        isValidating: false,
        isValid: null,
        suggestion: null
      });

      setEditingLocationId(null);
      setShowNewLocationForm(false);
    }
  };

  // Enhanced logo upload handlers with better format support and optimization
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const imageFile = files.find(file => isValidImageFile(file));

    if (imageFile) {
      handleLogoUpload(imageFile);
    }
  };

  // Enhanced file validation with comprehensive format support
  const isValidImageFile = (file: File): boolean => {
    const validTypes = [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      'image/bmp',
      'image/tiff'
    ];
    
    const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp', '.tiff'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    
    return validTypes.includes(file.type) || validExtensions.includes(fileExtension);
  };

  // Enhanced logo upload with image optimization
  const handleLogoUpload = async (file: File) => {
    // Validate file size (5MB max for better format support)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size too large. Please choose a file under 5MB.');
      return;
    }

    // Validate file type
    if (!isValidImageFile(file)) {
      alert('Please select a valid image file (JPG, PNG, GIF, WebP, SVG, BMP, TIFF).');
      return;
    }

    try {
      // For SVG files, handle differently
      if (file.type === 'image/svg+xml') {
        const imageUrl = URL.createObjectURL(file);
        setProfileData(prev => ({ ...prev, logoUrl: imageUrl }));
        return;
      }

      // Create optimized image for other formats
      const optimizedImage = await optimizeImage(file);
      setProfileData(prev => ({ ...prev, logoUrl: optimizedImage }));
    } catch (error) {
      console.error('Error processing image:', error);
      alert('Error processing image. Please try a different file.');
    }
  };

  // Image optimization function
  const optimizeImage = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate optimal dimensions (max 512x512 for performance)
        const maxSize = 512;
        let { width, height } = img;
        
        if (width > height) {
          if (width > maxSize) {
            height = (height * maxSize) / width;
            width = maxSize;
          }
        } else {
          if (height > maxSize) {
            width = (width * maxSize) / height;
            height = maxSize;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Enable image smoothing for better quality
        if (ctx) {
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';
          ctx.drawImage(img, 0, 0, width, height);

          // Convert to optimized format
          const quality = file.type === 'image/png' ? 1.0 : 0.85;
          const optimizedDataUrl = canvas.toDataURL('image/webp', quality);
          resolve(optimizedDataUrl);
        } else {
          reject(new Error('Canvas context not available'));
        }
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  };

  const removeLogo = () => {
    setProfileData(prev => ({ ...prev, logoUrl: '' }));
  };

  // Tag management
  const addCustomTag = () => {
    const tag = customTagInput.trim().toLowerCase().replace(/\s+/g, '-');
    if (tag && !(profileData.businessTags || []).includes(tag)) {
      setProfileData(prev => ({
        ...prev,
        businessTags: [...(prev.businessTags || []), tag]
      }));
      setCustomTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setProfileData(prev => ({
      ...prev,
      businessTags: (prev.businessTags || []).filter(tag => tag !== tagToRemove)
    }));
  };

  // Phone number formatting
  const formatPhoneNumber = (value: string) => {
    // Remove all non-digits
    const phoneNumber = value.replace(/\D/g, '');

    // Format as XXX-XXX-XXXX
    if (phoneNumber.length >= 6) {
      return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
    } else if (phoneNumber.length >= 3) {
      return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3)}`;
    }
    return phoneNumber;
  };

  // Website URL normalization
  const normalizeWebsite = (website: string) => {
    if (!website) return '';

    // Remove whitespace
    let url = website.trim();

    // Don't add protocol if it already has one
    if (url.match(/^https?:\/\//)) {
      return url;
    }

    // Remove www. if present (we'll add https://)
    if (url.startsWith('www.')) {
      url = url.substring(4);
    }

    // Add https:// protocol
    return `https://${url}`;
  };

  // Validation helpers
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string) => {
    const phoneNumber = phone.replace(/\D/g, '');
    return phoneNumber.length >= 10;
  };

  const validateWebsite = (website: string) => {
    if (!website) return true; // Optional field

    try {
      const normalizedUrl = normalizeWebsite(website);
      new URL(normalizedUrl);
      return true;
    } catch {
      return false;
    }
  };

  const validateField = (field: string, value: string) => {
    let error = '';

    switch (field) {
      case 'businessName':
        if (!value.trim()) error = 'Business name is required';
        break;
      case 'primaryAddress':
        if (profileData.businessType === 'physical' && !value.trim()) {
          error = 'Primary address is required for physical businesses';
        }
        break;
      case 'serviceArea':
        if (profileData.businessType === 'service' && !value.trim()) {
          error = 'Service area is required for service-based businesses';
        }
        break;
      case 'phoneNumber':
        if (!value.trim()) error = 'Phone number is required';
        else if (!validatePhone(value)) error = 'Please enter a valid phone number';
        break;
      case 'contactEmail':
        if (value && !validateEmail(value)) error = 'Please enter a valid email address';
        break;
      case 'website':
        if (value && !validateWebsite(value)) error = 'Please enter a valid website URL';
        break;
      case 'businessCategory':
        if (!value) error = 'Please select a business category';
        break;
    }

    setValidationErrors(prev => ({
      ...prev,
      [field]: error
    }));

    return !error;
  };

  const handleFieldChange = (field: string, value: string) => {
    setProfileData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (validationErrors[field]) {
      setTimeout(() => validateField(field, value), 500);
    }
  };

  const updateBusinessProfile = useCallback((newData: Partial<BusinessProfileData>) => {
    setProfileData(prev => ({
      ...prev,
      ...newData
    }));
  }, []);

  const handleSubmit = async () => {
    const hasRequiredAddress = profileData.businessType === 'physical' 
      ? profileData.primaryAddress 
      : profileData.serviceArea;
    
    if (profileData.businessName && hasRequiredAddress && profileData.phoneNumber && profileData.businessCategory) {
      try {
        // Include geocoded data in the final profile
        const enhancedProfileData = {
          ...profileData,
          website: normalizeWebsite(profileData.website),
          primaryAddressCoordinates: profileData.businessType === 'physical' 
            ? addressValidation.coordinates 
            : serviceAreaValidation.coordinates,
          formattedAddress: profileData.businessType === 'physical'
            ? (addressValidation.suggestion || profileData.primaryAddress)
            : (serviceAreaValidation.suggestion || profileData.serviceArea),
          serviceAreaCoordinates: profileData.businessType === 'service' 
            ? serviceAreaValidation.coordinates 
            : undefined
        };

        // Get business ID from auth context or localStorage
        const authToken = localStorage.getItem('auth_token');
        const userData = localStorage.getItem('user_data');
        
        console.log('🔍 Debug - Auth token exists:', !!authToken);
        console.log('🔍 Debug - User data exists:', !!userData);
        
        if (!authToken || !userData) {
          alert('Please log in to save your business profile');
          return;
        }

        let user;
        try {
          user = JSON.parse(userData);
          console.log('🔍 Debug - Parsed user data:', {
            id: user.id,
            user_type: user.user_type,
            accountType: user.accountType,
            email: user.email
          });
        } catch (parseError) {
          console.error('❌ Failed to parse user data:', parseError);
          alert('Invalid user data - please log in again');
          return;
        }
        
        if (user.user_type !== 'business' && user.accountType !== 'business') {
          alert('Only business accounts can create business profiles');
          return;
        }

        const businessId = user.id || user.user_id;
        console.log('💾 Saving business profile to database for business ID:', businessId);
        console.log('📋 Profile data being saved:', enhancedProfileData);

        // Save to database using the business profile service
        console.log('🔄 Calling businessProfileService.saveBusinessProfile...');
        const result = await businessProfileService.saveBusinessProfile(businessId, enhancedProfileData);
        
        console.log('📡 Business profile service result:', result);

        if (result.success) {
          console.log('✅ Business profile saved successfully:', result.profile);
          
          // Store the profile data locally for immediate use
          localStorage.setItem('business_profile', JSON.stringify(enhancedProfileData));
          
          // Dispatch event to update other components
          window.dispatchEvent(new CustomEvent('businessProfileUpdated', { 
            detail: enhancedProfileData 
          }));

          // Show success message
          alert('Business profile saved successfully!');

          // Call the completion callback
          onComplete(enhancedProfileData);
        } else {
          console.error('❌ Failed to save business profile:', result.message);
          console.error('❌ Full result:', result);
          
          // Show more helpful error message
          const errorMessage = result.message || 'Unknown error occurred';
          alert(`Failed to save business profile: ${errorMessage}\n\nPlease try again. If the problem persists, please contact support.`);
        }

      } catch (error) {
        console.error('❌ Error saving business profile:', error);
        console.error('❌ Error details:', error.message);
        alert('An error occurred while saving your business profile. Please check the console for details and try again.');
      }
    } else {
      // Show validation errors for missing fields
      const missingFields = [];
      if (!profileData.businessName) missingFields.push('Business Name');
      if (!hasRequiredAddress) missingFields.push(profileData.businessType === 'physical' ? 'Primary Address' : 'Service Area');
      if (!profileData.phoneNumber) missingFields.push('Phone Number');
      if (!profileData.businessCategory) missingFields.push('Business Category');
      
      alert(`Please fill in all required fields: ${missingFields.join(', ')}`);
    }
  };

  const steps = [
    { id: 1, title: 'Basic Information', icon: Building },
    { id: 2, title: 'Locations & Hours', icon: MapPin },
    { id: 3, title: 'Review & Complete', icon: Save }
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Business Name *
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={profileData.businessName}
                    onChange={(e) => handleFieldChange('businessName', e.target.value)}
                    onBlur={(e) => validateField('businessName', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all ${
                      validationErrors.businessName ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    } focus:border-indigo-500`}
                    placeholder="Your Business Name"
                  />
                  {validationErrors.businessName && (
                    <div className="absolute right-3 top-3">
                      <AlertCircle className="w-5 h-5 text-red-500" />
                    </div>
                  )}
                </div>
                {validationErrors.businessName && (
                  <p className="mt-1 text-sm text-red-600 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {validationErrors.businessName}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <div className="relative">
                  <Phone className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type="tel"
                    value={profileData.phoneNumber}
                    onChange={(e) => {
                      const formatted = formatPhoneNumber(e.target.value);
                      handleFieldChange('phoneNumber', formatted);
                    }}
                    onBlur={(e) => validateField('phoneNumber', e.target.value)}
                    className={`w-full pl-12 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all ${
                      validationErrors.phoneNumber ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    } focus:border-indigo-500`}
                    placeholder="************"
                    maxLength={12}
                  />
                  {validationErrors.phoneNumber && (
                    <div className="absolute right-3 top-3">
                      <AlertCircle className="w-5 h-5 text-red-500" />
                    </div>
                  )}
                </div>
                {validationErrors.phoneNumber && (
                  <p className="mt-1 text-sm text-red-600 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {validationErrors.phoneNumber}
                  </p>
                )}
              </div>
            </div>

            {/* Business Type Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Business Type *
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <motion.button
                  type="button"
                  onClick={() => setProfileData(prev => ({ ...prev, businessType: 'physical' }))}
                  className={`p-4 border-2 rounded-xl transition-all duration-200 ${
                    profileData.businessType === 'physical'
                      ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                      : 'border-gray-300 hover:border-indigo-300 text-gray-700'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center space-x-3">
                    <Building className="w-6 h-6" />
                    <div className="text-left">
                      <h4 className="font-semibold">Physical Location</h4>
                      <p className="text-sm opacity-75">Store, office, restaurant, etc.</p>
                    </div>
                  </div>
                </motion.button>

                <motion.button
                  type="button"
                  onClick={() => setProfileData(prev => ({ ...prev, businessType: 'service' }))}
                  className={`p-4 border-2 rounded-xl transition-all duration-200 ${
                    profileData.businessType === 'service'
                      ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                      : 'border-gray-300 hover:border-indigo-300 text-gray-700'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center space-x-3">
                    <Navigation className="w-6 h-6" />
                    <div className="text-left">
                      <h4 className="font-semibold">Service-Based</h4>
                      <p className="text-sm opacity-75">Mobile, online, or home services</p>
                    </div>
                  </div>
                </motion.button>
              </div>
            </div>

            {/* Address or Service Area based on business type */}
            <div>
              {profileData.businessType === 'physical' ? (
                <>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Primary Address *
                  </label>
                </>
              ) : (
                <>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Service Area *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={profileData.serviceArea}
                      onChange={(e) => setProfileData(prev => ({ ...prev, serviceArea: e.target.value }))}
                      className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all mb-4 ${
                        serviceAreaValidation.isValid === true ? 'border-green-300 bg-green-50' :
                        serviceAreaValidation.isValid === false ? 'border-red-300 bg-red-50' :
                        'border-gray-300'
                      } focus:border-indigo-500`}
                      placeholder="e.g., San Diego County, Los Angeles, California, Within 50 miles of downtown"
                    />

                    {serviceAreaValidation.isValidating && (
                      <div className="absolute right-3 top-3">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-600"></div>
                      </div>
                    )}

                    {serviceAreaValidation.isValid === true && (
                      <div className="absolute right-3 top-3">
                        <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                      </div>
                    )}

                    {serviceAreaValidation.isValid === false && (
                      <div className="absolute right-3 top-3">
                        <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">!</span>
                        </div>
                      </div>
                    )}
                  </div>

                  {serviceAreaValidation.suggestion && (
                    <div className={`mt-2 p-3 rounded-lg text-sm mb-4 ${
                      serviceAreaValidation.isValid ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
                    }`}>
                      {serviceAreaValidation.isValid ? (
                        <div>
                          <span className="font-medium">✓ Service area validated:</span>
                          <div className="mt-1 text-xs">{serviceAreaValidation.suggestion}</div>
                        </div>
                      ) : (
                        <div>
                          <span className="font-medium">⚠ {serviceAreaValidation.suggestion}</span>
                        </div>
                      )}
                    </div>
                  )}
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Business Address (Optional)
                  </label>
                </>
              )}
              <div className="relative">
                <input
                  type="text"
                  value={profileData.primaryAddress}
                  onChange={(e) => {
                    handleFieldChange('primaryAddress', e.target.value);
                    setShowSuggestions(false);
                  }}
                  onFocus={() => {
                    if (addressSuggestions.length > 0) {
                      setShowSuggestions(true);
                    }
                  }}
                  onBlur={(e) => {
                    setTimeout(() => setShowSuggestions(false), 150);
                    validateField('primaryAddress', e.target.value);
                  }}
                  className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all ${
                    validationErrors.primaryAddress ? 'border-red-300 bg-red-50' :
                    addressValidation.isValid === true ? 'border-green-300 bg-green-50' :
                    addressValidation.isValid === false ? 'border-red-300 bg-red-50' :
                    'border-gray-300'
                  } focus:border-indigo-500`}
                  placeholder="Start typing your address..."
                />

                {addressValidation.isValidating && (
                  <div className="absolute right-3 top-3">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-600"></div>
                  </div>
                )}

                {addressValidation.isValid === true && (
                  <div className="absolute right-3 top-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  </div>
                )}

                {addressValidation.isValid === false && (
                  <div className="absolute right-3 top-3">
                    <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">!</span>
                    </div>
                  </div>
                )}

                {/* Autocomplete suggestions */}
                {showSuggestions && addressSuggestions.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg max-h-60 overflow-y-auto"
                  >
                    {addressSuggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        type="button"
                        className="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none first:rounded-t-xl last:rounded-b-xl transition-colors"
                        onClick={() => {
                          setProfileData(prev => ({ ...prev, primaryAddress: suggestion.display_name }));
                          setAddressValidation({
                            isValidating: false,
                            isValid: true,
                            suggestion: suggestion.display_name,
                            coordinates: { lat: suggestion.lat, lng: suggestion.lon }
                          });
                          setShowSuggestions(false);
                        }}
                      >
                        <div className="flex items-start">
                          <MapPin className="w-4 h-4 text-gray-400 mt-0.5 mr-3 flex-shrink-0" />
                          <div className="text-sm text-gray-900">{suggestion.display_name}</div>
                        </div>
                      </button>
                    ))}
                  </motion.div>
                )}
              </div>

              {addressValidation.suggestion && (
                <div className={`mt-2 p-3 rounded-lg text-sm ${
                  addressValidation.isValid ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
                }`}>
                  {addressValidation.isValid ? (
                    <div>
                      <span className="font-medium">✓ Address validated:</span>
                      <div className="mt-1 text-xs">{addressValidation.suggestion}</div>
                    </div>
                  ) : (
                    <div>
                      <span className="font-medium">⚠ {addressValidation.suggestion}</span>
                    </div>
                  )}
                </div>
              )}

              {/* Map preview for validated address */}
              {addressValidation.isValid && addressValidation.coordinates && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 200 }}
                  className="mt-4 rounded-xl overflow-hidden border border-gray-200 shadow-sm"
                >
                  <div className="flex items-center justify-between bg-gray-50 px-4 py-2 border-b border-gray-200">
                    <div className="flex items-center text-sm text-gray-600">
                      <Map className="w-4 h-4 mr-2" />
                      Address Location Preview
                    </div>
                    <Navigation className="w-4 h-4 text-gray-400" />
                  </div>
                  <div className="bg-gray-50 rounded-xl p-6 border-2 border-dashed border-gray-300">
                    <div
                      ref={primaryMapContainer}
                      className="h-[300px] w-full rounded-lg relative"
                      style={{ minHeight: '300px', position: 'relative' }}
                    />
                    <p className="text-sm text-gray-500 mt-2 text-center">
                      Click on the map to set your business location
                    </p>
                  </div>
                </motion.div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Website
                </label>
                <div className="relative">
                  <Globe className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={profileData.website}
                    onChange={(e) => handleFieldChange('website', e.target.value)}
                    onBlur={(e) => {
                      const normalized = normalizeWebsite(e.target.value);
                      if (normalized !== e.target.value) {
                        setProfileData(prev => ({ ...prev, website: normalized }));
                      }
                      validateField('website', e.target.value);
                    }}
                    className={`w-full pl-12 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all ${
                      validationErrors.website ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    } focus:border-indigo-500`}
                    placeholder="yourbusiness.com"
                  />
                  {validationErrors.website && (
                    <div className="absolute right-3 top-3">
                      <AlertCircle className="w-5 h-5 text-red-500" />
                    </div>
                  )}
                </div>
                {profileData.website && !validationErrors.website && (
                  <p className="mt-1 text-xs text-gray-500">
                    Will be saved as: {normalizeWebsite(profileData.website)}
                  </p>
                )}
                {validationErrors.website && (
                  <p className="mt-1 text-sm text-red-600 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {validationErrors.website}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Email
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type="email"
                    value={profileData.contactEmail}
                    onChange={(e) => handleFieldChange('contactEmail', e.target.value)}
                    onBlur={(e) => validateField('contactEmail', e.target.value)}
                    className={`w-full pl-12 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all ${
                      validationErrors.contactEmail ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    } focus:border-indigo-500`}
                    placeholder="<EMAIL>"
                  />
                  {validationErrors.contactEmail && (
                    <div className="absolute right-3 top-3">
                      <AlertCircle className="w-5 h-5 text-red-500" />
                    </div>
                  )}
                </div>
                {validationErrors.contactEmail && (
                  <p className="mt-1 text-sm text-red-600 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {validationErrors.contactEmail}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Description
              </label>
              <textarea
                value={profileData.description}
                onChange={(e) => setProfileData(prev => ({ ...prev, description: e.target.value }))}
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Tell customers about your business..."
              />
            </div>

            {/* Business Category Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Business Category *
              </label>
              <p className="text-sm text-gray-500 mb-6">
                This will automatically categorize all your vouchers and help customers find them
              </p>

              {validationErrors.businessCategory && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-2" />
                    {validationErrors.businessCategory}
                  </p>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(businessCategories).map(([key, category]) => {
                  const IconComponent = getCategoryIcon(category.icon);
                  const isSelected = profileData.businessCategory === key;

                  return (
                    <motion.div
                      key={key}
                      className={`relative border-2 rounded-xl p-6 cursor-pointer transition-all group ${
                        isSelected
                          ? 'border-indigo-500 bg-indigo-50 shadow-lg ring-1 ring-indigo-200'
                          : validationErrors.businessCategory
                          ? 'border-red-200 hover:border-red-300 hover:shadow-md bg-white'
                          : 'border-gray-200 hover:border-indigo-300 hover:shadow-md bg-white'
                      }`}
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => {
                        setProfileData(prev => ({
                          ...prev,
                          businessCategory: key,
                          businessTags: category.tags
                        }));
                        validateField('businessCategory', key);
                      }}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 * Object.keys(businessCategories).indexOf(key) }}
                    >
                      {isSelected && (
                        <motion.div
                          className="absolute top-3 right-3 w-6 h-6 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-md z-10"
                          initial={{ scale: 0, rotate: -180 }}
                          animate={{ scale: 1, rotate: 0 }}
                          transition={{ type: "spring", stiffness: 500, delay: 0.2 }}
                        >
                          <CheckCircle2 className="w-3.5 h-3.5 text-white" />
                        </motion.div>
                      )}

                      <div className="relative text-center">
                        <motion.div 
                          className={`w-12 h-12 mx-auto mb-4 rounded-xl flex items-center justify-center transition-all duration-300 ${
                            isSelected 
                              ? 'bg-gradient-to-br from-indigo-500 to-purple-600 text-white shadow-lg' 
                              : 'bg-gray-100 text-gray-600 group-hover:bg-indigo-100 group-hover:text-indigo-600'
                          }`}
                          animate={isSelected ? { rotate: [0, 5, -5, 0] } : {}}
                          transition={{ duration: 0.5 }}
                        >
                          <IconComponent className="w-6 h-6" />
                        </motion.div>
                        <h3 className={`text-sm font-semibold mb-2 transition-colors ${
                          isSelected ? 'text-indigo-700' : 'text-gray-900'
                        }`}>
                          {category.label}
                        </h3>
                        <div className="flex items-center justify-center text-xs text-gray-500 bg-gray-50 rounded-full px-3 py-1 mb-3">
                          <Tag className="w-3 h-3 mr-1" />
                          {category.tags.length} tags
                        </div>

                        {/* Preview tags */}
                        <div className="flex flex-wrap gap-1 justify-center">
                          {category.tags.slice(0, 2).map((tag, index) => (
                            <span 
                              key={index}
                              className="text-xs bg-white text-gray-600 px-2 py-1 rounded-md border border-gray-200"
                            >
                              {tag.replace('-', ' ')}
                            </span>
                          ))}
                          {category.tags.length > 2 && (
                            <span className="text-xs text-gray-400 px-2 py-1">
                              +{category.tags.length - 2}
                            </span>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </div>

            {/* Business Tags (auto-populated but editable) */}
            {profileData.businessCategory && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="bg-white rounded-2xl p-6 border border-gray-200"
              >
                <div className="flex items-center mb-3">
                  <Tag className="w-5 h-5 text-indigo-600 mr-2" />
                  <label className="text-sm font-medium text-gray-700">
                    Business Tags
                  </label>
                </div>
                <p className="text-sm text-gray-500 mb-6">
                  These tags will help customers discover your vouchers. You can add or remove tags.
                </p>

                <div className="space-y-4">
                  <div className="flex flex-wrap gap-2 min-h-[60px] p-4 border-2 border-dashed border-gray-300 rounded-xl bg-white/50">
                    {(profileData.businessTags || []).length === 0 ? (
                      <div className="flex items-center justify-center w-full h-full text-gray-400 text-sm italic">
                        <Tag className="w-4 h-4 mr-2" />
                        No tags selected
                      </div>
                    ) : (
                      (profileData.businessTags || []).map((tag, index) => (
                        <motion.span
                          key={`${tag}-${index}`}
                          initial={{ opacity: 0, scale: 0.8, y: 10 }}
                          animate={{ opacity: 1, scale: 1, y: 0 }}
                          exit={{ opacity: 0, scale: 0.8, y: -10 }}
                          whileHover={{ scale: 1.05 }}
                          className="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 border border-indigo-200 shadow-sm"
                        >
                          {tag.replace('-', ' ')}
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="ml-2 text-indigo-500 hover:text-red-600 hover:bg-white rounded-full w-5 h-5 flex items-center justify-center transition-all duration-200"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </motion.span>
                      ))
                    )}
                  </div>

                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <input
                        type="text"
                        value={customTagInput}
                        onChange={(e) => setCustomTagInput(e.target.value)}
                        placeholder="Type a custom tag..."
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            addCustomTag();
                          }
                        }}
                      />
                      <div className="absolute right-3 top-3 text-xs text-gray-400">
                        Press Enter
                      </div>
                    </div>
                    <motion.button
                      type="button"
                      onClick={addCustomTag}
                      disabled={!customTagInput.trim()}
                      className="px-4 py-3 bg-indigo-600 text-white rounded-xl hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Plus className="w-4 h-4 mr-1" />
                      Add
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Primary Business Hours */}
            <div>
              <div className="flex items-center mb-3">
                <Clock className="w-5 h-5 text-indigo-600 mr-2" />
                <label className="text-sm font-medium text-gray-700">
                  Primary Business Hours
                </label>
              </div>
              <p className="text-sm text-gray-500 mb-6">
                Set your main business hours and timezone. Additional locations can inherit these or have their own.
              </p>

              <div className="bg-white border border-gray-200 rounded-2xl p-6 shadow-sm space-y-6">
                {/* Timezone and 24-hour controls */}
                <div className="flex flex-col sm:flex-row gap-4">
                  {/* Timezone Selection */}
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Timezone
                    </label>
                    <div className="relative">
                      <select
                        value={profileData.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone}
                        onChange={(e) => setProfileData(prev => ({ ...prev, timezone: e.target.value }))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white text-sm appearance-none cursor-pointer hover:border-purple-400 transition-all duration-200 shadow-sm hover:shadow-md font-medium"
                        style={{
                          backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                          backgroundPosition: 'right 0.75rem center',
                          backgroundRepeat: 'no-repeat',
                          backgroundSize: '1.25rem 1.25rem'
                        }}
                      >
                        <optgroup label="US Timezones" className="text-gray-900 font-semibold">
                          <option value="America/New_York" className="py-2">Eastern Time (EST/EDT)</option>
                          <option value="America/Chicago" className="py-2">Central Time (CST/CDT)</option>
                          <option value="America/Denver" className="py-2">Mountain Time (MST/MDT)</option>
                          <option value="America/Los_Angeles" className="py-2">Pacific Time (PST/PDT)</option>
                          <option value="America/Anchorage" className="py-2">Alaska Time (AKST/AKDT)</option>
                          <option value="Pacific/Honolulu" className="py-2">Hawaii Time (HST)</option>
                        </optgroup>
                        <optgroup label="International" className="text-gray-900 font-semibold">
                          <option value="UTC" className="py-2">UTC (Coordinated Universal)</option>
                          <option value="Europe/London" className="py-2">London (GMT/BST)</option>
                          <option value="Europe/Paris" className="py-2">Paris (CET/CEST)</option>
                          <option value="Asia/Tokyo" className="py-2">Tokyo (JST)</option>
                          <option value="Australia/Sydney" className="py-2">Sydney (AEST/AEDT)</option>
                          <option value="America/Toronto" className="py-2">Toronto (EST/EDT)</option>
                        </optgroup>
                      </select>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Current time: {new Date().toLocaleTimeString('en-US', { 
                        timeZone: profileData.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
                        hour12: true 
                      })}
                    </p>
                  </div>

                  {/* 24 Hours Toggle */}
                  <div className="flex items-center justify-end">
                    <motion.button
                      type="button"
                      onClick={() => setProfileData(prev => ({ 
                        ...prev, 
                        isOpen24Hours: !prev.isOpen24Hours 
                      }))}
                      className={`px-6 py-3 rounded-xl font-semibold text-sm transition-all duration-300 flex items-center space-x-2 shadow-lg ${
                        profileData.isOpen24Hours
                          ? 'bg-gradient-to-r from-purple-500 to-indigo-600 text-white hover:from-purple-600 hover:to-indigo-700 shadow-purple-200'
                          : 'bg-white text-gray-700 border-2 border-gray-300 hover:border-purple-400 hover:text-purple-700 hover:shadow-purple-100'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Clock className="w-4 h-4" />
                      <span>Open 24 Hours</span>
                      {profileData.isOpen24Hours && (
                        <motion.div
                          initial={{ scale: 0, rotate: -180 }}
                          animate={{ scale: 1, rotate: 0 }}
                          className="w-4 h-4 bg-white rounded-full flex items-center justify-center"
                        >
                          <CheckCircle2 className="w-3 h-3 text-purple-600" />
                        </motion.div>
                      )}
                    </motion.button>
                  </div>
                </div>

                {/* Collapsible Daily Hours Section */}
                <motion.div
                  initial={false}
                  animate={{ 
                    height: profileData.isOpen24Hours ? 0 : 'auto',
                    opacity: profileData.isOpen24Hours ? 0 : 1
                  }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                  className="overflow-hidden"
                >
                  {!profileData.isOpen24Hours && (
                    <div className="space-y-3">
                      {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => {
                        const dayHours = profileData.primaryHours?.[day] || { open: '09:00', close: '17:00', closed: day === 'sunday' };
                        const isOpen = !dayHours.closed;

                        // Convert 24-hour to 12-hour format for display
                        const formatTimeFor12Hour = (time24: string) => {
                          const [hours, minutes] = time24.split(':').map(Number);
                          const period = hours >= 12 ? 'PM' : 'AM';
                          const hours12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
                          return { hours12, minutes, period, time24 };
                        };

                        // Convert 12-hour format to 24-hour
                        const convertTo24Hour = (hours12: number, minutes: number, period: string) => {
                          let hours24 = hours12;
                          if (period === 'AM' && hours12 === 12) hours24 = 0;
                          if (period === 'PM' && hours12 !== 12) hours24 = hours12 + 12;
                          return `${hours24.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                        };

                        // Validate that opening time is before closing time
                        const validateTimeOrder = (openTime: string, closeTime: string) => {
                          const [openHours, openMinutes] = openTime.split(':').map(Number);
                          const [closeHours, closeMinutes] = closeTime.split(':').map(Number);
                          const openTotalMinutes = openHours * 60 + openMinutes;
                          const closeTotalMinutes = closeHours * 60 + closeMinutes;
                          return openTotalMinutes < closeTotalMinutes;
                        };

                        const openTime = formatTimeFor12Hour(dayHours.open);
                        const closeTime = formatTimeFor12Hour(dayHours.close);
                        const timeOrderValid = !isOpen || validateTimeOrder(dayHours.open, dayHours.close);

                        // Generate hour and minute options
                        const hourOptions = Array.from({ length: 12 }, (_, i) => i + 1);
                        const minuteOptions = [0, 30];

                        return (
                          <div 
                            key={day} 
                            className="flex flex-col py-4 px-4 bg-white rounded-xl border border-gray-100 hover:shadow-sm transition-all duration-200 space-y-3"
                          >
                            {/* Day and Toggle */}
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-4">
                                <div className="w-20">
                                  <span className="text-sm text-gray-700 capitalize font-semibold">
                                    {day.charAt(0).toUpperCase() + day.slice(1)}
                                  </span>
                                </div>

                                <div className="flex items-center">
                                  <label className="relative inline-flex items-center cursor-pointer">
                                    <input
                                      type="checkbox"
                                      checked={!dayHours.closed}
                                      onChange={(e) => {
                                        setProfileData(prev => ({
                                          ...prev,
                                          primaryHours: {
                                            ...prev.primaryHours,
                                            [day]: { 
                                              ...dayHours, 
                                              closed: !e.target.checked 
                                            }
                                          }
                                        }));
                                      }}
                                      className="sr-only"
                                    />
                                    <div className={`w-12 h-6 rounded-full transition-all duration-300 ${
                                      !dayHours.closed
                                        ? 'bg-gradient-to-r from-purple-500 to-indigo-600 shadow-lg shadow-purple-200' 
                                        : 'bg-gray-300'
                                    }`}>
                                      <motion.div 
                                        className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-all duration-300 ${
                                          !dayHours.closed ? 'translate-x-6' : 'translate-x-0.5'
                                        } mt-0.5`}
                                        whileHover={{ scale: 1.1 }}
                                        animate={{ x: !dayHours.closed ? 24 : 2 }}
                                      />
                                    </div>
                                  </label>
                                </div>
                              </div>

                              {/* Status indicator */}
                              <div className="flex items-center">
                                {!dayHours.closed ? (
                                  <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">
                                    Open
                                  </span>
                                ) : (
                                  <div className="flex items-center text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
                                    <motion.div
                                      className="flex items-center space-x-1"
                                      animate={{ opacity: [0.5, 1, 0.5] }}
                                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                                    >
                                      <div className="flex space-x-0.5">
                                        <motion.span 
                                          className="text-sm text-gray-400"
                                          animate={{ y: [0, -1, 0] }}
                                          transition={{ duration: 1.5, repeat: Infinity, delay: 0 }}
                                        >
                                          Z
                                        </motion.span>
                                        <motion.span 
                                          className="text-xs text-gray-400"
                                          animate={{ y: [0, -1, 0] }}
                                          transition={{ duration: 1.5, repeat: Infinity, delay: 0.2 }}
                                        >
                                          z
                                        </motion.span>
                                        <motion.span 
                                          className="text-xs text-gray-400"
                                          animate={{ y: [0, -1, 0] }}
                                          transition={{ duration: 1.5, repeat: Infinity, delay: 0.4 }}
                                        >
                                          z
                                        </motion.span>
                                      </div>
                                      <span className="text-xs font-medium text-gray-500 ml-1">Closed</span>
                                    </motion.div>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Time Selection - only show when open */}
                            {!dayHours.closed && (
                              <div className="flex items-center space-x-4 pl-24">
                                <span className="text-sm text-gray-600 font-medium">From</span>

                                {/* Opening Time */}
                                <div className="flex items-center space-x-1">
                                  {/* Hours Dropdown */}
                                  <select
                                    value={openTime.hours12}
                                    onChange={(e) => {
                                      const newHours = parseInt(e.target.value);
                                      const newTime24 = convertTo24Hour(newHours, openTime.minutes, openTime.period);
                                      setProfileData(prev => ({
                                        ...prev,
                                        primaryHours: {
                                          ...prev.primaryHours,
                                          [day]: { ...dayHours, open: newTime24 }
                                        }
                                      }));
                                    }}
                                    className={`w-14 text-sm border rounded-lg px-2 py-1.5 bg-white focus:ring-2 transition-all duration-200 font-mono ${
                                      timeOrderValid 
                                        ? 'border-gray-300 focus:ring-purple-500 focus:border-purple-500' 
                                        : 'border-red-400 focus:ring-red-500 focus:border-red-500 bg-red-50'
                                    }`}
                                  >
                                    {hourOptions.map(hour => (
                                      <option key={hour} value={hour}>
                                        {hour.toString().padStart(2, '0')}
                                      </option>
                                    ))}
                                  </select>

                                  <span className="text-gray-400">:</span>

                                  {/* Minutes Dropdown */}
                                  <select
                                    value={openTime.minutes}
                                    onChange={(e) => {
                                      const newMinutes = parseInt(e.target.value);
                                      const newTime24 = convertTo24Hour(openTime.hours12, newMinutes, openTime.period);
                                      setProfileData(prev => ({
                                        ...prev,
                                        primaryHours: {
                                          ...prev.primaryHours,
                                          [day]: { ...dayHours, open: newTime24 }
                                        }
                                      }));
                                    }}
                                    className={`w-14 text-sm border rounded-lg px-2 py-1.5 bg-white focus:ring-2 transition-all duration-200 font-mono ${
                                      timeOrderValid 
                                        ? 'border-gray-300 focus:ring-purple-500 focus:border-purple-500' 
                                        : 'border-red-400 focus:ring-red-500 focus:border-red-500 bg-red-50'
                                    }`}
                                  >
                                    {minuteOptions.map(minute => (
                                      <option key={minute} value={minute}>
                                        {minute.toString().padStart(2, '0')}
                                      </option>
                                    ))}
                                  </select>

                                  {/* AM/PM Dropdown */}
                                  <select
                                    value={openTime.period}
                                    onChange={(e) => {
                                      const newPeriod = e.target.value as 'AM' | 'PM';
                                      const newTime24 = convertTo24Hour(openTime.hours12, openTime.minutes, newPeriod);
                                      setProfileData(prev => ({
                                        ...prev,
                                        primaryHours: {
                                          ...prev.primaryHours,
                                          [day]: { ...dayHours, open: newTime24 }
                                        }
                                      }));
                                    }}
                                    className="w-16 text-sm border border-gray-300 rounded-lg px-2 py-1.5 bg-gradient-to-r from-purple-50 to-indigo-50 hover:from-purple-100 hover:to-indigo-100 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 font-semibold text-purple-700 cursor-pointer"
                                  >
                                    <option value="AM">AM</option>
                                    <option value="PM">PM</option>
                                  </select>
                                </div>

                                <span className="text-sm text-gray-600 font-medium">To</span>

                                {/* Closing Time */}
                                <div className="flex items-center space-x-1">
                                  {/* Hours Dropdown */}
                                  <select
                                    value={closeTime.hours12}
                                    onChange={(e) => {
                                      const newHours = parseInt(e.target.value);
                                      const newTime24 = convertTo24Hour(newHours, closeTime.minutes, closeTime.period);
                                      setProfileData(prev => ({
                                        ...prev,
                                        primaryHours: {
                                          ...prev.primaryHours,
                                          [day]: { ...dayHours, close: newTime24 }
                                        }
                                      }));
                                    }}
                                    className={`w-14 text-sm border rounded-lg px-2 py-1.5 bg-white focus:ring-2 transition-all duration-200 font-mono ${
                                      timeOrderValid 
                                        ? 'border-gray-300 focus:ring-purple-500 focus:border-purple-500' 
                                        : 'border-red-400 focus:ring-red-500 focus:border-red-500 bg-red-50'
                                    }`}
                                  >
                                    {hourOptions.map(hour => (
                                      <option key={hour} value={hour}>
                                        {hour.toString().padStart(2, '0')}
                                      </option>
                                    ))}
                                  </select>

                                  <span className="text-gray-400">:</span>

                                  {/* Minutes Dropdown */}
                                  <select
                                    value={closeTime.minutes}
                                    onChange={(e) => {
                                      const newMinutes = parseInt(e.target.value);
                                      const newTime24 = convertTo24Hour(closeTime.hours12, newMinutes, closeTime.period);
                                      setProfileData(prev => ({
                                        ...prev,
                                        primaryHours: {
                                          ...prev.primaryHours,
                                          [day]: { ...dayHours, close: newTime24 }
                                        }
                                      }));
                                    }}
                                    className={`w-14 text-sm border rounded-lg px-2 py-1.5 bg-white focus:ring-2 transition-all duration-200 font-mono ${
                                      timeOrderValid 
                                        ? 'border-gray-300 focus:ring-purple-500 focus:border-purple-500' 
                                        : 'border-red-400 focus:ring-red-500 focus:border-red-500 bg-red-50'
                                    }`}
                                  >
                                    {minuteOptions.map(minute => (
                                      <option key={minute} value={minute}>
                                        {minute.toString().padStart(2, '0')}
                                      </option>
                                    ))}
                                  </select>

                                  {/* AM/PM Dropdown */}
                                  <select
                                    value={closeTime.period}
                                    onChange={(e) => {
                                      const newPeriod = e.target.value as 'AM' | 'PM';
                                      const newTime24 = convertTo24Hour(closeTime.hours12, closeTime.minutes, newPeriod);
                                      setProfileData(prev => ({
                                        ...prev,
                                        primaryHours: {
                                          ...prev.primaryHours,
                                          [day]: { ...dayHours, close: newTime24 }
                                        }
                                      }));
                                    }}
                                    className="w-16 text-sm border border-gray-300 rounded-lg px-2 py-1.5 bg-gradient-to-r from-purple-50 to-indigo-50 hover:from-purple-100 hover:to-indigo-100 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 font-semibold text-purple-700 cursor-pointer"
                                  >
                                    <option value="AM">AM</option>
                                    <option value="PM">PM</option>
                                  </select>
                                </div>
                              </div>
                            )}

                            {/* Time validation error */}
                            {!dayHours.closed && !timeOrderValid && (
                              <motion.div 
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="flex items-center text-red-600 bg-red-50 px-3 py-2 rounded-lg ml-24 border border-red-200"
                              >
                                <AlertCircle className="w-4 h-4 mr-2" />
                                <span className="text-sm font-medium">
                                  Opening time must be before closing time
                                </span>
                              </motion.div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </motion.div>

                {/* 24-Hour Message */}
                {profileData.isOpen24Hours && (
                  <motion.div 
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="text-center py-8"
                  >
                    <div className="inline-flex items-center space-x-3 px-6 py-4 bg-gradient-to-r from-purple-100 to-indigo-100 rounded-2xl border border-purple-200">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                        className="w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg"
                      >
                        <Clock className="w-4 h-4 text-white" />
                      </motion.div>
                      <div className="text-left">
                        <p className="text-sm font-semibold text-purple-800">Open 24 Hours, 7 Days a Week</p>
                        <p className="text-xs text-purple-600">Always available for your customers</p>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Quick presets - only show when not 24 hours */}```
              {!profileData.isOpen24Hours && (
                  <div className="pt-4 border-t border-gray-200">
                    <p className="text-sm text-gray-600 mb-4 font-medium flex items-center">
                      <Calendar className="w-4 h-4 mr-2" />
                      Quick presets
                    </p>
                    <div className="grid grid-cols-1 sm:grid-cols-4 gap-3">
                      <motion.button
                        type="button"
                        onClick={() => {
                          const businessHours = {
                            monday: { open: '09:00', close: '17:00', closed: false },
                            tuesday: { open: '09:00', close: '17:00', closed: false },
                            wednesday: { open: '09:00', close: '17:00', closed: false },
                            thursday: { open: '09:00', close: '17:00', closed: false },
                            friday: { open: '09:00', close: '17:00', closed: false },
                            saturday: { open: '09:00', close: '17:00', closed: true },
                            sunday: { open: '09:00', close: '17:00', closed: true }
                          };
                          setProfileData(prev => ({ ...prev, primaryHours: businessHours }));
                        }}
                        className="flex items-center justify-center px-3 py-3 bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 text-purple-700 rounded-xl hover:from-purple-100 hover:to-indigo-100 hover:border-purple-300 transition-all duration-200 font-semibold text-sm group shadow-sm hover:shadow-md"
                        whileHover={{ scale: 1.02, y: -1 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Briefcase className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                        Business
                      </motion.button>
                      <motion.button
                        type="button"
                        onClick={() => {
                          const retailHours = {
                            monday: { open: '10:00', close: '20:00', closed: false },
                            tuesday: { open: '10:00', close: '20:00', closed: false },
                            wednesday: { open: '10:00', close: '20:00', closed: false },
                            thursday: { open: '10:00', close: '20:00', closed: false },
                            friday: { open: '10:00', close: '20:00', closed: false },
                            saturday: { open: '10:00', close: '20:00', closed: false },
                            sunday: { open: '12:00', close: '18:00', closed: false }
                          };
                          setProfileData(prev => ({ ...prev, primaryHours: retailHours }));
                        }}
                        className="flex items-center justify-center px-3 py-3 bg-gradient-to-r from-pink-50 to-purple-50 border border-pink-200 text-pink-700 rounded-xl hover:from-pink-100 hover:to-purple-100 hover:border-pink-300 transition-all duration-200 font-semibold text-sm group shadow-sm hover:shadow-md"
                        whileHover={{ scale: 1.02, y: -1 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <ShoppingBag className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                        Retail
                      </motion.button>
                      <motion.button
                        type="button"
                        onClick={() => {
                          const restaurantHours = {
                            monday: { open: '11:00', close: '22:00', closed: false },
                            tuesday: { open: '11:00', close: '22:00', closed: false },
                            wednesday: { open: '11:00', close: '22:00', closed: false },
                            thursday: { open: '11:00', close: '22:00', closed: false },
                            friday: { open: '11:00', close: '23:00', closed: false },
                            saturday: { open: '11:00', close: '23:00', closed: false },
                            sunday: { open: '11:00', close: '21:00', closed: false }
                          };
                          setProfileData(prev => ({ ...prev, primaryHours: restaurantHours }));
                        }}
                        className="flex items-center justify-center px-3 py-3 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 text-green-700 rounded-xl hover:from-green-100 hover:to-emerald-100 hover:border-green-300 transition-all duration-200 font-semibold text-sm group shadow-sm hover:shadow-md"
                        whileHover={{ scale: 1.02, y: -1 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Restaurant className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                        Restaurant
                      </motion.button>
                      <motion.button
                        type="button"
                        onClick={() => {
                          setProfileData(prev => ({ ...prev, isOpen24Hours: true }));
                        }}
                        className="flex items-center justify-center px-3 py-3 bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 text-indigo-700 rounded-xl hover:from-indigo-100 hover:to-blue-100 hover:border-indigo-300 transition-all duration-200 font-semibold text-sm group shadow-sm hover:shadow-md"
                        whileHover={{ scale: 1.02, y: -1 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Clock className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                        24/7
                      </motion.button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Business Logo Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Logo (Optional)
              </label>

              <div 
                className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all ${
                  dragOver ? 'border-indigo-400 bg-indigo-50' : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                {profileData.logoUrl ? (
                  <div className="relative">
                    <img 
                      src={profileData.logoUrl} 
                      alt="Business Logo" 
                      className="w-32 h-32 object-cover rounded-lg mx-auto mb-4 border-4 border-white shadow-lg"
                    />
                    <button
                      type="button"
                      onClick={removeLogo}
                      className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-600 transition-colors"
                    >
                      <X className="w-4 h-4" />
                    </button>
                    <p className="text-sm text-gray-600">Click X to remove or drag a new image to replace</p>
                  </div>
                ) : (
                  <div>
                    <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-lg font-medium text-gray-700 mb-2">Upload your business logo</p>
                    <p className="text-sm text-gray-500 mb-4">Drag and drop or click to browse</p>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleLogoUpload(file);
                      }}
                      className="hidden"
                      id="logo-upload"
                    />
                    <label
                      htmlFor="logo-upload"
                      className="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 cursor-pointer transition-colors"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Choose File
                    </label>
                    <p className="text-xs text-gray-400 mt-2">JPG, PNG, GIF, WebP, SVG, BMP, TIFF up to 5MB</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 2:
        // Only show locations tab for physical businesses with potential multiple locations
        if (profileData.businessType === 'service') {
          return (
            <div className="text-center py-12">
              <div className="flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mx-auto mb-4">
                <Navigation className="w-8 h-8 text-indigo-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Service-Based Business</h3>
              <p className="text-gray-600 mb-6">
                Since you provide services, additional locations are not needed. You can proceed to review your profile.
              </p>
              <motion.button
                onClick={() => setCurrentStep(3)}
                className="px-6 py-3 bg-indigo-600 text-white rounded-xl hover:bg-indigo-700 transition-all duration-200 font-medium"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Continue to Review
              </motion.button>
            </div>
          );
        }

        return (
          <div className="space-y-8">
            <div className="flex items-center mb-6">
              <div className="flex items-center justify-center w-12 h-12 bg-indigo-100 rounded-xl mr-4">
                <MapPin className="w-6 h-6 text-indigo-600" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">Locations & Hours</h3>
                <p className="text-gray-600 text-sm mt-1">Manage your business locations and operating hours</p>
              </div>
            </div>

            {/* Primary Business Location Card */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gradient-to-r from-indigo-50 to-purple-50 border-2 border-indigo-200 rounded-2xl p-6 shadow-sm"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mr-3">
                    <Building className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900">Primary Business Location</h4>
                    <p className="text-sm text-gray-600">Main location from your business profile</p>
                  </div>
                </div>
                <span className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                  PRIMARY
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 bg-white/60 rounded-xl p-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Business Name</label>
                  <p className="text-gray-900 font-medium">{profileData.businessName}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                  <p className="text-gray-900">{profileData.phoneNumber}</p>
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                  <p className="text-gray-900">{profileData.primaryAddress}</p>
                </div>
                {profileData.website && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Website</label>
                    <p className="text-gray-900">{profileData.website}</p>
                  </div>
                )}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <p className="text-gray-900">
                    {profileData.businessCategory ? 
                      businessCategories[profileData.businessCategory as keyof typeof businessCategories]?.label : 
                      'Not set'
                    }
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Location Usage Meter */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <Building className="w-5 h-5 text-blue-600 mr-2" />
                  <h4 className="text-lg font-semibold text-gray-900">Location Usage</h4>
                </div>
                <div className="text-sm text-blue-600 font-medium">
                  {profileData.locations.length + 1} / {profileData.locationLimit} locations
                </div>
              </div>

              <div className="space-y-3">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${((profileData.locations.length + 1) / (profileData.locationLimit || 2)) * 100}%` }}
                  />
                </div>

                {profileData.subscriptionPlan === 'free' && (
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-600">
                      Free plan includes {profileData.maxFreeLocations} locations
                    </p>
                    {profileData.locations.length >= (profileData.locationLimit || 2) - 1 && (
                      <motion.button
                        className="px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg text-sm font-medium hover:from-purple-700 hover:to-indigo-700 transition-all"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setShowUpgradeModal(true)}
                      >
                        Upgrade Plan
                      </motion.button>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <motion.button
                type="button"
                onClick={() => setShowNewLocationForm(true)}
                className="flex-1 px-6 py-4 bg-indigo-600 text-white rounded-xl hover:bg-indigo-700 transition-all duration-200 font-medium flex items-center justify-center"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Plus className="w-5 h-5 mr-2" />
                Add Location
              </motion.button>

              <motion.button
                onClick={() => setCurrentStep(3)}
                className="flex-1 px-6 py-4 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-all duration-200 font-medium flex items-center justify-center"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Skip Additional Locations
              </motion.button>
            </div>

            <p className="text-center text-gray-600 text-sm">
              Only have one location? You can skip this step and proceed to review.
            </p>

            {/* New Location Form - Conditional rendering */}
            {showNewLocationForm && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="bg-white border-2 border-gray-200 rounded-2xl p-8 shadow-lg"
              >
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h4 className="text-xl font-bold text-gray-900">
                      {editingLocationId ? 'Edit Location' : 'Add New Location'}
                    </h4>
                    <p className="text-gray-600 text-sm mt-1">
                      {editingLocationId 
                        ? 'Update the details for this location' 
                        : 'Add another branch, store, or office location'
                      }
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      setShowNewLocationForm(false);
                      setEditingLocationId(null);
                      // Reset form when closing
                      setNewLocation({
                        name: '',
                        address: '',
                        addressLine2: '',
                        city: '',
                        state: '',
                        zipCode: '',
                        country: 'United States',
                        phone: '',
                        website: '',
                        usePrimaryWebsite: true,
                        usePrimaryHours: true,
                        businessType: 'physical',
                        businessCategory: '',
                        businessTags: [],
                        usePrimaryCategory: true,
                        usePrimaryTags: true,
                        isPrimary: false,
                        canBeDeleted: true,
                        isOpen24Hours: false,
                        operatingHours: {
                          monday: { open: '09:00', close: '17:00', closed: false },
                          tuesday: { open: '09:00', close: '17:00', closed: false },
                          wednesday: { open: '09:00', close: '17:00', closed: false },
                          thursday: { open: '09:00', close: '17:00', closed: false },
                          friday: { open: '09:00', close: '17:00', closed: false },
                          saturday: { open: '10:00', close: '16:00', closed: false },
                          sunday: { open: '10:00', close: '16:00', closed: true }
                        }
                      });
                      setNewLocationValidation({
                        isValidating: false,
                        isValid: null,
                        suggestion: null
                      });
                    }}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Location Name and Phone */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Location Name *
                      </label>
                      <input
                        type="text"
                        value={newLocation.name}
                        onChange={(e) => setNewLocation(prev => ({ ...prev, name: e.target.value }))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                        placeholder="Downtown Branch, Mall Location, etc."
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                        <input
                          type="tel"
                          value={newLocation.phone}
                          onChange={(e) => setNewLocation(prev => ({ ...prev, phone: formatPhoneNumber(e.target.value) }))}
                          className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                          placeholder="************"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Full Address with autocomplete */}
                  <div className="space-y-4">
                    <label className="block text-sm font-medium text-gray-700">
                      Full Address *
                    </label>
                    
                    <div className="space-y-4">
                      {/* Main Address Input with Autocomplete */}
                      <div className="relative">
                        <input
                          type="text"
                          value={`${newLocation.address || ''}${newLocation.city ? `, ${newLocation.city}` : ''}${newLocation.state ? `, ${newLocation.state}` : ''}${newLocation.zipCode ? ` ${newLocation.zipCode}` : ''}`}
                          onChange={(e) => {
                            const fullAddress = e.target.value;
                            // For simplicity, store the full address in the address field
                            // In a real implementation, you'd parse this properly
                            setNewLocation(prev => ({ ...prev, address: fullAddress }));
                            
                            // Trigger autocomplete search
                            if (fullAddress.length > 3) {
                              searchAddressesForNewLocation(fullAddress);
                            }
                          }}
                          onFocus={() => {
                            if (newLocationSuggestions.length > 0) {
                              setShowNewLocationSuggestions(true);
                            }
                          }}
                          onBlur={() => {
                            setTimeout(() => setShowNewLocationSuggestions(false), 150);
                          }}
                          className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-indigo-500 transition-all ${
                            newLocationValidation.isValid === true ? 'border-green-300 bg-green-50' :
                            newLocationValidation.isValid === false ? 'border-red-300 bg-red-50' :
                            'border-gray-300'
                          } focus:border-indigo-500`}
                          placeholder="Start typing your address..."
                        />

                        {newLocationValidation.isValidating && (
                          <div className="absolute right-3 top-3">
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-600"></div>
                          </div>
                        )}

                        {newLocationValidation.isValid === true && (
                          <div className="absolute right-3 top-3">
                            <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs">✓</span>
                            </div>
                          </div>
                        )}

                        {newLocationValidation.isValid === false && (
                          <div className="absolute right-3 top-3">
                            <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs">!</span>
                            </div>
                          </div>
                        )}

                        {/* Autocomplete suggestions for new location */}
                        {showNewLocationSuggestions && newLocationSuggestions.length > 0 && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg max-h-60 overflow-y-auto"
                          >
                            {newLocationSuggestions.map((suggestion, index) => (
                              <button
                                key={index}
                                type="button"
                                className="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none first:rounded-t-xl last:rounded-b-xl transition-colors"
                                onClick={() => {
                                  // Parse the selected address
                                  const addressParts = suggestion.display_name.split(', ');
                                  setNewLocation(prev => ({
                                    ...prev,
                                    address: addressParts[0] || '',
                                    city: addressParts[1] || '',
                                    state: addressParts[2] || '',
                                    zipCode: addressParts[3] || '',
                                    country: 'United States'
                                  }));
                                  setNewLocationValidation({
                                    isValidating: false,
                                    isValid: true,
                                    suggestion: suggestion.display_name,
                                    coordinates: { lat: suggestion.lat, lng: suggestion.lon }
                                  });
                                  setShowNewLocationSuggestions(false);
                                }}
                              >
                                <div className="flex items-start">
                                  <MapPin className="w-4 h-4 text-gray-400 mt-0.5 mr-3 flex-shrink-0" />
                                  <div className="text-sm text-gray-900">{suggestion.display_name}</div>
                                </div>
                              </button>
                            ))}
                          </motion.div>
                        )}
                      </div>

                      {/* Address 2 / Unit (Optional) */}
                      <div>
                        <input
                          type="text"
                          value={newLocation.addressLine2 || ''}
                          onChange={(e) => setNewLocation(prev => ({ ...prev, addressLine2: e.target.value }))}
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                          placeholder="Address 2, Unit, Suite, etc. (Optional)"
                        />
                      </div>
                    </div>

                    {/* Address validation indicator */}
                    <div className="relative">
                      {newLocationValidation.isValidating && (
                        <div className="flex items-center text-sm text-blue-600">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                          Validating address...
                        </div>
                      )}

                      {newLocationValidation.isValid === true && (
                        <div className="flex items-center text-sm text-green-600">
                          <CheckCircle2 className="w-4 h-4 mr-2" />
                          Address validated: {newLocationValidation.suggestion}
                        </div>
                      )}

                      {newLocationValidation.isValid === false && (
                        <div className="flex items-center text-sm text-red-600">
                          <AlertCircle className="w-4 h-4 mr-2" />
                          {newLocationValidation.suggestion}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Business Type Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Business Type *
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <motion.button
                        type="button"
                        onClick={() => setNewLocation(prev => ({ ...prev, businessType: 'physical' }))}
                        className={`p-4 border-2 rounded-xl transition-all duration-200 ${
                          newLocation.businessType === 'physical'
                            ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                            : 'border-gray-300 hover:border-indigo-300 text-gray-700'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center space-x-3">
                          <Building className="w-6 h-6" />
                          <div className="text-left">
                            <h4 className="font-semibold">Physical Location</h4>
                            <p className="text-sm opacity-75">Store, office, restaurant, etc.</p>
                          </div>
                        </div>
                      </motion.button>

                      <motion.button
                        type="button"
                        onClick={() => setNewLocation(prev => ({ ...prev, businessType: 'service' }))}
                        className={`p-4 border-2 rounded-xl transition-all duration-200 ${
                          newLocation.businessType === 'service'
                            ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                            : 'border-gray-300 hover:border-indigo-300 text-gray-700'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center space-x-3">
                          <Navigation className="w-6 h-6" />
                          <div className="text-left">
                            <h4 className="font-semibold">Service-Based</h4>
                            <p className="text-sm opacity-75">Mobile, online, or home services</p>
                          </div>
                        </div>
                      </motion.button>
                    </div>
                  </div>

                  {/* Business Category Configuration */}
                  <div className="bg-gray-50 border border-gray-200 rounded-xl p-6">
                    <div className="flex items-center mb-4">
                      <Tag className="w-5 h-5 text-indigo-600 mr-2" />
                      <h4 className="text-lg font-semibold text-gray-900">Business Category</h4>
                    </div>
                    
                    <div className="space-y-6">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="usePrimaryCategory"
                          checked={newLocation.usePrimaryCategory}
                          onChange={(e) => setNewLocation(prev => ({ 
                            ...prev, 
                            usePrimaryCategory: e.target.checked,
                            businessCategory: e.target.checked ? profileData.businessCategory : prev.businessCategory,
                            businessTags: e.target.checked ? profileData.businessTags : prev.businessTags
                          }))}
                          className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                        />
                        <label htmlFor="usePrimaryCategory" className="ml-2 text-sm text-gray-700">
                          Use same category and tags as primary business ({profileData.businessCategory ? businessCategories[profileData.businessCategory as keyof typeof businessCategories]?.label : 'Not set'})
                        </label>
                      </div>

                      {!newLocation.usePrimaryCategory && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="space-y-6"
                        >
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-3">
                              Business Category *
                            </label>
                            <p className="text-sm text-gray-500 mb-6">
                              This will automatically categorize all vouchers from this location
                            </p>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                              {Object.entries(businessCategories).map(([key, category]) => {
                                const IconComponent = getCategoryIcon(category.icon);
                                const isSelected = newLocation.businessCategory === key;

                                return (
                                  <motion.div
                                    key={key}
                                    className={`relative border-2 rounded-xl p-6 cursor-pointer transition-all group ${
                                      isSelected
                                        ? 'border-indigo-500 bg-indigo-50 shadow-lg ring-1 ring-indigo-200'
                                        : 'border-gray-200 hover:border-indigo-300 hover:shadow-md bg-white'
                                    }`}
                                    whileHover={{ scale: 1.02, y: -2 }}
                                    whileTap={{ scale: 0.98 }}
                                    onClick={() => {
                                      setNewLocation(prev => ({
                                        ...prev,
                                        businessCategory: key,
                                        businessTags: category.tags
                                      }));
                                    }}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.1 * Object.keys(businessCategories).indexOf(key) }}
                                  >
                                    {isSelected && (
                                      <motion.div
                                        className="absolute top-3 right-3 w-6 h-6 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-md z-10"
                                        initial={{ scale: 0, rotate: -180 }}
                                        animate={{ scale: 1, rotate: 0 }}
                                        transition={{ type: "spring", stiffness: 500, delay: 0.2 }}
                                      >
                                        <CheckCircle2 className="w-3.5 h-3.5 text-white" />
                                      </motion.div>
                                    )}

                                    <div className="relative text-center">
                                      <motion.div 
                                        className={`w-12 h-12 mx-auto mb-4 rounded-xl flex items-center justify-center transition-all duration-300 ${
                                          isSelected 
                                            ? 'bg-gradient-to-br from-indigo-500 to-purple-600 text-white shadow-lg' 
                                            : 'bg-gray-100 text-gray-600 group-hover:bg-indigo-100 group-hover:text-indigo-600'
                                        }`}
                                        animate={isSelected ? { rotate: [0, 5, -5, 0] } : {}}
                                        transition={{ duration: 0.5 }}
                                      >
                                        <IconComponent className="w-6 h-6" />
                                      </motion.div>
                                      <h3 className={`text-sm font-semibold mb-2 transition-colors ${
                                        isSelected ? 'text-indigo-700' : 'text-gray-900'
                                      }`}>
                                        {category.label}
                                      </h3>
                                      <div className="flex items-center justify-center text-xs text-gray-500 bg-gray-50 rounded-full px-3 py-1 mb-3">
                                        <Tag className="w-3 h-3 mr-1" />
                                        {category.tags.length} tags
                                      </div>

                                      {/* Preview tags */}
                                      <div className="flex flex-wrap gap-1 justify-center">
                                        {category.tags.slice(0, 2).map((tag, index) => (
                                          <span 
                                            key={index}
                                            className="text-xs bg-white text-gray-600 px-2 py-1 rounded-md border border-gray-200"
                                          >
                                            {tag.replace('-', ' ')}
                                          </span>
                                        ))}
                                        {category.tags.length > 2 && (
                                          <span className="text-xs text-gray-400 px-2 py-1">
                                            +{category.tags.length - 2}
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  </motion.div>
                                );
                              })}
                            </div>
                          </div>

                          {/* Business Tags (auto-populated but editable) */}
                          {newLocation.businessCategory && (
                            <motion.div
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.3 }}
                              className="bg-white rounded-2xl p-6 border border-gray-200"
                            >
                              <div className="flex items-center mb-3">
                                <Tag className="w-5 h-5 text-indigo-600 mr-2" />
                                <label className="text-sm font-medium text-gray-700">
                                  Business Tags
                                </label>
                              </div>
                              <p className="text-sm text-gray-500 mb-6">
                                These tags will help customers discover vouchers from this location. You can add or remove tags.
                              </p>

                              <div className="space-y-4">
                                <div className="flex flex-wrap gap-2 min-h-[60px] p-4 border-2 border-dashed border-gray-300 rounded-xl bg-white/50">
                                  {(newLocation.businessTags || []).length === 0 ? (
                                    <div className="flex items-center justify-center w-full h-full text-gray-400 text-sm italic">
                                      <Tag className="w-4 h-4 mr-2" />
                                      No tags selected
                                    </div>
                                  ) : (
                                    (newLocation.businessTags || []).map((tag, index) => (
                                      <motion.span
                                        key={`${tag}-${index}`}
                                        initial={{ opacity: 0, scale: 0.8, y: 10 }}
                                        animate={{ opacity: 1, scale: 1, y: 0 }}
                                        exit={{ opacity: 0, scale: 0.8, y: -10 }}
                                        whileHover={{ scale: 1.05 }}
                                        className="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 border border-indigo-200 shadow-sm"
                                      >
                                        {tag.replace('-', ' ')}
                                        <button
                                          type="button"
                                          onClick={() => {
                                            setNewLocation(prev => ({
                                              ...prev,
                                              businessTags: (prev.businessTags || []).filter(t => t !== tag)
                                            }));
                                          }}
                                          className="ml-2 text-indigo-500 hover:text-red-600 hover:bg-white rounded-full w-5 h-5 flex items-center justify-center transition-all duration-200"
                                        >
                                          <X className="w-3 h-3" />
                                        </button>
                                      </motion.span>
                                    ))
                                  )}
                                </div>

                                <div className="flex gap-2">
                                  <div className="relative flex-1">
                                    <input
                                      type="text"
                                      value={customTagInput}
                                      onChange={(e) => setCustomTagInput(e.target.value)}
                                      placeholder="Type a custom tag..."
                                      className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                                      onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                          e.preventDefault();
                                          const tag = customTagInput.trim().toLowerCase().replace(/\s+/g, '-');
                                          if (tag && !(newLocation.businessTags || []).includes(tag)) {
                                            setNewLocation(prev => ({
                                              ...prev,
                                              businessTags: [...(prev.businessTags || []), tag]
                                            }));
                                            setCustomTagInput('');
                                          }
                                        }
                                      }}
                                    />
                                    <div className="absolute right-3 top-3 text-xs text-gray-400">
                                      Press Enter
                                    </div>
                                  </div>
                                  <motion.button
                                    type="button"
                                    onClick={() => {
                                      const tag = customTagInput.trim().toLowerCase().replace(/\s+/g, '-');
                                      if (tag && !(newLocation.businessTags || []).includes(tag)) {
                                        setNewLocation(prev => ({
                                          ...prev,
                                          businessTags: [...(prev.businessTags || []), tag]
                                        }));
                                        setCustomTagInput('');
                                      }
                                    }}
                                    disabled={!customTagInput.trim()}
                                    className="px-4 py-3 bg-indigo-600 text-white rounded-xl hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center"
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                  >
                                    <Plus className="w-4 h-4 mr-1" />
                                    Add
                                  </motion.button>
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </motion.div>
                      )}
                    </div>
                  </div>

                  {/* Operating Hours Configuration */}
                  <div className="bg-gray-50 border border-gray-200 rounded-xl p-6">
                    <div className="flex items-center mb-4">
                      <Clock className="w-5 h-5 text-indigo-600 mr-2" />
                      <h4 className="text-lg font-semibold text-gray-900">Operating Hours</h4>
                    </div>
                    
                    <div className="space-y-6">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="usePrimaryHours"
                          checked={newLocation.usePrimaryHours}
                          onChange={(e) => setNewLocation(prev => ({ 
                            ...prev, 
                            usePrimaryHours: e.target.checked,
                            operatingHours: e.target.checked 
                              ? (profileData.primaryHours || prev.operatingHours!)
                              : prev.operatingHours!,
                            isOpen24Hours: e.target.checked ? profileData.isOpen24Hours : false
                          }))}
                          className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                        />
                        <label htmlFor="usePrimaryHours" className="ml-2 text-sm text-gray-700">
                          Use same hours as primary business
                        </label>
                      </div>

                      {!newLocation.usePrimaryHours && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="bg-white border border-gray-200 rounded-2xl p-6 shadow-sm space-y-6"
                        >
                          {/* Timezone and 24-hour controls */}
                          <div className="flex flex-col sm:flex-row gap-4">
                            {/* Timezone Selection */}
                            <div className="flex-1">
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Timezone
                              </label>
                              <div className="relative">
                                <select
                                  value={profileData.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone}
                                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white text-sm appearance-none cursor-pointer hover:border-purple-400 transition-all duration-200 shadow-sm hover:shadow-md font-medium"
                                  style={{
                                    backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                                    backgroundPosition: 'right 0.75rem center',
                                    backgroundRepeat: 'no-repeat',
                                    backgroundSize: '1.25rem 1.25rem'
                                  }}
                                >
                                  <optgroup label="US Timezones" className="text-gray-900 font-semibold">
                                    <option value="America/New_York" className="py-2">Eastern Time (EST/EDT)</option>
                                    <option value="America/Chicago" className="py-2">Central Time (CST/CDT)</option>
                                    <option value="America/Denver" className="py-2">Mountain Time (MST/MDT)</option>
                                    <option value="America/Los_Angeles" className="py-2">Pacific Time (PST/PDT)</option>
                                    <option value="America/Anchorage" className="py-2">Alaska Time (AKST/AKDT)</option>
                                    <option value="Pacific/Honolulu" className="py-2">Hawaii Time (HST)</option>
                                  </optgroup>
                                  <optgroup label="International" className="text-gray-900 font-semibold">
                                    <option value="UTC" className="py-2">UTC (Coordinated Universal)</option>
                                    <option value="Europe/London" className="py-2">London (GMT/BST)</option>
                                    <option value="Europe/Paris" className="py-2">Paris (CET/CEST)</option>
                                    <option value="Asia/Tokyo" className="py-2">Tokyo (JST)</option>
                                    <option value="Australia/Sydney" className="py-2">Sydney (AEST/AEDT)</option>
                                    <option value="America/Toronto" className="py-2">Toronto (EST/EDT)</option>
                                  </optgroup>
                                </select>
                              </div>
                              <p className="mt-1 text-xs text-gray-500">
                                Current time: {new Date().toLocaleTimeString('en-US', { 
                                  timeZone: profileData.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
                                  hour12: true 
                                })}
                              </p>
                            </div>

                            {/* 24 Hours Toggle */}
                            <div className="flex items-center justify-end">
                              <motion.button
                                type="button"
                                onClick={() => setNewLocation(prev => ({ 
                                  ...prev, 
                                  isOpen24Hours: !prev.isOpen24Hours 
                                }))}
                                className={`px-6 py-3 rounded-xl font-semibold text-sm transition-all duration-300 flex items-center space-x-2 shadow-lg ${
                                  newLocation.isOpen24Hours
                                    ? 'bg-gradient-to-r from-purple-500 to-indigo-600 text-white hover:from-purple-600 hover:to-indigo-700 shadow-purple-200'
                                    : 'bg-white text-gray-700 border-2 border-gray-300 hover:border-purple-400 hover:text-purple-700 hover:shadow-purple-100'
                                }`}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                              >
                                <Clock className="w-4 h-4" />
                                <span>Open 24 Hours</span>
                                {newLocation.isOpen24Hours && (
                                  <motion.div
                                    initial={{ scale: 0, rotate: -180 }}
                                    animate={{ scale: 1, rotate: 0 }}
                                    className="w-4 h-4 bg-white rounded-full flex items-center justify-center"
                                  >
                                    <CheckCircle2 className="w-3 h-3 text-purple-600" />
                                  </motion.div>
                                )}
                              </motion.button>
                            </div>
                          </div>

                          {/* Collapsible Daily Hours Section */}
                          <motion.div
                            initial={false}
                            animate={{ 
                              height: newLocation.isOpen24Hours ? 0 : 'auto',
                              opacity: newLocation.isOpen24Hours ? 0 : 1
                            }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="overflow-hidden"
                          >
                            {!newLocation.isOpen24Hours && (
                              <div className="space-y-3">
                                {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => {
                                  const dayHours = newLocation.operatingHours?.[day] || { open: '09:00', close: '17:00', closed: day === 'sunday' };
                                  const isOpen = !dayHours.closed;

                                  // Convert 24-hour to 12-hour format for display
                                  const formatTimeFor12Hour = (time24: string) => {
                                    const [hours, minutes] = time24.split(':').map(Number);
                                    const period = hours >= 12 ? 'PM' : 'AM';
                                    const hours12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
                                    return { hours12, minutes, period, time24 };
                                  };

                                  // Convert 12-hour format to 24-hour
                                  const convertTo24Hour = (hours12: number, minutes: number, period: string) => {
                                    let hours24 = hours12;
                                    if (period === 'AM' && hours12 === 12) hours24 = 0;
                                    if (period === 'PM' && hours12 !== 12) hours24 = hours12 + 12;
                                    return `${hours24.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                                  };

                                  // Validate that opening time is before closing time
                                  const validateTimeOrder = (openTime: string, closeTime: string) => {
                                    const [openHours, openMinutes] = openTime.split(':').map(Number);
                                    const [closeHours, closeMinutes] = closeTime.split(':').map(Number);
                                    const openTotalMinutes = openHours * 60 + openMinutes;
                                    const closeTotalMinutes = closeHours * 60 + closeMinutes;
                                    return openTotalMinutes < closeTotalMinutes;
                                  };

                                  const openTime = formatTimeFor12Hour(dayHours.open);
                                  const closeTime = formatTimeFor12Hour(dayHours.close);
                                  const timeOrderValid = !isOpen || validateTimeOrder(dayHours.open, dayHours.close);

                                  // Generate hour and minute options
                                  const hourOptions = Array.from({ length: 12 }, (_, i) => i + 1);
                                  const minuteOptions = [0, 30];

                                  return (
                                    <div 
                                      key={day} 
                                      className="flex flex-col py-4 px-4 bg-white rounded-xl border border-gray-100 hover:shadow-sm transition-all duration-200 space-y-3"
                                    >
                                      {/* Day and Toggle */}
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-4">
                                          <div className="w-20">
                                            <span className="text-sm text-gray-700 capitalize font-semibold">
                                              {day.charAt(0).toUpperCase() + day.slice(1)}
                                            </span>
                                          </div>

                                          <div className="flex items-center">
                                            <label className="relative inline-flex items-center cursor-pointer">
                                              <input
                                                type="checkbox"
                                                checked={!dayHours.closed}
                                                onChange={(e) => {
                                                  setNewLocation(prev => ({
                                                    ...prev,
                                                    operatingHours: {
                                                      ...prev.operatingHours,
                                                      [day]: { 
                                                        ...dayHours, 
                                                        closed: !e.target.checked 
                                                      }
                                                    }
                                                  }));
                                                }}
                                                className="sr-only"
                                              />
                                              <div className={`w-12 h-6 rounded-full transition-all duration-300 ${
                                                !dayHours.closed
                                                  ? 'bg-gradient-to-r from-purple-500 to-indigo-600 shadow-lg shadow-purple-200' 
                                                  : 'bg-gray-300'
                                              }`}>
                                                <motion.div 
                                                  className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-all duration-300 ${
                                                    !dayHours.closed ? 'translate-x-6' : 'translate-x-0.5'
                                                  } mt-0.5`}
                                                  whileHover={{ scale: 1.1 }}
                                                  animate={{ x: !dayHours.closed ? 24 : 2 }}
                                                />
                                              </div>
                                            </label>
                                          </div>
                                        </div>

                                        {/* Status indicator */}
                                        <div className="flex items-center">
                                          {!dayHours.closed ? (
                                            <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">
                                              Open
                                            </span>
                                          ) : (
                                            <div className="flex items-center text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
                                              <span className="text-xs font-medium text-gray-500">Closed</span>
                                            </div>
                                          )}
                                        </div>
                                      </div>

                                      {/* Time Selection - only show when open */}
                                      {!dayHours.closed && (
                                        <div className="flex items-center space-x-4 pl-24">
                                          <span className="text-sm text-gray-600 font-medium">From</span>

                                          {/* Opening Time */}
                                          <div className="flex items-center space-x-1">
                                            {/* Hours Dropdown */}
                                            <select
                                              value={openTime.hours12}
                                              onChange={(e) => {
                                                const newHours = parseInt(e.target.value);
                                                const newTime24 = convertTo24Hour(newHours, openTime.minutes, openTime.period);
                                                setNewLocation(prev => ({
                                                  ...prev,
                                                  operatingHours: {
                                                    ...prev.operatingHours,
                                                    [day]: { ...dayHours, open: newTime24 }
                                                  }
                                                }));
                                              }}
                                              className={`w-14 text-sm border rounded-lg px-2 py-1.5 bg-white focus:ring-2 transition-all duration-200 font-mono ${
                                                timeOrderValid 
                                                  ? 'border-gray-300 focus:ring-purple-500 focus:border-purple-500' 
                                                  : 'border-red-400 focus:ring-red-500 focus:border-red-500 bg-red-50'
                                              }`}
                                            >
                                              {hourOptions.map(hour => (
                                                <option key={hour} value={hour}>
                                                  {hour.toString().padStart(2, '0')}
                                                </option>
                                              ))}
                                            </select>

                                            <span className="text-gray-400">:</span>

                                            {/* Minutes Dropdown */}
                                            <select
                                              value={openTime.minutes}
                                              onChange={(e) => {
                                                const newMinutes = parseInt(e.target.value);
                                                const newTime24 = convertTo24Hour(openTime.hours12, newMinutes, openTime.period);
                                                setNewLocation(prev => ({
                                                  ...prev,
                                                  operatingHours: {
                                                    ...prev.operatingHours,
                                                    [day]: { ...dayHours, open: newTime24 }
                                                  }
                                                }));
                                              }}
                                              className={`w-14 text-sm border rounded-lg px-2 py-1.5 bg-white focus:ring-2 transition-all duration-200 font-mono ${
                                                timeOrderValid 
                                                  ? 'border-gray-300 focus:ring-purple-500 focus:border-purple-500' 
                                                  : 'border-red-400 focus:ring-red-500 focus:border-red-500 bg-red-50'
                                              }`}
                                            >
                                              {minuteOptions.map(minute => (
                                                <option key={minute} value={minute}>
                                                  {minute.toString().padStart(2, '0')}
                                                </option>
                                              ))}
                                            </select>

                                            {/* AM/PM Dropdown */}
                                            <select
                                              value={openTime.period}
                                              onChange={(e) => {
                                                const newPeriod = e.target.value as 'AM' | 'PM';
                                                const newTime24 = convertTo24Hour(openTime.hours12, openTime.minutes, newPeriod);
                                                setNewLocation(prev => ({
                                                  ...prev,
                                                  operatingHours: {
                                                    ...prev.operatingHours,
                                                    [day]: { ...dayHours, open: newTime24 }
                                                  }
                                                }));
                                              }}
                                              className="w-16 text-sm border border-gray-300 rounded-lg px-2 py-1.5 bg-gradient-to-r from-purple-50 to-indigo-50 hover:from-purple-100 hover:to-indigo-100 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 font-semibold text-purple-700 cursor-pointer"
                                            >
                                              <option value="AM">AM</option>
                                              <option value="PM">PM</option>
                                            </select>
                                          </div>

                                          <span className="text-sm text-gray-600 font-medium">To</span>

                                          {/* Closing Time */}
                                          <div className="flex items-center space-x-1">
                                            {/* Hours Dropdown */}
                                            <select
                                              value={closeTime.hours12}
                                              onChange={(e) => {
                                                const newHours = parseInt(e.target.value);
                                                const newTime24 = convertTo24Hour(newHours, closeTime.minutes, closeTime.period);
                                                setNewLocation(prev => ({
                                                  ...prev,
                                                  operatingHours: {
                                                    ...prev.operatingHours,
                                                    [day]: { ...dayHours, close: newTime24 }
                                                  }
                                                }));
                                              }}
                                              className={`w-14 text-sm border rounded-lg px-2 py-1.5 bg-white focus:ring-2 transition-all duration-200 font-mono ${
                                                timeOrderValid 
                                                  ? 'border-gray-300 focus:ring-purple-500 focus:border-purple-500' 
                                                  : 'border-red-400 focus:ring-red-500 focus:border-red-500 bg-red-50'
                                              }`}
                                            >
                                              {hourOptions.map(hour => (
                                                <option key={hour} value={hour}>
                                                  {hour.toString().padStart(2, '0')}
                                                </option>
                                              ))}
                                            </select>

                                            <span className="text-gray-400">:</span>

                                            {/* Minutes Dropdown */}
                                            <select
                                              value={closeTime.minutes}
                                              onChange={(e) => {
                                                const newMinutes = parseInt(e.target.value);
                                                const newTime24 = convertTo24Hour(closeTime.hours12, newMinutes, closeTime.period);
                                                setNewLocation(prev => ({
                                                  ...prev,
                                                  operatingHours: {
                                                    ...prev.operatingHours,
                                                    [day]: { ...dayHours, close: newTime24 }
                                                  }
                                                }));
                                              }}
                                              className={`w-14 text-sm border rounded-lg px-2 py-1.5 bg-white focus:ring-2 transition-all duration-200 font-mono ${
                                                timeOrderValid 
                                                  ? 'border-gray-300 focus:ring-purple-500 focus:border-purple-500' 
                                                  : 'border-red-400 focus:ring-red-500 focus:border-red-500 bg-red-50'
                                              }`}
                                            >
                                              {minuteOptions.map(minute => (
                                                <option key={minute} value={minute}>
                                                  {minute.toString().padStart(2, '0')}
                                                </option>
                                              ))}
                                            </select>

                                            {/* AM/PM Dropdown */}
                                            <select
                                              value={closeTime.period}
                                              onChange={(e) => {
                                                const newPeriod = e.target.value as 'AM' | 'PM';
                                                const newTime24 = convertTo24Hour(closeTime.hours12, closeTime.minutes, newPeriod);
                                                setNewLocation(prev => ({
                                                  ...prev,
                                                  operatingHours: {
                                                    ...prev.operatingHours,
                                                    [day]: { ...dayHours, close: newTime24 }
                                                  }
                                                }));
                                              }}
                                              className="w-16 text-sm border border-gray-300 rounded-lg px-2 py-1.5 bg-gradient-to-r from-purple-50 to-indigo-50 hover:from-purple-100 hover:to-indigo-100 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 font-semibold text-purple-700 cursor-pointer"
                                            >
                                              <option value="AM">AM</option>
                                              <option value="PM">PM</option>
                                            </select>
                                          </div>
                                        </div>
                                      )}

                                      {/* Time validation error */}
                                      {!dayHours.closed && !timeOrderValid && (
                                        <motion.div 
                                          initial={{ opacity: 0, y: -10 }}
                                          animate={{ opacity: 1, y: 0 }}
                                          className="flex items-center text-red-600 bg-red-50 px-3 py-2 rounded-lg ml-24 border border-red-200"
                                        >
                                          <AlertCircle className="w-4 h-4 mr-2" />
                                          <span className="text-sm font-medium">
                                            Opening time must be before closing time
                                          </span>
                                        </motion.div>
                                      )}
                                    </div>
                                  );
                                })}
                              </div>
                            )}
                          </motion.div>

                          {/* 24-Hour Message */}
                          {newLocation.isOpen24Hours && (
                            <motion.div 
                              initial={{ opacity: 0, scale: 0.95 }}
                              animate={{ opacity: 1, scale: 1 }}
                              className="text-center py-8"
                            >
                              <div className="inline-flex items-center space-x-3 px-6 py-4 bg-gradient-to-r from-purple-100 to-indigo-100 rounded-2xl border border-purple-200">
                                <motion.div
                                  animate={{ rotate: 360 }}
                                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                                  className="w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg"
                                >
                                  <Clock className="w-4 h-4 text-white" />
                                </motion.div>
                                <div className="text-left">
                                  <p className="text-sm font-semibold text-purple-800">Open 24 Hours, 7 Days a Week</p>
                                  <p className="text-xs text-purple-600">Always available for your customers</p>
                                </div>
                              </div>
                            </motion.div>
                          )}

                          {/* Quick presets - only show when not 24 hours */}
                          {!newLocation.isOpen24Hours && (
                            <div className="pt-4 border-t border-gray-200">
                              <p className="text-sm text-gray-600 mb-4 font-medium flex items-center">
                                <Calendar className="w-4 h-4 mr-2" />
                                Quick presets
                              </p>
                              <div className="grid grid-cols-1 sm:grid-cols-4 gap-3">
                                <motion.button
                                  type="button"
                                  onClick={() => {
                                    const businessHours = {
                                      monday: { open: '09:00', close: '17:00', closed: false },
                                      tuesday: { open: '09:00', close: '17:00', closed: false },
                                      wednesday: { open: '09:00', close: '17:00', closed: false },
                                      thursday: { open: '09:00', close: '17:00', closed: false },
                                      friday: { open: '09:00', close: '17:00', closed: false },
                                      saturday: { open: '09:00', close: '17:00', closed: true },
                                      sunday: { open: '09:00', close: '17:00', closed: true }
                                    };
                                    setNewLocation(prev => ({ ...prev, operatingHours: businessHours }));
                                  }}
                                  className="flex items-center justify-center px-3 py-3 bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 text-purple-700 rounded-xl hover:from-purple-100 hover:to-indigo-100 hover:border-purple-300 transition-all duration-200 font-semibold text-sm group shadow-sm hover:shadow-md"
                                  whileHover={{ scale: 1.02, y: -1 }}
                                  whileTap={{ scale: 0.98 }}
                                >
                                  <Briefcase className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                                  Business
                                </motion.button>
                                <motion.button
                                  type="button"
                                  onClick={() => {
                                    const retailHours = {
                                      monday: { open: '10:00', close: '20:00', closed: false },
                                      tuesday: { open: '10:00', close: '20:00', closed: false },
                                      wednesday: { open: '10:00', close: '20:00', closed: false },
                                      thursday: { open: '10:00', close: '20:00', closed: false },
                                      friday: { open: '10:00', close: '20:00', closed: false },
                                      saturday: { open: '10:00', close: '20:00', closed: false },
                                      sunday: { open: '12:00', close: '18:00', closed: false }
                                    };
                                    setNewLocation(prev => ({ ...prev, operatingHours: retailHours }));
                                  }}
                                  className="flex items-center justify-center px-3 py-3 bg-gradient-to-r from-pink-50 to-purple-50 border border-pink-200 text-pink-700 rounded-xl hover:from-pink-100 hover:to-purple-100 hover:border-pink-300 transition-all duration-200 font-semibold text-sm group shadow-sm hover:shadow-md"
                                  whileHover={{ scale: 1.02, y: -1 }}
                                  whileTap={{ scale: 0.98 }}
                                >
                                  <ShoppingBag className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                                  Retail
                                </motion.button>
                                <motion.button
                                  type="button"
                                  onClick={() => {
                                    const restaurantHours = {
                                      monday: { open: '11:00', close: '22:00', closed: false },
                                      tuesday: { open: '11:00', close: '22:00', closed: false },
                                      wednesday: { open: '11:00', close: '22:00', closed: false },
                                      thursday: { open: '11:00', close: '22:00', closed: false },
                                      friday: { open: '11:00', close: '23:00', closed: false },
                                      saturday: { open: '11:00', close: '23:00', closed: false },
                                      sunday: { open: '11:00', close: '21:00', closed: false }
                                    };
                                    setNewLocation(prev => ({ ...prev, operatingHours: restaurantHours }));
                                  }}
                                  className="flex items-center justify-center px-3 py-3 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 text-green-700 rounded-xl hover:from-green-100 hover:to-emerald-100 hover:border-green-300 transition-all duration-200 font-semibold text-sm group shadow-sm hover:shadow-md"
                                  whileHover={{ scale: 1.02, y: -1 }}
                                  whileTap={{ scale: 0.98 }}
                                >
                                  <Restaurant className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                                  Restaurant
                                </motion.button>
                                <motion.button
                                  type="button"
                                  onClick={() => {
                                    setNewLocation(prev => ({ ...prev, isOpen24Hours: true }));
                                  }}
                                  className="flex items-center justify-center px-3 py-3 bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 text-indigo-700 rounded-xl hover:from-indigo-100 hover:to-blue-100 hover:border-indigo-300 transition-all duration-200 font-semibold text-sm group shadow-sm hover:shadow-md"
                                  whileHover={{ scale: 1.02, y: -1 }}
                                  whileTap={{ scale: 0.98 }}
                                >
                                  <Clock className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                                  24/7
                                </motion.button>
                              </div>
                            </div>
                          )}
                        </motion.div>
                      )}
                    </div>
                  </div>

                  {/* Website Configuration */}
                  <div className="bg-gray-50 border border-gray-200 rounded-xl p-6">
                    <div className="flex items-center mb-4">
                      <Globe className="w-5 h-5 text-indigo-600 mr-2" />
                      <h4 className="text-lg font-semibold text-gray-900">Website Configuration</h4>
                    </div>
                    
                    <div className="space-y-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="usePrimaryWebsite"
                          checked={newLocation.usePrimaryWebsite}
                          onChange={(e) => setNewLocation(prev => ({ 
                            ...prev, 
                            usePrimaryWebsite: e.target.checked,
                            website: e.target.checked ? '' : prev.website
                          }))}
                          className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                        />
                        <label htmlFor="usePrimaryWebsite" className="ml-2 text-sm text-gray-700">
                          Use same website as primary business ({profileData.website || 'Not set'})
                        </label>
                      </div>

                      {!newLocation.usePrimaryWebsite && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Location-Specific Website
                          </label>
                          <div className="relative">
                            <Globe className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                            <input
                              type="text"
                              value={newLocation.website}
                              onChange={(e) => setNewLocation(prev => ({ ...prev, website: e.target.value }))}
                              className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                              placeholder="https://downtown.yourbusiness.com"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={() => {
                        setShowNewLocationForm(false);
                        setEditingLocationId(null);
                        // Reset form when canceling
                        setNewLocation({
                          name: '',
                          address: '',
                          addressLine2: '',
                          city: '',
                          state: '',
                          zipCode: '',
                          country: 'United States',
                          phone: '',
                          website: '',
                          usePrimaryWebsite: true,
                          usePrimaryHours: true,
                          businessType: 'physical',
                          businessCategory: '',
                          businessTags: [],
                          usePrimaryCategory: true,
                          usePrimaryTags: true,
                          isPrimary: false,
                          canBeDeleted: true,
                          isOpen24Hours: false,
                          operatingHours: {
                            monday: { open: '09:00', close: '17:00', closed: false },
                            tuesday: { open: '09:00', close: '17:00', closed: false },
                            wednesday: { open: '09:00', close: '17:00', closed: false },
                            thursday: { open: '09:00', close: '17:00', closed: false },
                            friday: { open: '09:00', close: '17:00', closed: false },
                            saturday: { open: '10:00', close: '16:00', closed: false },
                            sunday: { open: '10:00', close: '16:00', closed: true }
                          }
                        });
                        setNewLocationValidation({
                          isValidating: false,
                          isValid: null,
                          suggestion: null
                        });
                      }}
                      className="px-6 py-3 text-gray-600 hover:text-gray-800 font-medium transition-colors"
                    >
                      Cancel
                    </button>
                    
                    <motion.button
                      type="button"
                      onClick={editingLocationId ? updateLocation : addLocation}
                      disabled={
                        !newLocation.name || 
                        !newLocation.address ||
                        newLocationValidation.isValid !== true
                      }
                      className="px-6 py-3 bg-indigo-600 text-white rounded-xl hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center font-medium"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {editingLocationId ? (
                        <>
                          <Save className="w-4 h-4 mr-2" />
                          Update Location
                        </>
                      ) : (
                        <>
                          <Plus className="w-4 h-4 mr-2" />
                          Add Location
                        </>
                      )}
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Existing Locations */}
            {profileData.locations.length > 0 && (
              <div className="space-y-4">
                <h4 className="text-lg font-semibold text-gray-900">Additional Locations</h4>
                <div className="space-y-3">
                  {profileData.locations.map((location) => (
                    <motion.div 
                      key={location.id} 
                      className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-md transition-all duration-200"
                      whileHover={{ scale: 1.01 }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center mb-2">
                            <MapPin className="w-5 h-5 text-gray-400 mr-2" />
                            <h5 className="font-semibold text-gray-900">{location.name}</h5>
                          </div>
                          <p className="text-gray-600 mb-2">
                            {location.formattedAddress || `${location.address}${location.addressLine2 ? `, ${location.addressLine2}` : ''}, ${location.city}, ${location.state} ${location.zipCode}`}
                          </p>
                          <div className="flex flex-wrap gap-2 text-xs">
                            <span className={`px-2 py-1 rounded-full ${
                              location.usePrimaryWebsite ? 'bg-blue-100 text-blue-700' : 'bg-orange-100 text-orange-700'
                            }`}>
                              {location.usePrimaryWebsite ? 'Primary website' : 'Custom website'}
                            </span>
                            <span className={`px-2 py-1 rounded-full ${
                              location.usePrimaryHours ? 'bg-green-100 text-green-700' : 'bg-purple-100 text-purple-700'
                            }`}>
                              {location.usePrimaryHours ? 'Primary hours' : 'Custom hours'}
                            </span>
                            <span className={`px-2 py-1 rounded-full ${
                              location.usePrimaryCategory ? 'bg-indigo-100 text-indigo-700' : 'bg-pink-100 text-pink-700'
                            }`}>
                              {location.usePrimaryCategory ? 'Primary category' : 'Custom category'}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <motion.button
                            type="button"
                            onClick={() => editLocation(location.id)}
                            className="p-2 text-gray-400 hover:text-indigo-600 rounded-full hover:bg-indigo-50 transition-colors"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            title="Edit location"
                          >
                            <Edit3 className="w-4 h-4" />
                          </motion.button>
                          <motion.button
                            type="button"
                            onClick={() => {
                              if (window.confirm(`Are you sure you want to delete "${location.name}"? This action cannot be undone.`)) {
                                removeLocation(location.id);
                              }
                            }}
                            className="p-2 text-red-400 hover:text-red-600 rounded-full hover:bg-red-50 transition-colors"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            title="Delete location"
                          >
                            <Trash2 className="w-4 h-4" />
                          </motion.button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mx-auto mb-4">
                <CheckCircle2 className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Review Your Business Profile</h3>
              <p className="text-gray-600">Please review all information before completing your profile setup.</p>
            </div>

            {/* Business Overview */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-2xl p-8 border border-gray-200 shadow-sm"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center mr-4">
                  <Building className="w-6 h-6 text-indigo-600" />
                </div>
                <div>
                  <h4 className="text-xl font-semibold text-gray-900">Business Information</h4>
                  <p className="text-gray-600 text-sm">Core business details and contact information</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Business Name</label>
                  <p className="text-lg font-semibold text-gray-900">{profileData.businessName}</p>
                </div>

                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Phone Number</label>
                  <p className="text-lg text-gray-900">{profileData.phoneNumber}</p>
                </div>

                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Business Type</label>
                  <div className="flex items-center">
                    {profileData.businessType === 'physical' ? (
                      <>
                        <Building className="w-4 h-4 text-indigo-600 mr-2" />
                        <span className="text-gray-900">Physical Location</span>
                      </>
                    ) : (
                      <>
                        <Navigation className="w-4 h-4 text-indigo-600 mr-2" />
                        <span className="text-gray-900">Service-Based</span>
                      </>
                    )}
                  </div>
                </div>

                {profileData.businessType === 'physical' && profileData.primaryAddress && (
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Primary Address</label>
                    <p className="text-gray-900">{profileData.primaryAddress}</p>
                  </div>
                )}

                {profileData.businessType === 'service' && profileData.serviceArea && (
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Service Area</label>
                    <p className="text-gray-900">{profileData.serviceArea}</p>
                  </div>
                )}

                {profileData.website && (
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Website</label>
                    <p className="text-gray-900 break-all">{profileData.website}</p>
                  </div>
                )}

                {profileData.contactEmail && (
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Contact Email</label>
                    <p className="text-gray-900">{profileData.contactEmail}</p>
                  </div>
                )}
              </div>

              {profileData.description && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Description</label>
                  <p className="text-gray-700 leading-relaxed">{profileData.description}</p>
                </div>
              )}
            </motion.div>

            {/* Business Category & Tags */}
            {profileData.businessCategory && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-white rounded-2xl p-8 border border-gray-200 shadow-sm"
              >
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mr-4">
                    <Tag className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900">Business Category & Tags</h4>
                    <p className="text-gray-600 text-sm">Category and tags for voucher classification</p>
                  </div>
                </div>

                <div className="space-y-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Business Category</label>
                    <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 text-indigo-800 rounded-xl font-medium">
                      {businessCategories[profileData.businessCategory as keyof typeof businessCategories]?.label}
                    </div>
                  </div>

                  {profileData.businessTags && profileData.businessTags.length > 0 && (
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Tags</label>
                      <div className="flex flex-wrap gap-2">
                        {profileData.businessTags.map((tag, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-purple-50 border border-purple-200 text-purple-700 rounded-lg text-sm font-medium"
                          >
                            {tag.replace('-', ' ')}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            )}

            {/* Primary Location Card */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-8 border-2 border-indigo-200 shadow-sm"
            >
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                    <Building className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900 flex items-center">
                      Primary Location
                      <span className="ml-3 px-3 py-1 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-xs font-bold rounded-full">
                        PRIMARY
                      </span>
                    </h4>
                    <p className="text-gray-600 text-sm">Main business location</p>
                  </div>
                </div>
              </div>

              <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Location Name</label>
                    <p className="text-lg font-semibold text-gray-900">{profileData.businessName}</p>
                  </div>

                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Phone Number</label>
                    <p className="text-gray-900">{profileData.phoneNumber}</p>
                  </div>

                  {profileData.businessType === 'physical' && profileData.primaryAddress && (
                    <div className="md:col-span-2 space-y-1">
                      <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Address</label>
                      <p className="text-gray-900">{profileData.primaryAddress}</p>
                    </div>
                  )}

                  {profileData.businessType === 'service' && profileData.serviceArea && (
                    <div className="md:col-span-2 space-y-1">
                      <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Service Area</label>
                      <p className="text-gray-900">{profileData.serviceArea}</p>
                    </div>
                  )}

                  {profileData.website && (
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Website</label>
                      <p className="text-gray-900 break-all">{profileData.website}</p>
                    </div>
                  )}

                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Category</label>
                    <p className="text-gray-900">
                      {profileData.businessCategory ? 
                        businessCategories[profileData.businessCategory as keyof typeof businessCategories]?.label : 
                        'Not set'
                      }
                    </p>
                  </div>
                </div>

                {/* Operating Hours for Primary Location */}
                <div className="pt-4 border-t border-gray-200">
                  <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">Operating Hours</label>
                  {profileData.isOpen24Hours ? (
                    <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-xl font-medium">
                      <Clock className="w-4 h-4 mr-2" />
                      Open 24 Hours, 7 Days a Week
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-7 gap-3">
                      {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => {
                        const dayHours = profileData.primaryHours?.[day];
                        return (
                          <div key={day} className="text-center p-2 bg-white rounded-lg border border-gray-200">
                            <div className="font-semibold text-gray-900 capitalize text-sm mb-1">{day.slice(0, 3)}</div>
                            {dayHours?.closed ? (
                              <div className="text-red-500 text-xs font-medium">Closed</div>
                            ) : (
                              <div className="text-gray-700 text-xs">
                                <div>{dayHours?.open}</div>
                                <div className="text-gray-400">to</div>
                                <div>{dayHours?.close}</div>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>

            {/* Additional Locations */}
            {profileData.locations.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="space-y-6"
              >
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-4">
                    <MapPin className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900">Additional Locations</h4>
                    <p className="text-gray-600 text-sm">{profileData.locations.length} additional location{profileData.locations.length > 1 ? 's' : ''} configured</p>
                  </div>
                </div>

                <div className="space-y-4">
                  {profileData.locations.map((location, index) => (
                    <div key={location.id} className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <MapPin className="w-5 h-5 text-blue-600" />
                          </div>
                          <div>
                            <h5 className="text-lg font-semibold text-gray-900 flex items-center">
                              {location.name}
                              <span className="ml-3 px-2 py-1 bg-blue-100 text-blue-700 text-xs font-bold rounded-full">
                                LOCATION {index + 2}
                              </span>
                            </h5>
                            <p className="text-gray-600 text-sm">Additional business location</p>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {location.phone && (
                          <div className="space-y-1">
                            <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Phone Number</label>
                            <p className="text-gray-900">{location.phone}</p>
                          </div>
                        )}

                        <div className="space-y-1">
                          <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Business Type</label>
                          <div className="flex items-center">
                            {location.businessType === 'physical' ? (
                              <>
                                <Building className="w-4 h-4 text-blue-600 mr-2" />
                                <span className="text-gray-900">Physical Location</span>
                              </>
                            ) : (
                              <>
                                <Navigation className="w-4 h-4 text-blue-600 mr-2" />
                                <span className="text-gray-900">Service-Based</span>
                              </>
                            )}
                          </div>
                        </div>

                        <div className="md:col-span-2 space-y-1">
                          <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Full Address</label>
                          <p className="text-gray-900">
                            {location.formattedAddress || `${location.address}${location.addressLine2 ? `, ${location.addressLine2}` : ''}, ${location.city}, ${location.state} ${location.zipCode}`}
                          </p>
                        </div>

                        <div className="space-y-1">
                          <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Website</label>
                          {location.usePrimaryWebsite ? (
                            <div className="flex items-center text-gray-600">
                              <span className="text-sm">Uses primary website</span>
                              <span className="ml-2 px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">INHERITED</span>
                            </div>
                          ) : (
                            <p className="text-gray-900 break-all">{location.website || 'Not specified'}</p>
                          )}
                        </div>

                        <div className="space-y-1">
                          <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide">Business Category</label>
                          {location.usePrimaryCategory ? (
                            <div className="flex items-center text-gray-600">
                              <span className="text-sm">Uses primary category</span>
                              <span className="ml-2 px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">INHERITED</span>
                            </div>
                          ) : (
                            <p className="text-gray-900">
                              {location.businessCategory ? 
                                businessCategories[location.businessCategory as keyof typeof businessCategories]?.label : 
                                'Not set'
                              }
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Tags for this location */}
                      {!location.usePrimaryTags && location.businessTags && location.businessTags.length > 0 && (
                        <div className="mt-4 pt-4 border-t border-gray-200">
                          <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Location Tags</label>
                          <div className="flex flex-wrap gap-2">
                            {location.businessTags.map((tag, tagIndex) => (
                              <span
                                key={tagIndex}
                                className="px-2 py-1 bg-blue-50 border border-blue-200 text-blue-700 rounded-md text-xs font-medium"
                              >
                                {tag.replace('-', ' ')}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {location.usePrimaryTags && (
                        <div className="mt-4 pt-4 border-t border-gray-200">
                          <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Location Tags</label>
                          <div className="flex items-center text-gray-600">
                            <span className="text-sm">Uses primary business tags</span>
                            <span className="ml-2 px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">INHERITED</span>
                          </div>
                        </div>
                      )}

                      {/* Operating Hours for this location */}
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <label className="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">Operating Hours</label>
                        {location.usePrimaryHours ? (
                          <div className="flex items-center text-gray-600">
                            <span className="text-sm">Uses primary business hours</span>
                            <span className="ml-2 px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">INHERITED</span>
                          </div>
                        ) : (
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-7 gap-3">
                            {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => {
                              const dayHours = location.operatingHours?.[day];
                              return (
                                <div key={day} className="text-center p-2 bg-gray-50 rounded-lg border border-gray-200">
                                  <div className="font-semibold text-gray-900 capitalize text-sm mb-1">{day.slice(0, 3)}</div>
                                  {dayHours?.closed ? (
                                    <div className="text-red-500 text-xs font-medium">Closed</div>
                                  ) : (
                                    <div className="text-gray-700 text-xs">
                                      <div>{dayHours?.open}</div>
                                      <div className="text-gray-400">to</div>
                                      <div>{dayHours?.close}</div>
                                    </div>
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Logo Preview */}
            {profileData.logoUrl && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="bg-white rounded-2xl p-8 border border-gray-200 shadow-sm"
              >
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mr-4">
                    <Upload className="w-6 h-6 text-orange-600" />
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900">Business Logo</h4>
                    <p className="text-gray-600 text-sm">Logo to be used across all locations</p>
                  </div>
                </div>
                <div className="flex items-center justify-center">
                  <div className="relative">
                    <img 
                      src={profileData.logoUrl} 
                      alt="Business Logo" 
                      className="w-32 h-32 object-cover rounded-2xl border-4 border-white shadow-xl"
                    />
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <CheckCircle2 className="w-5 h-5 text-white" />
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Summary Statistics */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200"
            >
              <div className="flex items-center justify-center">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-8 mb-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">{profileData.locations.length + 1}</div>
                      <div className="text-sm text-gray-600">Location{(profileData.locations.length + 1) > 1 ? 's' : ''}</div>
                    </div>
                    <div className="w-px h-12 bg-green-200"></div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">
                        {profileData.businessTags ? profileData.businessTags.length : 0}
                      </div>
                      <div className="text-sm text-gray-600">Tag{(profileData.businessTags?.length || 0) !== 1 ? 's' : ''}</div>
                    </div>
                    <div className="w-px h-12 bg-green-200"></div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">1</div>
                      <div className="text-sm text-gray-600">Category</div>
                    </div>
                  </div>
                  <p className="text-green-700 font-medium">Ready to launch your referral campaigns!</p>
                </div>
              </div>
            </motion.div>
          </div>
        );

      default:
        return null;
    }
  };

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case 1:
        const hasRequiredAddress = profileData.businessType === 'physical' 
          ? profileData.primaryAddress 
          : profileData.serviceArea;
        
        // Check validation state for the appropriate field
        const addressValidationPassed = profileData.businessType === 'physical'
          ? (addressValidation.isValid === true || !profileData.primaryAddress)
          : (serviceAreaValidation.isValid === true || !profileData.serviceArea);
        
        return (
          profileData.businessName &&
          hasRequiredAddress &&
          profileData.phoneNumber &&
          profileData.businessCategory &&
          profileData.businessType &&
          addressValidationPassed &&
          !Object.values(validationErrors).some(error => error)
        );
      case 2:
        return true; // Step 2 is optional
      case 3:
        return true; // Review step, always can complete
      default:
        return false;
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps && canProceedToNextStep()) {
      // Validate current step fields before proceeding
      let hasErrors = false;

      if (currentStep === 1) {
        const requiredFields = ['businessName', 'phoneNumber', 'businessCategory'];
        
        // Add address validation based on business type
        if (profileData.businessType === 'physical') {
          requiredFields.push('primaryAddress');
        } else if (profileData.businessType === 'service') {
          requiredFields.push('serviceArea');
        }
        
        requiredFields.forEach(field => {
          if (!validateField(field, (profileData as any)[field])) {
            hasErrors = true;
          }
        });
      }

      if (!hasErrors) {
        setCurrentStep(prev => prev + 1);
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Progress Steps */}
        <div className="mb-12">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const StepIcon = step.icon;
              const isActive = currentStep === step.id;
              const isCompleted = currentStep > step.id;

              return (
                <React.Fragment key={step.id}>
                  <div className="flex flex-col items-center">
                    <motion.div
                      className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 transition-all duration-300 ${
                        isCompleted
                          ? 'bg-green-500 text-white shadow-lg shadow-green-200'
                          : isActive
                          ? 'bg-indigo-600 text-white shadow-lg shadow-indigo-200'
                          : 'bg-gray-200 text-gray-600'
                      }`}
                      whileHover={{ scale: 1.05 }}
                      animate={isActive ? { scale: [1, 1.05, 1] } : {}}
                      transition={{ duration: 0.3 }}
                    >
                      {isCompleted ? (
                        <CheckCircle2 className="w-6 h-6" />
                      ) : (
                        <StepIcon className="w-6 h-6" />
                      )}
                    </motion.div>
                    <span className={`text-sm font-medium ${
                      isActive ? 'text-indigo-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </span>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`flex-1 h-0.5 mx-4 transition-all duration-300 ${
                      currentStep > step.id ? 'bg-green-500' : 'bg-gray-300'
                    }`} />
                  )}
                </React.Fragment>
              );
            })}
          </div>
        </div>

        {/* Main Content */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
          className="bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-200"
        >
          <div className="p-8 md:p-12">
            {renderStepContent()}
          </div>

          {/* Navigation */}
          <div className="px-8 md:px-12 py-6 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className="flex items-center px-6 py-3 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
              Previous
            </button>

            <div className="flex items-center space-x-2">
              {Array.from({ length: totalSteps }, (_, i) => (
                <div
                  key={i}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    i + 1 === currentStep
                      ? 'bg-indigo-600 w-8'
                      : i + 1 < currentStep
                      ? 'bg-green-500'
                      : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>

            {currentStep < totalSteps ? (
              <motion.button
                onClick={nextStep}
                disabled={!canProceedToNextStep()}
                className="flex items-center px-6 py-3 bg-indigo-600 text-white rounded-xl hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Next Step
                <ArrowRight className="w-4 h-4 ml-2" />
              </motion.button>
            ) : (
              <motion.button
                onClick={handleSubmit}
                disabled={!canProceedToNextStep()}
                className="flex items-center px-6 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {existingData ? 'Save Changes' : 'Complete Setup'}
                <Save className="w-4 h-4 ml-2" />
              </motion.button>
            )}
          </div>
        </motion.div>

        {/* Upgrade Plan Modal */}
        {showUpgradeModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowUpgradeModal(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="relative p-8 pb-6 bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600 text-white rounded-t-3xl">
                <button
                  onClick={() => setShowUpgradeModal(false)}
                  className="absolute top-4 right-4 p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-full transition-all"
                >
                  <X className="w-6 h-6" />
                </button>
                
                <div className="text-center">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6"
                  >
                    <Star className="w-10 h-10 text-yellow-300" />
                  </motion.div>
                  <h2 className="text-3xl font-bold mb-2">Upgrade to Premium</h2>
                  <p className="text-white/90 text-lg">Unlock unlimited locations and advanced features</p>
                </div>
              </div>

              {/* Plans */}
              <div className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Free Plan */}
                  <div className="bg-gray-50 rounded-2xl p-6 border-2 border-gray-200">
                    <div className="text-center mb-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">Free</h3>
                      <div className="text-3xl font-bold text-gray-900 mb-1">$0</div>
                      <p className="text-gray-600">per month</p>
                    </div>
                    <ul className="space-y-3 mb-6">
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        2 locations
                      </li>
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        5 active campaigns
                      </li>
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        10 published vouchers
                      </li>
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        Basic analytics
                      </li>
                    </ul>
                    <button
                      disabled
                      className="w-full py-3 px-4 bg-gray-300 text-gray-500 rounded-xl font-semibold cursor-not-allowed"
                    >
                      Current Plan
                    </button>
                  </div>

                  {/* Basic Plan */}
                  <div className="bg-white rounded-2xl p-6 border-2 border-indigo-200 relative">
                    <div className="text-center mb-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">Basic</h3>
                      <div className="text-3xl font-bold text-indigo-600 mb-1">$29</div>
                      <p className="text-gray-600">per month</p>
                    </div>
                    <ul className="space-y-3 mb-6">
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        5 locations
                      </li>
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        15 active campaigns
                      </li>
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        50 published vouchers
                      </li>
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        Advanced analytics
                      </li>
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        Email support
                      </li>
                    </ul>
                    <motion.button
                      className="w-full py-3 px-4 bg-indigo-600 text-white rounded-xl font-semibold hover:bg-indigo-700 transition-all"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => {
                        // Placeholder for upgrade functionality
                        alert('Upgrade functionality coming soon!');
                        setShowUpgradeModal(false);
                      }}
                    >
                      Choose Basic
                    </motion.button>
                  </div>

                  {/* Premium Plan */}
                  <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-6 border-2 border-purple-300 relative">
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                        MOST POPULAR
                      </span>
                    </div>
                    <div className="text-center mb-6 mt-4">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">Premium</h3>
                      <div className="text-3xl font-bold text-purple-600 mb-1">$99</div>
                      <p className="text-gray-600">per month</p>
                    </div>
                    <ul className="space-y-3 mb-6">
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        Unlimited locations
                      </li>
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        Unlimited campaigns
                      </li>
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        Unlimited vouchers
                      </li>
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        Real-time analytics
                      </li>
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        Priority support
                      </li>
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        Custom branding
                      </li>
                      <li className="flex items-center text-gray-700">
                        <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        API access
                      </li>
                    </ul>
                    <motion.button
                      className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-xl font-semibold hover:from-purple-700 hover:to-indigo-700 transition-all shadow-lg"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => {
                        // Placeholder for upgrade functionality
                        alert('Upgrade functionality coming soon!');
                        setShowUpgradeModal(false);
                      }}
                    >
                      Choose Premium
                    </motion.button>
                  </div>
                </div>

                {/* Features Comparison */}
                <div className="mt-12 pt-8 border-t border-gray-200">
                  <h3 className="text-xl font-bold text-gray-900 mb-6 text-center">Why upgrade?</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Building className="w-8 h-8 text-purple-600" />
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">Multiple Locations</h4>
                      <p className="text-gray-600 text-sm">Manage all your business locations from one dashboard</p>
                    </div>
                    <div className="text-center">
                      <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Users className="w-8 h-8 text-indigo-600" />
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">More Campaigns</h4>
                      <p className="text-gray-600 text-sm">Run multiple referral campaigns simultaneously</p>
                    </div>
                    <div className="text-center">
                      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Star className="w-8 h-8 text-blue-600" />
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">Priority Support</h4>
                      <p className="text-gray-600 text-sm">Get help when you need it with dedicated support</p>
                    </div>
                  </div>
                </div>

                {/* Trust indicators */}
                <div className="mt-8 pt-6 border-t border-gray-200 text-center">
                  <p className="text-gray-600 text-sm mb-4">Trusted by thousands of businesses worldwide</p>
                  <div className="flex items-center justify-center space-x-4 text-gray-400">
                    <div className="flex items-center">
                      <CheckCircle2 className="w-4 h-4 mr-1" />
                      <span className="text-xs">30-day money back</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle2 className="w-4 h-4 mr-1" />
                      <span className="text-xs">Cancel anytime</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle2 className="w-4 h-4 mr-1" />
                      <span className="text-xs">Secure payments</span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default BusinessProfileSetup;