
import React from 'react';
import { motion } from 'framer-motion';
import { Sparkles, TrendingUp, MapPin, Clock } from 'lucide-react';

interface Recommendation {
  id: string;
  title: string;
  business: string;
  discount: string;
  reason: string;
  icon: React.ElementType;
  color: string;
  urgency?: boolean;
}

const SmartRecommendations: React.FC = () => {
  const recommendations: Recommendation[] = [
    {
      id: '1',
      title: 'Coffee & Pastry Deal',
      business: 'Sunrise Café',
      discount: '25% off',
      reason: 'Based on your morning routine',
      icon: Clock,
      color: 'from-orange-500 to-amber-500'
    },
    {
      id: '2',
      title: 'Fitness Class Package',
      business: 'FitZone Gym',
      discount: '40% off',
      reason: 'Trending in your area',
      icon: TrendingUp,
      color: 'from-green-500 to-emerald-500',
      urgency: true
    },
    {
      id: '3',
      title: 'Local Restaurant',
      business: 'Pasta Paradise',
      discount: '30% off',
      reason: '5 min from your location',
      icon: MapPin,
      color: 'from-red-500 to-pink-500'
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="flex items-center justify-center mb-4">
            <Sparkles className="w-8 h-8 text-purple-600 mr-2" />
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Personalized For You
            </h2>
          </div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            AI-powered recommendations based on your preferences, location, and behavior
          </p>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-6">
          {recommendations.map((rec, index) => {
            const IconComponent = rec.icon;
            return (
              <motion.div
                key={rec.id}
                className="relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 group cursor-pointer border border-gray-100 overflow-hidden"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                whileHover={{ 
                  scale: 1.02,
                  y: -5,
                  transition: { duration: 0.3 }
                }}
              >
                {rec.urgency && (
                  <motion.div
                    className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    Hot Deal!
                  </motion.div>
                )}
                
                <motion.div 
                  className={`w-14 h-14 mb-4 bg-gradient-to-br ${rec.color} rounded-xl flex items-center justify-center shadow-lg`}
                  whileHover={{ 
                    rotate: 360,
                    transition: { duration: 0.6 }
                  }}
                >
                  <IconComponent className="w-7 h-7 text-white" />
                </motion.div>

                <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-purple-700 transition-colors duration-300">
                  {rec.title}
                </h3>
                <p className="text-gray-600 mb-2">{rec.business}</p>
                <div className="flex items-center justify-between mb-4">
                  <span className="text-2xl font-bold text-green-600">{rec.discount}</span>
                  <span className="text-sm text-gray-500">{rec.reason}</span>
                </div>
                
                <motion.button
                  className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white py-3 rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  View Deal
                </motion.button>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default SmartRecommendations;
