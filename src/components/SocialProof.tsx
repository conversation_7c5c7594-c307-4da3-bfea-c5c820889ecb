import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, DollarSign, Users, TrendingUp, MapPin, Clock } from 'lucide-react';

const SocialProof: React.FC = () => {
  const [currentActivity, setCurrentActivity] = useState(0);

  const recentActivities = [
    { user: "Sarah M.", action: "earned $25", location: "San Francisco", time: "2 min ago", type: "earning" },
    { user: "Coffee Corner", action: "got 5 new customers", location: "Austin", time: "5 min ago", type: "business" },
    { user: "<PERSON>", action: "shared Yoga Studio deal", location: "Seattle", time: "7 min ago", type: "sharing" },
    { user: "Local Bistro", action: "hit 100 referrals", location: "Portland", time: "12 min ago", type: "milestone" },
    { user: "Jessica L.", action: "earned $18", location: "Denver", time: "15 min ago", type: "earning" },
    { user: "Fitness Hub", action: "launched new campaign", location: "Chicago", time: "18 min ago", type: "business" }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentActivity((prev) => (prev + 1) % recentActivities.length);
    }, 3000);
    return () => clearInterval(timer);
  }, []);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'earning': return <DollarSign className="w-4 h-4 text-green-500" />;
      case 'business': return <TrendingUp className="w-4 h-4 text-blue-500" />;
      case 'sharing': return <Users className="w-4 h-4 text-purple-500" />;
      case 'milestone': return <CheckCircle className="w-4 h-4 text-orange-500" />;
      default: return <CheckCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'earning': return 'border-green-200 bg-green-50';
      case 'business': return 'border-blue-200 bg-blue-50';
      case 'sharing': return 'border-purple-200 bg-purple-50';
      case 'milestone': return 'border-orange-200 bg-orange-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <section className="py-16 bg-white relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm font-semibold mb-4">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            Live Activity
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Real People, Real Results
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            See what's happening on Referit right now across the country
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8 items-center">
          {/* Live Activity Feed */}
          <div className="lg:col-span-2">
            <div className="bg-gray-50 rounded-2xl p-6 h-80 relative overflow-hidden">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-bold text-gray-900">Live Activity Feed</h3>
                <div className="flex items-center text-sm text-gray-500">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  Live
                </div>
              </div>

              <div className="space-y-3">
                <motion.div
                  key={currentActivity}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  {recentActivities.slice(currentActivity, currentActivity + 4).map((activity, index) => (
                    <div
                      key={`${currentActivity}-${index}`}
                      className={`flex items-center p-4 rounded-xl border ${getActivityColor(activity.type)} transition-all duration-300`}
                    >
                      <div className="mr-3">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1">
                        <div className="font-semibold text-gray-900 text-sm">
                          {activity.user} {activity.action}
                        </div>
                        <div className="flex items-center text-xs text-gray-500 mt-1">
                          <MapPin className="w-3 h-3 mr-1" />
                          {activity.location}
                          <Clock className="w-3 h-3 ml-3 mr-1" />
                          {activity.time}
                        </div>
                      </div>
                    </div>
                  ))}
                </motion.div>
              </div>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="space-y-6">
            <motion.div
              className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <div className="text-3xl font-bold text-blue-600 mb-2">25,000+</div>
                <div className="text-gray-600">Active Users</div>
                <div className="text-sm text-green-600 mt-2">+500 this week</div>
              </div>
            </motion.div>

            <motion.div
              className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <div className="text-center">
                <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <DollarSign className="w-6 h-6 text-white" />
                </div>
                <div className="text-3xl font-bold text-green-600 mb-2">$1.2M+</div>
                <div className="text-gray-600">Total Earned</div>
                <div className="text-sm text-green-600 mt-2">$12K today</div>
              </div>
            </motion.div>

            <motion.div
              className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-200"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <div className="text-3xl font-bold text-purple-600 mb-2">94%</div>
                <div className="text-gray-600">Success Rate</div>
                <div className="text-sm text-green-600 mt-2">Industry leading</div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Trust Indicators */}
        <motion.div
          className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-6 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
        >
          {[
            { metric: "99.9%", label: "Uptime" },
            { metric: "24/7", label: "Support" },
            { metric: "150+", label: "Cities" },
            { metric: "5★", label: "Rating" }
          ].map((item, index) => (
            <motion.div
              key={index}
              className="p-4"
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-2xl font-bold text-gray-900 mb-1">{item.metric}</div>
              <div className="text-gray-600 text-sm">{item.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default SocialProof;