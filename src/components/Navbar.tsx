import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, useScroll, useMotionValueEvent, AnimatePresence } from 'framer-motion';
import { Menu, X, ArrowRight, Sparkles, ChevronDown, Plus, Settings, DollarSign } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const Navbar: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isScrolled, setIsScrolled] = useState(false);
  const [businessProfile, setBusinessProfile] = useState<any>(null);
  const { user, logout } = useAuth();

  const location = useLocation();
  const [focusedItem, setFocusedItem] = useState<number | null>(null);
  const { scrollY } = useScroll();

  const navItems = [
    { 
      path: '/', 
      label: 'Home',
      description: 'Start your journey'
    },
    { 
      path: '/customers', 
      label: 'Earn Money',
      description: 'Share deals, earn rewards'
    },
    { 
      path: '/business', 
      label: 'Grow Business',
      description: 'Acquire customers risk-free'
    },
    { 
      path: '/contact', 
      label: 'Contact',
      description: 'Get in touch'
    },
  ];

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isMenuOpen) return;

      if (e.key === 'Escape') {
        closeMenu();
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        setFocusedItem(prev => prev === null ? 0 : Math.min(prev + 1, navItems.length - 1));
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setFocusedItem(prev => prev === null ? navItems.length - 1 : Math.max(prev - 1, 0));
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isMenuOpen, navItems.length]);

  useMotionValueEvent(scrollY, "change", (latest) => {
    setIsScrolled(latest > 50);
  });

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    const handleProfileUpdate = (event: CustomEvent) => {
      setBusinessProfile(event.detail);
    };

    // Load existing profile from localStorage
    const savedProfile = localStorage.getItem('businessProfile');
    if (savedProfile) {
      setBusinessProfile(JSON.parse(savedProfile));
    }

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('businessProfileUpdated', handleProfileUpdate as EventListener);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('businessProfileUpdated', handleProfileUpdate as EventListener);
    };
  }, []);

  return (
    <>
      <motion.nav 
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
          isScrolled 
            ? 'bg-white/80 backdrop-blur-xl border-b border-gray-200/50 shadow-lg shadow-black/5' 
            : 'bg-white/70 backdrop-blur-md border-b border-white/20'
        }`}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.8, ease: [0.21, 0.47, 0.32, 0.98] }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            {/* Enhanced Logo */}
            <motion.div 
              className="flex-shrink-0 relative"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <Link 
                to="/" 
                className="relative group flex items-center space-x-2"
                onClick={closeMenu}
              >
                {/* Logo icon */}
                <motion.div 
                  className="w-10 h-10 rounded-xl bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 flex items-center justify-center shadow-lg"
                  whileHover={{ rotate: [0, -5, 5, 0] }}
                  transition={{ duration: 0.5 }}
                >
                  <Sparkles className="w-5 h-5 text-white" />
                </motion.div>

                {/* Logo text */}
                <motion.span 
                  className="text-2xl lg:text-3xl font-black tracking-tight bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-700 bg-clip-text text-transparent"
                  whileHover={{
                    backgroundImage: "linear-gradient(to right, #4f46e5, #7c3aed, #4f46e5)",
                  }}
                >
                  Referit
                </motion.span>

                {/* Subtle glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/20 via-purple-600/20 to-pink-600/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>
              </Link>
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-1">
              {/* Check if user is logged in as business */}
              {location.pathname.includes('/dashboard') ? (
                // Business Navigation
                <>
                  {[
                    { path: '/dashboard#overview', label: 'Dashboard', description: 'Overview & insights' },
                    { path: '/dashboard#campaigns', label: 'Campaigns', description: 'Manage campaigns' },
                    { path: '/dashboard#customers', label: 'Customers', description: 'Customer insights' },
                    { path: '/dashboard#analytics', label: 'Analytics', description: 'Performance data' }
                  ].map((item, index) => (
                    <motion.div
                      key={item.path}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 + index * 0.1, duration: 0.6 }}
                      className="relative group"
                    >
                      <Link
                        to={item.path}
                        className={`relative px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 group ${
                          location.pathname === item.path
                            ? 'text-indigo-700 bg-indigo-50 shadow-sm'
                            : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'
                        }`}
                        onMouseEnter={() => setFocusedItem(index)}
                        onMouseLeave={() => setFocusedItem(null)}
                      >
                        <span className="relative z-10">{item.label}</span>

                        {/* Active indicator */}
                        {location.pathname === item.path && (
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-xl -z-10"
                            layoutId="activeTab"
                            transition={{ type: "spring", bounce: 0.25, duration: 0.6 }}
                          />
                        )}

                        {/* Enhanced hover effect */}
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"
                        />
                      </Link>

                      {/* Tooltip */}
                      <AnimatePresence>
                        {focusedItem === index && location.pathname !== item.path && (
                          <motion.div
                            initial={{ opacity: 0, y: 10, scale: 0.95 }}
                            animate={{ opacity: 1, y: 0, scale: 1 }}
                            exit={{ opacity: 0, y: 10, scale: 0.95 }}
                            transition={{ duration: 0.2 }}
                            className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg whitespace-nowrap z-50"
                          >
                            {item.description}
                            <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  ))}
                </>
              ) : (
                // Public Navigation
                <>
                  {navItems.map((item, index) => (
                    <motion.div
                      key={item.path}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 + index * 0.1, duration: 0.6 }}
                      className="relative group"
                    >
                      <Link
                        to={item.path}
                        className={`relative px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 group ${
                          location.pathname === item.path
                            ? 'text-indigo-700 bg-indigo-50 shadow-sm'
                            : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'
                        }`}
                        onMouseEnter={() => setFocusedItem(index)}
                        onMouseLeave={() => setFocusedItem(null)}
                      >
                        <span className="relative z-10">{item.label}</span>

                        {/* Active indicator */}
                        {location.pathname === item.path && (
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-xl -z-10"
                            layoutId="activeTab"
                            transition={{ type: "spring", bounce: 0.25, duration: 0.6 }}
                          />
                        )}

                        {/* Enhanced hover effect with subtle animation */}
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"
                        />

                        {/* Micro-interaction indicator */}
                        <motion.div
                          className="absolute -bottom-1 left-1/2 w-0 h-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full group-hover:w-1/2 transition-all duration-300"
                          style={{ transform: 'translateX(-50%)' }}
                        />
                      </Link>

                      {/* Tooltip on hover */}
                      <AnimatePresence>
                        {focusedItem === index && location.pathname !== item.path && (
                          <motion.div
                            initial={{ opacity: 0, y: 10, scale: 0.95 }}
                            animate={{ opacity: 1, y: 0, scale: 1 }}
                            exit={{ opacity: 0, y: 10, scale: 0.95 }}
                            transition={{ duration: 0.2 }}
                            className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg whitespace-nowrap z-50"
                          >
                            {item.description}
                            <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  ))}
                </>
              )}
            </div>

            {/* Desktop Auth Buttons */}
            <div className="hidden lg:flex items-center space-x-3">
              {location.pathname.includes('/dashboard') ? (
                // Business User Controls
                <>
                  {/* Create Campaign CTA */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5, duration: 0.6 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Link
                      to="/dashboard#campaigns"
                      className="group inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-xl font-bold text-sm transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-emerald-500/25 relative overflow-hidden"
                    >
                      <Plus className="w-4 h-4 mr-2 relative z-10" />
                      <span className="relative z-10">Create Campaign</span>

                      {/* Animated background */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-emerald-700 to-teal-700"
                        initial={{ x: "-100%" }}
                        whileHover={{ x: "0%" }}
                        transition={{ duration: 0.3 }}
                      />
                    </Link>
                  </motion.div>

                  {/* Profile Menu */}
              <div className="relative group">
                <motion.button
                  className="flex items-center space-x-3 p-2 rounded-xl bg-gray-50 hover:bg-gray-100 transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center text-white font-semibold shadow-lg overflow-hidden">
                    {(businessProfile?.logoUrl || user?.avatar) ? (
                      <img 
                        src={businessProfile?.logoUrl || user?.avatar} 
                        alt={businessProfile?.businessName || user?.name} 
                        className="w-10 h-10 rounded-full object-cover" 
                      />
                    ) : (
                      (businessProfile?.businessName?.charAt(0) || user?.name?.charAt(0) || user?.email?.charAt(0))?.toUpperCase()
                    )}
                  </div>
                  <div className="hidden md:block">
                    <div className="text-sm font-semibold text-gray-900">
                      {businessProfile?.businessName || user?.name || user?.full_name || 'User'}
                    </div>
                    <div className="text-xs text-gray-500 capitalize">
                      {user?.accountType || user?.user_type} Account
                      {businessProfile?.businessCategory && (
                        <span className="ml-1">• {businessProfile.businessCategory.replace('-', ' ')}</span>
                      )}
                    </div>
                  </div>
                </div>
                </motion.button>

                {/* Dropdown Menu */}
                <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-2xl shadow-2xl border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                  <div className="p-4 border-b border-gray-100">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                        JS
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900">John Smith</div>
                        <div className="text-sm text-gray-500"><EMAIL></div>
                        <div className="text-xs text-indigo-600 bg-indigo-50 px-2 py-1 rounded-full inline-block mt-1">
                          Business Premium
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="p-2">
                    {[
                      { icon: Settings, label: 'My Profile', action: () => window.location.href = '/dashboard#profile' },
                      { icon: Settings, label: 'Account Settings', action: () => {
                        window.dispatchEvent(new CustomEvent('dashboardTabChange', { detail: 'settings' }));
                        window.location.href = '/dashboard#settings';
                      }},
                      { icon: Settings, label: 'Notifications', action: () => window.location.href = '/dashboard#notifications' },
                      { icon: ArrowRight, label: 'Sign Out', action: () => {
                        localStorage.removeItem('user_data');
                        localStorage.removeItem('auth_token');
                        window.location.href = '/';
                      }, className: 'text-red-600 hover:bg-red-50' }
                    ].map((item, index) => (
                      <button
                        key={index}
                        onClick={item.action}
                        className={`flex items-center space-x-3 w-full px-4 py-3 text-left rounded-xl hover:bg-gray-50 transition-colors text-sm ${item.className || 'text-gray-700 hover:text-gray-900'}`}
                      >
                        {/* @ts-expect-error */}
                        <item.icon className="w-4 h-4" />
                        <span>{item.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
                </>
              ) : (
                // Public Auth Buttons
                <>
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5, duration: 0.6 }}
                  >
                    <Link
                      to="/login"
                      className="px-4 py-2.5 text-gray-700 hover:text-indigo-600 font-semibold text-sm transition-all duration-300 rounded-xl hover:bg-gray-50"
                    >
                      Log In
                    </Link>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6, duration: 0.6 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Link
                      to="/signup"
                      className="group inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-semibold text-sm transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-indigo-500/25 relative overflow-hidden"
                    >
                      <span className="relative z-10">Get Started</span>
                      <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300 relative z-10" />

                      {/* Animated background */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-indigo-700 to-purple-700"
                        initial={{ x: "-100%" }}
                        whileHover={{ x: "0%" }}
                        transition={{ duration: 0.3 }}
                      />
                    </Link>
                  </motion.div>
                </>
              )}
            </div>

            {/* Enhanced Mobile menu button */}
            <div className="lg:hidden">
              <motion.button
                onClick={toggleMenu}
                className="p-2 rounded-xl text-gray-700 hover:text-indigo-600 hover:bg-gray-50 transition-all duration-300 relative focus:outline-none focus:ring-2 focus:ring-indigo-500"
                whileTap={{ scale: 0.95 }}
                whileHover={{ scale: 1.05 }}
                aria-label={isMenuOpen ? "Close menu" : "Open menu"}
                aria-expanded={isMenuOpen}
              >
                <AnimatePresence>
                  {isMenuOpen ? (
                    <motion.div
                      key="close"
                      initial={{ rotate: -90, opacity: 0 }}
                      animate={{ rotate: 0, opacity: 1 }}
                      exit={{ rotate: 90, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <X className="h-6 w-6" />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="menu"
                      initial={{ rotate: 90, opacity: 0 }}
                      animate={{ rotate: 0, opacity: 1 }}
                      exit={{ rotate: -90, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Menu className="h-6 w-6" />
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Ripple effect on click */}
                <motion.div
                  className="absolute inset-0 bg-indigo-200 rounded-xl opacity-0"
                  animate={isMenuOpen ? { opacity: [0, 0.3, 0] } : {}}
                  transition={{ duration: 0.3 }}
                />
              </motion.button>
            </div>
          </div>
        </div>

        {/* Enhanced Mobile Navigation Menu */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              className="lg:hidden absolute top-full left-0 right-0 bg-white/95 backdrop-blur-xl border-b border-gray-200/50 shadow-2xl"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: [0.21, 0.47, 0.32, 0.98] }}
            >
              <div className="px-4 py-6 space-y-1">
                {navItems.map((item, index) => (
                  <motion.div
                    key={item.path}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.4 }}
                  >
                    <Link
                      to={item.path}
                      onClick={closeMenu}
                      className={`group block px-4 py-4 rounded-xl transition-all duration-300 ${
                        location.pathname === item.path
                          ? 'text-indigo-700 bg-indigo-50 shadow-sm'
                          : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-semibold">{item.label}</div>
                          <div className="text-sm text-gray-500 group-hover:text-indigo-500 transition-colors duration-300">
                            {item.description}
                          </div>
                        </div>
                        <motion.div
                          className="text-gray-400 group-hover:text-indigo-600 transition-colors duration-300"
                          whileHover={{ x: 5 }}
                        >
                          <ArrowRight className="w-4 h-4" />
                        </motion.div>
                      </div>
                    </Link>
                  </motion.div>
                ))}

                {/* Enhanced Mobile Auth Buttons */}
                <div className="pt-6 border-t border-gray-200/50 space-y-3">
                  {location.pathname.includes('/dashboard') ? (
                    // Business Mobile Menu
                    <>
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4, duration: 0.4 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Link
                          to="/dashboard/campaigns"
                          onClick={closeMenu}
                          className="relative overflow-hidden block mx-4 bg-gradient-to-r from-emerald-600 to-teal-600 text-white px-6 py-4 rounded-xl font-bold transition-all duration-300 text-center shadow-lg hover:shadow-xl group"
                        >
                          <span className="relative z-10 flex items-center justify-center">
                            <Plus className="mr-2 w-5 h-5" />
                            Create Campaign
                          </span>
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-emerald-700 to-teal-700"
                            initial={{ x: "-100%" }}
                            whileHover={{ x: "0%" }}
                            transition={{ duration: 0.3 }}
                          />
                        </Link>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5, duration: 0.4 }}
                      >
                        <div className="flex items-center px-4 py-3 bg-gray-50 rounded-xl mx-4">
                          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-indigo-600 to-purple-600 flex items-center justify-center text-white font-bold text-sm mr-3">
                            JS
                          </div>
                          <div className="flex-1">
                            <div className="text-sm font-semibold text-gray-900">John Smith</div>
                            <div className="text-xs text-gray-500">Business Account</div>
                          </div>
                        </div>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6, duration: 0.4 }}
                      >
                        <button
                          onClick={closeMenu}
                          className="block w-full px-4 py-3 text-gray-700 hover:text-red-600 hover:bg-red-50 font-semibold transition-all duration-300 rounded-xl text-center border border-gray-200 hover:border-red-200 mx-4"
                        >
                          Sign Out
                        </button>
                      </motion.div>
                    </>
                  ) : (
                    // Public Mobile Menu
                    <>
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4, duration: 0.4 }}
                      >
                        <Link
                          to="/login"
                          onClick={closeMenu}
                          className="block px-4 py-3 text-gray-700 hover:text-indigo-600 hover:bg-gray-50 font-semibold transition-all duration-300 rounded-xl text-center border border-gray-200 hover:border-indigo-200"
                        >
                          Log In
                        </Link>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5, duration: 0.4 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Link
                          to="/signup"
                          onClick={closeMenu}
                          className="relative overflow-hidden block mx-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 text-center shadow-lg hover:shadow-xl group"
                        >
                          <span className="relative z-10 flex items-center justify-center">
                            Get Started Free
                            <Sparkles className="ml-2 w-4 h-4" />
                          </span>
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-indigo-700 to-purple-700"
                            initial={{ x: "-100%" }}
                            whileHover={{ x: "0%" }}
                            transition={{ duration: 0.3 }}
                          />
                        </Link>
                      </motion.div>
                    </>
                  )}
                </div>

                {/* Quick stats in mobile menu */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.4 }}
                  className="pt-4 text-center"
                >
                  <div className="text-xs text-gray-500">
                    Join 10,000+ users earning through referrals
                  </div>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.nav>

      {/* Spacer to prevent content from going under fixed navbar */}
      <div className="h-20"></div>
    </>
  );
};

export default Navbar;