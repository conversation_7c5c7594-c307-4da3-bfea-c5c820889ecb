import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Sparkles, User, Building, Target, CheckCircle, ArrowRight, ArrowLeft } from 'lucide-react';

interface OnboardingStep {
  id: number;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  action: string;
}

const ProgressiveOnboarding: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [userType, setUserType] = useState<'customer' | 'business' | null>(null);

  const steps: OnboardingStep[] = [
    {
      id: 1,
      title: 'Welcome to Referit',
      description: 'Choose your journey to start earning through referrals',
      icon: Sparkles,
      color: 'from-purple-500 to-indigo-600',
      action: 'Choose Account Type'
    },
    {
      id: 2,
      title: userType === 'customer' ? 'Discover & Save' : 'Create & Grow',
      description: userType === 'customer' 
        ? 'Find amazing deals and share them with friends to earn rewards'
        : 'Create compelling vouchers and watch your customer base expand',
      icon: userType === 'customer' ? User : Building,
      color: userType === 'customer' ? 'from-blue-500 to-cyan-600' : 'from-green-500 to-emerald-600',
      action: 'Set Up Profile'
    },
    {
      id: 3,
      title: 'Start Referring',
      description: 'Begin your referral journey and watch your network grow',
      icon: Target,
      color: 'from-orange-500 to-red-500',
      action: 'Get Started'
    }
  ];

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const selectUserType = (type: 'customer' | 'business') => {
    setUserType(type);
    nextStep();
  };

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-purple-300 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-blue-300 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-4xl mx-auto px-4 relative z-10">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Smart Onboarding Experience
          </h2>
          <p className="text-xl text-gray-600">
            Personalized guidance that adapts to your goals and preferences
          </p>
        </motion.div>

        {/* Progress Indicator */}
        <div className="flex justify-center mb-12">
          <div className="flex items-center space-x-4">
            {steps.map((_, index) => (
              <React.Fragment key={index}>
                <motion.div
                  className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    index <= currentStep 
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white' 
                      : 'bg-gray-200 text-gray-400'
                  }`}
                  initial={{ scale: 0.8 }}
                  animate={{ scale: index === currentStep ? 1.1 : 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {index < currentStep ? (
                    <CheckCircle className="w-6 h-6" />
                  ) : (
                    <span className="font-bold">{index + 1}</span>
                  )}
                </motion.div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-1 rounded ${
                    index < currentStep ? 'bg-gradient-to-r from-blue-500 to-purple-600' : 'bg-gray-200'
                  }`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            className="bg-white rounded-3xl p-8 md:p-12 shadow-2xl max-w-2xl mx-auto"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5, ease: [0.22, 1, 0.36, 1] }}
          >
            {currentStep < steps.length && (
              <>
                <motion.div 
                  className={`w-20 h-20 mx-auto mb-8 bg-gradient-to-br ${steps[currentStep].color} rounded-3xl flex items-center justify-center shadow-lg`}
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.6, type: "spring", stiffness: 200 }}
                >
                  {React.createElement(steps[currentStep].icon, { className: "w-10 h-10 text-white" })}
                </motion.div>

                <motion.h3 
                  className="text-3xl font-bold text-gray-900 mb-6 text-center"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.6 }}
                >
                  {steps[currentStep].title}
                </motion.h3>

                <motion.p 
                  className="text-xl text-gray-600 mb-8 text-center leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.6 }}
                >
                  {steps[currentStep].description}
                </motion.p>

                {/* Step-specific content */}
                {currentStep === 0 && (
                  <motion.div 
                    className="grid md:grid-cols-2 gap-6 mb-8"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4, duration: 0.6 }}
                  >
                    <motion.button
                      onClick={() => selectUserType('customer')}
                      className="p-6 border-2 border-blue-200 rounded-2xl hover:border-blue-400 hover:bg-blue-50 transition-all duration-300 group"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <User className="w-12 h-12 text-blue-600 mx-auto mb-4 group-hover:scale-110 transition-transform" />
                      <h4 className="text-xl font-bold text-gray-900 mb-2">Customer</h4>
                      <p className="text-gray-600">Find deals, share, and earn rewards</p>
                    </motion.button>

                    <motion.button
                      onClick={() => selectUserType('business')}
                      className="p-6 border-2 border-green-200 rounded-2xl hover:border-green-400 hover:bg-green-50 transition-all duration-300 group"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Building className="w-12 h-12 text-green-600 mx-auto mb-4 group-hover:scale-110 transition-transform" />
                      <h4 className="text-xl font-bold text-gray-900 mb-2">Business</h4>
                      <p className="text-gray-600">Create vouchers and grow your reach</p>
                    </motion.button>
                  </motion.div>
                )}

                {/* Navigation */}
                <motion.div 
                  className="flex justify-between items-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.6 }}
                >
                  <motion.button
                    onClick={prevStep}
                    disabled={currentStep === 0}
                    className={`flex items-center px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                      currentStep === 0
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                    whileHover={currentStep > 0 ? { scale: 1.05 } : {}}
                    whileTap={currentStep > 0 ? { scale: 0.95 } : {}}
                  >
                    <ArrowLeft className="w-5 h-5 mr-2" />
                    Back
                  </motion.button>

                  {currentStep > 0 && (
                    <motion.button
                      onClick={nextStep}
                      className="flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {steps[currentStep].action}
                      <ArrowRight className="w-5 h-5 ml-2" />
                    </motion.button>
                  )}
                </motion.div>
              </>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </section>
  );
};

export default ProgressiveOnboarding;