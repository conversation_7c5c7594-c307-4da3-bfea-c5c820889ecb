
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Brain,
  TrendingUp,
  Users,
  Target,
  Zap,
  Gift,
  Mail,
  Clock,
  Star,
  AlertTriangle,
  CheckCircle,
  ArrowRight
} from 'lucide-react';

interface CustomerRecommendation {
  id: string;
  type: 'engagement' | 'retention' | 'growth' | 'reward';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  customerCount: number;
  expectedImpact: string;
  actionText: string;
  icon: React.ComponentType<any>;
  color: string;
  customers?: string[];
}

interface SmartCustomerRecommendationsProps {
  customers: any[];
  onAction: (recommendation: CustomerRecommendation) => void;
}

const SmartCustomerRecommendations: React.FC<SmartCustomerRecommendationsProps> = ({ 
  customers, 
  onAction 
}) => {
  const [recommendations, setRecommendations] = useState<CustomerRecommendation[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    generateRecommendations();
  }, [customers]);

  const generateRecommendations = () => {
    setLoading(true);
    
    // Simulate AI processing delay
    setTimeout(() => {
      const recs: CustomerRecommendation[] = [];

      // Analyze customer data and generate recommendations
      const inactiveCustomers = customers.filter(c => {
        const lastActive = new Date(c.lastActivity);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return lastActive < weekAgo;
      });

      const highValueCustomers = customers.filter(c => c.totalEarnings > 50);
      const newCustomers = customers.filter(c => {
        const joinDate = new Date(c.joinedDate);
        const monthAgo = new Date();
        monthAgo.setMonth(monthAgo.getMonth() - 1);
        return joinDate > monthAgo;
      });

      const lowPerformers = customers.filter(c => c.totalReferrals === 0 && c.status === 'active');

      // Re-engagement recommendation
      if (inactiveCustomers.length > 0) {
        recs.push({
          id: 'reengagement',
          type: 'retention',
          priority: 'high',
          title: 'Re-engage Inactive Customers',
          description: `${inactiveCustomers.length} customers haven't been active in over a week. Send them a special offer to bring them back.`,
          customerCount: inactiveCustomers.length,
          expectedImpact: '15-25% reactivation rate',
          actionText: 'Send Re-engagement Campaign',
          icon: Clock,
          color: 'from-orange-500 to-red-500',
          customers: inactiveCustomers.map(c => c.id)
        });
      }

      // High-value customer rewards
      if (highValueCustomers.length > 0) {
        recs.push({
          id: 'highvalue',
          type: 'reward',
          priority: 'high',
          title: 'Reward High-Value Customers',
          description: `${highValueCustomers.length} customers have earned significant rewards. Send them exclusive perks to maintain loyalty.`,
          customerCount: highValueCustomers.length,
          expectedImpact: '30% increase in referrals',
          actionText: 'Send VIP Rewards',
          icon: Star,
          color: 'from-yellow-500 to-orange-500',
          customers: highValueCustomers.map(c => c.id)
        });
      }

      // New customer onboarding
      if (newCustomers.length > 0) {
        recs.push({
          id: 'newcustomer',
          type: 'engagement',
          priority: 'medium',
          title: 'Onboard New Customers',
          description: `${newCustomers.length} new customers joined recently. Help them get started with a welcome guide and first referral bonus.`,
          customerCount: newCustomers.length,
          expectedImpact: '40% faster first referral',
          actionText: 'Send Welcome Series',
          icon: Users,
          color: 'from-green-500 to-emerald-500',
          customers: newCustomers.map(c => c.id)
        });
      }

      // Low performer activation
      if (lowPerformers.length > 0) {
        recs.push({
          id: 'lowperformer',
          type: 'growth',
          priority: 'medium',
          title: 'Activate Low Performers',
          description: `${lowPerformers.length} active customers haven't made any referrals yet. Provide them with easy sharing tools and incentives.`,
          customerCount: lowPerformers.length,
          expectedImpact: '20% conversion to referrers',
          actionText: 'Send Activation Guide',
          icon: TrendingUp,
          color: 'from-blue-500 to-indigo-500',
          customers: lowPerformers.map(c => c.id)
        });
      }

      // Segment-based targeting
      const vipCustomers = customers.filter(c => c.tags?.includes('vip'));
      if (vipCustomers.length > 2) {
        recs.push({
          id: 'vipcampaign',
          type: 'engagement',
          priority: 'low',
          title: 'VIP Customer Campaign',
          description: `Create an exclusive campaign for your ${vipCustomers.length} VIP customers with premium rewards.`,
          customerCount: vipCustomers.length,
          expectedImpact: '50% higher engagement',
          actionText: 'Create VIP Campaign',
          icon: Gift,
          color: 'from-purple-500 to-pink-500',
          customers: vipCustomers.map(c => c.id)
        });
      }

      setRecommendations(recs);
      setLoading(false);
    }, 1500);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-200 bg-red-50';
      case 'medium': return 'border-yellow-200 bg-yellow-50';
      case 'low': return 'border-blue-200 bg-blue-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-700';
      case 'medium': return 'bg-yellow-100 text-yellow-700';
      case 'low': return 'bg-blue-100 text-blue-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center mb-4">
          <Brain className="w-6 h-6 text-indigo-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">AI Recommendations</h3>
        </div>
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="border border-gray-200 rounded-lg p-4">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded mb-2 w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (recommendations.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center mb-4">
          <Brain className="w-6 h-6 text-indigo-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">AI Recommendations</h3>
        </div>
        <div className="text-center py-8">
          <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">All Good!</h4>
          <p className="text-gray-600">Your customer engagement is optimized. Check back later for new recommendations.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mr-3">
              <Brain className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Smart Recommendations</h3>
              <p className="text-sm text-gray-600">AI-powered insights for your customer base</p>
            </div>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Zap className="w-4 h-4" />
            <span>Powered by AI</span>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-4">
        {recommendations.map((rec, index) => {
          const IconComponent = rec.icon;
          return (
            <motion.div
              key={rec.id}
              className={`border-2 rounded-xl p-4 transition-all duration-300 hover:shadow-md ${getPriorityColor(rec.priority)}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.01 }}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  <div className={`w-10 h-10 rounded-xl bg-gradient-to-r ${rec.color} flex items-center justify-center shadow-lg`}>
                    <IconComponent className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-semibold text-gray-900">{rec.title}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityBadge(rec.priority)}`}>
                        {rec.priority} priority
                      </span>
                    </div>
                    <p className="text-gray-600 mb-3">{rec.description}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-1" />
                        <span>{rec.customerCount} customers</span>
                      </div>
                      <div className="flex items-center">
                        <Target className="w-4 h-4 mr-1" />
                        <span>{rec.expectedImpact}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <motion.button
                  onClick={() => onAction(rec)}
                  className={`bg-gradient-to-r ${rec.color} text-white px-4 py-2 rounded-lg font-medium text-sm hover:shadow-lg transition-all flex items-center`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {rec.actionText}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </motion.button>
              </div>
            </motion.div>
          );
        })}
      </div>

      <div className="p-4 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>Recommendations update automatically based on customer behavior</span>
          <button
            onClick={generateRecommendations}
            className="text-indigo-600 hover:text-indigo-700 font-medium"
          >
            Refresh
          </button>
        </div>
      </div>
    </div>
  );
};

export default SmartCustomerRecommendations;
