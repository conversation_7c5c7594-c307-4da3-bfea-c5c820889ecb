
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  Users,
  Calendar,
  MapPin,
  Star,
  DollarSign,
  Target,
  BarChart3,
  PieChart,
  Activity,
  Award,
  Zap,
  Clock,
  Gift,
  Share2
} from 'lucide-react';

interface CustomerInsight {
  topPerformers: Array<{
    name: string;
    email: string;
    totalReferrals: number;
    totalEarnings: number;
    lastActive: string;
  }>;
  customerSegments: Array<{
    segment: string;
    count: number;
    percentage: number;
    avgValue: number;
  }>;
  activityMetrics: {
    newCustomersThisMonth: number;
    activeCustomers: number;
    averageEngagement: number;
    topTags: Array<{ tag: string; count: number }>;
  };
  geographicData: Array<{
    location: string;
    customers: number;
    revenue: number;
  }>;
}

interface CustomerInsightsProps {
  customers: any[];
}

const CustomerInsights: React.FC<CustomerInsightsProps> = ({ customers }) => {
  const [insights, setInsights] = useState<CustomerInsight | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'segments' | 'performance' | 'geography'>('overview');

  useEffect(() => {
    generateInsights();
  }, [customers]);

  const generateInsights = () => {
    if (!customers.length) return;

    // Top performers
    const topPerformers = customers
      .sort((a, b) => b.totalReferrals - a.totalReferrals)
      .slice(0, 5)
      .map(customer => ({
        name: customer.name,
        email: customer.email,
        totalReferrals: customer.totalReferrals,
        totalEarnings: customer.totalEarnings,
        lastActive: customer.lastActivity
      }));

    // Customer segments based on activity
    const activeCustomers = customers.filter(c => c.status === 'active');
    const inactiveCustomers = customers.filter(c => c.status === 'inactive');
    const highValueCustomers = customers.filter(c => c.totalEarnings > 100);
    const newCustomers = customers.filter(c => {
      const joinDate = new Date(c.joinedDate);
      const monthAgo = new Date();
      monthAgo.setMonth(monthAgo.getMonth() - 1);
      return joinDate > monthAgo;
    });

    const segments = [
      {
        segment: 'Active Members',
        count: activeCustomers.length,
        percentage: (activeCustomers.length / customers.length) * 100,
        avgValue: activeCustomers.reduce((sum, c) => sum + c.totalEarnings, 0) / activeCustomers.length || 0
      },
      {
        segment: 'High Value',
        count: highValueCustomers.length,
        percentage: (highValueCustomers.length / customers.length) * 100,
        avgValue: highValueCustomers.reduce((sum, c) => sum + c.totalEarnings, 0) / highValueCustomers.length || 0
      },
      {
        segment: 'New This Month',
        count: newCustomers.length,
        percentage: (newCustomers.length / customers.length) * 100,
        avgValue: newCustomers.reduce((sum, c) => sum + c.totalEarnings, 0) / newCustomers.length || 0
      },
      {
        segment: 'Inactive',
        count: inactiveCustomers.length,
        percentage: (inactiveCustomers.length / customers.length) * 100,
        avgValue: inactiveCustomers.reduce((sum, c) => sum + c.totalEarnings, 0) / inactiveCustomers.length || 0
      }
    ];

    // Tag analysis
    const tagCounts: Record<string, number> = {};
    customers.forEach(customer => {
      customer.tags?.forEach((tag: string) => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    });

    const topTags = Object.entries(tagCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([tag, count]) => ({ tag, count }));

    // Geographic data (mock for demo)
    const geographicData = [
      { location: 'Main Store', customers: Math.floor(customers.length * 0.4), revenue: 15420 },
      { location: 'Downtown Branch', customers: Math.floor(customers.length * 0.35), revenue: 12380 },
      { location: 'Mall Location', customers: Math.floor(customers.length * 0.15), revenue: 8750 },
      { location: 'Online', customers: Math.floor(customers.length * 0.1), revenue: 5280 }
    ];

    setInsights({
      topPerformers,
      customerSegments: segments,
      activityMetrics: {
        newCustomersThisMonth: newCustomers.length,
        activeCustomers: activeCustomers.length,
        averageEngagement: (activeCustomers.length / customers.length) * 100,
        topTags
      },
      geographicData
    });
  };

  if (!insights) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'segments', label: 'Segments', icon: PieChart },
    { id: 'performance', label: 'Top Performers', icon: Award },
    { id: 'geography', label: 'Geography', icon: MapPin }
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-xl font-bold text-gray-900">Customer Insights</h3>
            <p className="text-gray-600">Deep dive into your customer analytics</p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Activity className="w-4 h-4" />
            <span>Updated just now</span>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1">
          {tabs.map((tab) => {
            const TabIcon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-4 py-2 rounded-lg font-medium transition-all ${
                  activeTab === tab.id
                    ? 'bg-indigo-100 text-indigo-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <TabIcon className="w-4 h-4 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </div>

      <div className="p-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4">
                <div className="flex items-center justify-between mb-2">
                  <Users className="w-8 h-8 text-blue-600" />
                  <span className="text-sm text-green-600 font-medium">+{insights.activityMetrics.newCustomersThisMonth} this month</span>
                </div>
                <p className="text-2xl font-bold text-gray-900">{insights.activityMetrics.activeCustomers}</p>
                <p className="text-gray-600">Active Customers</p>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4">
                <div className="flex items-center justify-between mb-2">
                  <TrendingUp className="w-8 h-8 text-green-600" />
                  <span className="text-sm text-green-600 font-medium">{insights.activityMetrics.averageEngagement.toFixed(1)}%</span>
                </div>
                <p className="text-2xl font-bold text-gray-900">${customers.reduce((sum, c) => sum + c.totalEarnings, 0).toLocaleString()}</p>
                <p className="text-gray-600">Total Earnings Paid</p>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4">
                <div className="flex items-center justify-between mb-2">
                  <Share2 className="w-8 h-8 text-purple-600" />
                  <span className="text-sm text-green-600 font-medium">+12% vs last month</span>
                </div>
                <p className="text-2xl font-bold text-gray-900">{customers.reduce((sum, c) => sum + c.totalReferrals, 0)}</p>
                <p className="text-gray-600">Total Referrals</p>
              </div>
            </div>

            {/* Top Tags */}
            <div className="bg-gray-50 rounded-xl p-4">
              <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                <Target className="w-5 h-5 mr-2 text-purple-600" />
                Popular Customer Tags
              </h4>
              <div className="flex flex-wrap gap-2">
                {insights.activityMetrics.topTags.map((tag, index) => (
                  <div
                    key={tag.tag}
                    className="bg-white px-3 py-2 rounded-lg border border-gray-200 flex items-center space-x-2"
                  >
                    <span className="text-gray-700 font-medium">{tag.tag}</span>
                    <span className="bg-indigo-100 text-indigo-600 px-2 py-1 rounded-full text-xs font-medium">
                      {tag.count}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        )}

        {/* Segments Tab */}
        {activeTab === 'segments' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            {insights.customerSegments.map((segment, index) => (
              <div key={segment.segment} className="bg-gray-50 rounded-xl p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-gray-900">{segment.segment}</h4>
                  <span className="text-sm text-gray-500">{segment.count} customers</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <motion.div
                        className="bg-gradient-to-r from-indigo-500 to-purple-600 h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${segment.percentage}%` }}
                        transition={{ duration: 1, delay: index * 0.1 }}
                      />
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-gray-900">{segment.percentage.toFixed(1)}%</p>
                    <p className="text-sm text-gray-500">Avg: ${segment.avgValue.toFixed(0)}</p>
                  </div>
                </div>
              </div>
            ))}
          </motion.div>
        )}

        {/* Performance Tab */}
        {activeTab === 'performance' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-semibold text-gray-900">Top Performing Customers</h4>
              <span className="text-sm text-gray-500">Based on referral activity</span>
            </div>
            {insights.topPerformers.map((performer, index) => (
              <motion.div
                key={performer.email}
                className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 border border-gray-200"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                      #{index + 1}
                    </div>
                    <div>
                      <h5 className="font-semibold text-gray-900">{performer.name}</h5>
                      <p className="text-sm text-gray-600">{performer.email}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-4">
                      <div className="text-center">
                        <p className="text-lg font-bold text-gray-900">{performer.totalReferrals}</p>
                        <p className="text-xs text-gray-500">Referrals</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-bold text-green-600">${performer.totalEarnings}</p>
                        <p className="text-xs text-gray-500">Earned</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600">{new Date(performer.lastActive).toLocaleDateString()}</p>
                        <p className="text-xs text-gray-500">Last Active</p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Geography Tab */}
        {activeTab === 'geography' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-semibold text-gray-900">Customer Distribution by Location</h4>
              <span className="text-sm text-gray-500">Based on redemption locations</span>
            </div>
            {insights.geographicData.map((location, index) => (
              <div key={location.location} className="bg-gray-50 rounded-xl p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-gray-500" />
                    <h5 className="font-semibold text-gray-900">{location.location}</h5>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-gray-900">{location.customers} customers</p>
                    <p className="text-sm text-green-600">${location.revenue.toLocaleString()} revenue</p>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${(location.customers / customers.length) * 100}%` }}
                    transition={{ duration: 1, delay: index * 0.1 }}
                  />
                </div>
              </div>
            ))}
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default CustomerInsights;
