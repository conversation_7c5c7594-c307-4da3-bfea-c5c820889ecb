
import React, { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { 
  Zap, 
  Shield, 
  Globe, 
  Brain,
  Smartphone,
  ChartBar,
  Users,
  Target,
  Layers,
  Code,
  Palette,
  Settings,
  ArrowRight,
  CheckCircle,
  Sparkles
} from 'lucide-react';

const AdvancedFeatures: React.FC = () => {
  const [activeFeature, setActiveFeature] = useState(0);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  const features = [
    {
      id: 0,
      title: "AI-Powered Optimization",
      description: "Machine learning algorithms optimize your referral campaigns automatically for maximum conversion rates.",
      icon: Brain,
      gradient: "from-purple-500 to-indigo-600",
      benefits: ["Smart targeting", "Auto-optimization", "Predictive analytics"],
      stats: { improvement: "340%", accuracy: "98.5%", automation: "100%" }
    },
    {
      id: 1,
      title: "Global Infrastructure",
      description: "Worldwide CDN ensures lightning-fast performance and 99.99% uptime across all regions.",
      icon: Globe,
      gradient: "from-blue-500 to-cyan-600",
      benefits: ["Global CDN", "Edge computing", "Multi-region backup"],
      stats: { uptime: "99.99%", regions: "150+", latency: "<50ms" }
    },
    {
      id: 2,
      title: "Enterprise Security", 
      description: "Bank-level encryption, SOC2 compliance, and advanced fraud detection keep your data secure.",
      icon: Shield,
      gradient: "from-green-500 to-emerald-600",
      benefits: ["256-bit encryption", "SOC2 certified", "Fraud detection"],
      stats: { encryption: "256-bit", compliance: "SOC2", threats: "0" }
    },
    {
      id: 3,
      title: "Mobile-First Design",
      description: "Optimized for all devices with progressive web app capabilities and offline support.",
      icon: Smartphone,
      gradient: "from-pink-500 to-red-600",
      benefits: ["PWA support", "Offline mode", "Touch optimized"],
      stats: { mobile: "95%", pwa: "Enabled", offline: "Full" }
    }
  ];

  const integrations = [
    { name: "Shopify", logo: "🛍️", color: "bg-green-100 text-green-600" },
    { name: "WordPress", logo: "📝", color: "bg-blue-100 text-blue-600" },
    { name: "Stripe", logo: "💳", color: "bg-purple-100 text-purple-600" },
    { name: "Mailchimp", logo: "📧", color: "bg-yellow-100 text-yellow-600" },
    { name: "Salesforce", logo: "☁️", color: "bg-indigo-100 text-indigo-600" },
    { name: "Zapier", logo: "⚡", color: "bg-orange-100 text-orange-600" }
  ];

  return (
    <section ref={ref} className="py-32 bg-gradient-to-br from-slate-50 via-white to-blue-50 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 40 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold mb-6 bg-indigo-100 text-indigo-700"
          >
            <Zap className="w-4 h-4 mr-2" />
            Advanced Technology
          </motion.div>
          <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Built for the Future
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Cutting-edge technology meets intuitive design. Experience the most advanced referral platform ever created.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Feature Tabs */}
          <div className="space-y-4">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              const isActive = activeFeature === index;
              
              return (
                <motion.div
                  key={feature.id}
                  className={`relative p-6 rounded-2xl cursor-pointer transition-all duration-500 border-2 ${
                    isActive 
                      ? 'bg-white shadow-2xl border-indigo-200' 
                      : 'bg-white/50 hover:bg-white border-gray-100 hover:border-gray-200'
                  }`}
                  onClick={() => setActiveFeature(index)}
                  initial={{ opacity: 0, x: -50 }}
                  animate={isInView ? { opacity: 1, x: 0 } : {}}
                  transition={{ delay: index * 0.1, duration: 0.6 }}
                  whileHover={{ scale: 1.02, y: -5 }}
                >
                  {isActive && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-2xl"
                      layoutId="activeFeature"
                      transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    />
                  )}
                  
                  <div className="relative z-10 flex items-start space-x-4">
                    <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${feature.gradient} flex items-center justify-center flex-shrink-0 ${
                      isActive ? 'scale-110' : ''
                    } transition-transform duration-300`}>
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    
                    <div className="flex-1">
                      <h3 className={`text-xl font-bold mb-2 ${isActive ? 'text-indigo-600' : 'text-gray-900'}`}>
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        {feature.description}
                      </p>
                      
                      {/* Benefits */}
                      <div className="flex flex-wrap gap-2 mb-3">
                        {feature.benefits.map((benefit, idx) => (
                          <span 
                            key={idx}
                            className={`text-xs px-3 py-1 rounded-full ${
                              isActive 
                                ? 'bg-indigo-100 text-indigo-700' 
                                : 'bg-gray-100 text-gray-600'
                            }`}
                          >
                            {benefit}
                          </span>
                        ))}
                      </div>
                      
                      {/* Stats */}
                      {isActive && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="flex space-x-4 text-sm"
                        >
                          {Object.entries(feature.stats).map(([key, value]) => (
                            <div key={key} className="text-center">
                              <div className="font-bold text-indigo-600">{value}</div>
                              <div className="text-gray-500 capitalize">{key}</div>
                            </div>
                          ))}
                        </motion.div>
                      )}
                    </div>
                    
                    <ArrowRight className={`w-5 h-5 transition-all duration-300 ${
                      isActive ? 'text-indigo-600 rotate-90' : 'text-gray-400'
                    }`} />
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Feature Visualization */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ delay: 0.4, duration: 0.8 }}
          >
            <div className="bg-white rounded-3xl p-8 shadow-2xl border border-gray-200 relative overflow-hidden">
              {/* Animated background */}
              <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 to-purple-50 opacity-50"></div>
              
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-8">
                  <h3 className="text-2xl font-bold text-gray-900">
                    {features[activeFeature].title}
                  </h3>
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${features[activeFeature].gradient} flex items-center justify-center`}>
                    {React.createElement(features[activeFeature].icon, { className: "w-6 h-6 text-white" })}
                  </div>
                </div>

                {/* Feature Demo */}
                <div className="space-y-4">
                  {[1, 2, 3].map((item, index) => (
                    <motion.div
                      key={item}
                      className="flex items-center space-x-3 p-3 bg-white/50 rounded-lg"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6 + index * 0.1 }}
                    >
                      <CheckCircle className="w-5 h-5 text-green-500" />
                      <div className="flex-1">
                        <div className="h-2 bg-gray-200 rounded-full">
                          <motion.div
                            className={`h-2 bg-gradient-to-r ${features[activeFeature].gradient} rounded-full`}
                            initial={{ width: 0 }}
                            animate={{ width: `${85 + index * 5}%` }}
                            transition={{ delay: 0.8 + index * 0.1, duration: 1 }}
                          />
                        </div>
                      </div>
                      <span className="text-sm font-medium text-gray-600">
                        {85 + index * 5}%
                      </span>
                    </motion.div>
                  ))}
                </div>

                {/* Live metrics */}
                <div className="mt-8 p-4 bg-gray-50 rounded-xl">
                  <div className="text-sm text-gray-600 mb-2">Live Performance</div>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                      <span className="text-sm font-medium">Active</span>
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                      {Object.values(features[activeFeature].stats)[0]}
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating particles */}
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-indigo-400 rounded-full"
                  style={{
                    left: `${20 + (i % 3) * 30}%`,
                    top: `${20 + Math.floor(i / 3) * 40}%`,
                  }}
                  animate={{
                    y: [0, -10, 0],
                    opacity: [0.3, 1, 0.3],
                    scale: [0.8, 1.2, 0.8]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: i * 0.5
                  }}
                />
              ))}
            </div>
          </motion.div>
        </div>

        
      </div>
    </section>
  );
};

export default AdvancedFeatures;
