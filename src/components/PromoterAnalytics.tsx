import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  Users, 
  TrendingUp, 
  Target, 
  Calendar,
  Filter,
  Download,
  PieChart,
  Clock,
  DollarSign,
  Award,
  UserCheck
} from 'lucide-react';

const PromoterAnalytics: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [selectedMetric, setSelectedMetric] = useState<'referrals' | 'conversions' | 'revenue'>('referrals');
  const [hasPromoters, setHasPromoters] = useState(true); // Mock state, set to true to show content, false to show empty state

  // Mock analytics data
  const analyticsData = {
    overview: {
      totalPromoters: 156,
      activePromoters: 89,
      averageReferrals: 12.3,
      conversionRate: 62.7,
      totalRevenue: 45280,
      growth: {
        promoters: 18.5,
        referrals: 23.7,
        conversions: 15.2,
        revenue: 31.4
      }
    },
    chartData: [
      { date: '2024-01-01', referrals: 45, conversions: 28, revenue: 1400 },
      { date: '2024-01-02', referrals: 52, conversions: 33, revenue: 1650 },
      { date: '2024-01-03', referrals: 38, conversions: 24, revenue: 1200 },
      { date: '2024-01-04', referrals: 61, conversions: 39, revenue: 1950 },
      { date: '2024-01-05', referrals: 47, conversions: 30, revenue: 1500 },
      { date: '2024-01-06', referrals: 55, conversions: 35, revenue: 1750 },
      { date: '2024-01-07', referrals: 42, conversions: 26, revenue: 1300 }
    ],
    topSegments: [
      { segment: 'VIP Customers', count: 23, percentage: 25.8, color: 'purple' },
      { segment: 'Loyal Members', count: 19, percentage: 21.3, color: 'blue' },
      { segment: 'New Customers', count: 17, percentage: 19.1, color: 'green' },
      { segment: 'Social Media Active', count: 15, percentage: 16.9, color: 'pink' },
      { segment: 'Others', count: 15, percentage: 16.9, color: 'gray' }
    ]
  };

  if (!hasPromoters) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Promoter Analytics</h1>
            <p className="text-gray-600 mt-2">Deep insights into your referrer performance and trends</p>
          </div>
        </div>

        <motion.div
          className="bg-white rounded-3xl shadow-lg border border-gray-100 p-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="max-w-lg mx-auto">
            <div className="w-24 h-24 bg-gradient-to-r from-purple-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <BarChart3 className="w-12 h-12 text-purple-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Analytics Coming Soon!</h3>
            <p className="text-gray-600 mb-8 leading-relaxed">
              Once you have active promoters sharing your campaigns, you'll see detailed analytics here including 
              performance trends, conversion insights, and promoter segmentation data.
            </p>
            <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl p-6 mb-8">
              <h4 className="font-semibold text-gray-900 mb-4">What you'll discover:</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4 text-green-600" />
                  <span>Performance trends</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-blue-600" />
                  <span>Promoter segments</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Target className="w-4 h-4 text-purple-600" />
                  <span>Conversion insights</span>
                </div>
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4 text-green-600" />
                  <span>Revenue attribution</span>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <motion.button
                onClick={() => window.location.hash = 'campaigns'}
                className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-4 rounded-2xl font-bold hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 flex items-center mx-auto shadow-lg hover:shadow-xl"
                whileHover={{ y: -2, scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Target className="w-5 h-5 mr-2" />
                Create Campaign to Get Started
              </motion.button>
              <p className="text-sm text-gray-500">
                Launch campaigns and start gathering valuable analytics data
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Promoter Analytics</h1>
          <p className="text-gray-600 mt-2">Deep insights into your referrer performance and trends</p>
        </div>

        <div className="flex items-center space-x-4">
          {/* Timeframe Selector */}
          <div className="flex items-center space-x-2 bg-gray-100 rounded-xl p-1">
            {([
              { key: '7d', label: '7 Days' },
              { key: '30d', label: '30 Days' },
              { key: '90d', label: '90 Days' },
              { key: '1y', label: '1 Year' }
            ] as const).map((option) => (
              <button
                key={option.key}
                onClick={() => setSelectedTimeframe(option.key)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                  selectedTimeframe === option.key
                    ? 'bg-white text-indigo-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>

          <button className="flex items-center px-4 py-2 text-gray-600 border border-gray-300 rounded-xl hover:bg-gray-50">
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          { 
            icon: Users, 
            title: "Total Promoters", 
            value: analyticsData.overview.totalPromoters.toString(), 
            change: `+${analyticsData.overview.growth.promoters}%`, 
            color: "blue",
            subtitle: `${analyticsData.overview.activePromoters} active`
          },
          { 
            icon: TrendingUp, 
            title: "Avg Referrals", 
            value: analyticsData.overview.averageReferrals.toString(), 
            change: `+${analyticsData.overview.growth.referrals}%`, 
            color: "green",
            subtitle: "per promoter"
          },
          { 
            icon: Target, 
            title: "Conversion Rate", 
            value: `${analyticsData.overview.conversionRate}%`, 
            change: `+${analyticsData.overview.growth.conversions}%`, 
            color: "purple",
            subtitle: "referral success"
          },
          { 
            icon: DollarSign, 
            title: "Revenue Generated", 
            value: `$${analyticsData.overview.totalRevenue.toLocaleString()}`, 
            change: `+${analyticsData.overview.growth.revenue}%`, 
            color: "amber",
            subtitle: "from referrals"
          }
        ].map((metric, index) => {
          const MetricIcon = metric.icon;
          return (
            <motion.div
              key={index}
              className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 rounded-xl bg-${metric.color}-100 flex items-center justify-center`}>
                  <MetricIcon className={`w-6 h-6 text-${metric.color}-600`} />
                </div>
                <span className="text-sm text-green-600 font-semibold bg-green-50 px-2 py-1 rounded-lg">
                  {metric.change}
                </span>
              </div>
              <h3 className="text-gray-600 text-sm font-medium mb-1">{metric.title}</h3>
              <p className="text-3xl font-bold text-gray-900 mb-1">{metric.value}</p>
              <p className="text-xs text-gray-500">{metric.subtitle}</p>
            </motion.div>
          );
        })}
      </div>

      {/* Charts Section */}
      <div className="grid lg:grid-cols-3 gap-8">
        {/* Performance Chart */}
        <div className="lg:col-span-2 bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-bold text-gray-900">Performance Trends</h3>
              <p className="text-gray-600 text-sm">Track promoter activity over time</p>
            </div>

            {/* Metric Selector */}
            <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
              {[
                { key: 'referrals', label: 'Referrals', color: 'blue' },
                { key: 'conversions', label: 'Conversions', color: 'green' },
                { key: 'revenue', label: 'Revenue', color: 'purple' }
              ].map((option) => (
                <button
                  key={option.key}
                  onClick={() => setSelectedMetric(option.key as any)}
                  className={`px-3 py-1 rounded-md text-xs font-medium transition-all ${
                    selectedMetric === option.key
                      ? `bg-${option.color}-500 text-white`
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>

          {/* Simple Bar Chart */}
          <div className="space-y-4">
            {analyticsData.chartData.map((data, index) => {
              const value = data[selectedMetric as keyof typeof data] as number;
              const maxValue = Math.max(...analyticsData.chartData.map(d => d[selectedMetric as keyof typeof d] as number));
              const percentage = (value / maxValue) * 100;

              return (
                <div key={index} className="flex items-center space-x-4">
                  <div className="text-xs text-gray-500 w-16">
                    {new Date(data.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  </div>
                  <div className="flex-1 bg-gray-200 rounded-full h-6 relative overflow-hidden">
                    <motion.div
                      className={`h-full rounded-full ${
                        selectedMetric === 'referrals' ? 'bg-blue-500' :
                        selectedMetric === 'conversions' ? 'bg-green-500' : 'bg-purple-500'
                      }`}
                      initial={{ width: 0 }}
                      animate={{ width: `${percentage}%` }}
                      transition={{ delay: index * 0.1, duration: 0.8 }}
                    />
                    <div className="absolute inset-0 flex items-center justify-end pr-3">
                      <span className="text-xs font-medium text-white">
                        {selectedMetric === 'revenue' ? `$${value}` : value}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Promoter Segments */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Promoter Segments</h3>
          <p className="text-gray-600 text-sm mb-6">Distribution by customer type</p>

          <div className="space-y-4">
            {analyticsData.topSegments.map((segment, index) => (
              <motion.div
                key={index}
                className="flex items-center justify-between"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-4 h-4 rounded-full bg-${segment.color}-500`}></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{segment.segment}</p>
                    <p className="text-xs text-gray-500">{segment.count} promoters</p>
                  </div>
                </div>
                <span className="text-sm font-semibold text-gray-900">{segment.percentage}%</span>
              </motion.div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="mt-6 space-y-2">
            <button className="w-full bg-indigo-600 text-white py-2 rounded-lg font-medium hover:bg-indigo-700 transition-colors">
              Create Segment Campaign
            </button>
            <button className="w-full border border-gray-300 text-gray-700 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors">
              Export Segment Data
            </button>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-4">Recent Promoter Activity</h3>

        <div className="space-y-4">
          {[
            { 
              icon: UserCheck, 
              text: "Emma Rodriguez completed 3 successful referrals", 
              time: "2 hours ago", 
              color: "green" 
            },
            { 
              icon: Award, 
              text: "Michael Chen reached Silver tier status", 
              time: "4 hours ago", 
              color: "purple" 
            },
            { 
              icon: TrendingUp, 
              text: "Sarah Johnson's conversion rate improved to 68%", 
              time: "6 hours ago", 
              color: "blue" 
            },
            { 
              icon: Users, 
              text: "5 new promoters joined this week", 
              time: "1 day ago", 
              color: "indigo" 
            }
          ].map((activity, index) => {
            const ActivityIcon = activity.icon;
            return (
              <motion.div
                key={index}
                className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className={`w-10 h-10 rounded-full bg-${activity.color}-100 flex items-center justify-center`}>
                  <ActivityIcon className={`w-5 h-5 text-${activity.color}-600`} />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.text}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PromoterAnalytics;