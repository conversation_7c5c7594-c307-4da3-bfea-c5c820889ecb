import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Plus,
  TrendingUp,
  Users,
  DollarSign,
  Gift,
  Calendar,
  MapPin,
  Clock,
  Star,
  Trophy,
  Target,
  BarChart3,
  Zap,
  Bell,
  Settings,
  ChevronRight,
  CheckCircle,
  AlertCircle,
  Eye,
  EyeOff,
  X,
  User,
  Building,
  Phone,
  Mail,
  Globe,
  Camera,
  Save,
  Edit,
  Trash2,
  ArrowRight,
} from "lucide-react";
import BusinessProfileSetup from "./BusinessProfileSetup";
import EditProfilePanel from "./EditProfilePanel";
import CampaignManager from "./CampaignManager";
import { useBusinessProfile } from "../contexts/BusinessProfileContext";
import CustomerManager from "./CustomerManager";
import AnalyticsDashboard from "./AnalyticsDashboard";
import { Link, useNavigate } from "react-router-dom";
import TopPromoters from "./TopPromoters";
import PromoterAnalytics from "./PromoterAnalytics";
import SmartInsights from "./SmartInsights";
import LiveActivityFeed from "./LiveActivityFeed";
import QuickLaunchWidget from "./QuickLaunchWidget";
import { businessProfileService } from "../services/businessProfileService";

interface BusinessDashboardProps {
  user: any;
}

const BusinessDashboard: React.FC<BusinessDashboardProps> = ({ user }) => {
  const { profile, updateProfile, isProfileComplete } = useBusinessProfile();
  const [selectedTimeRange, setSelectedTimeRange] = useState("30d");
  const [showCampaignDetails, setShowCampaignDetails] = useState<string | null>(
    null,
  );
  const [activeTab, setActiveTab] = useState("overview");
  const [profileCompleted, setProfileCompleted] = useState(true); // Start as completed
  const [showProfileSetup, setShowProfileSetup] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [showCreateCampaignModal, setShowCreateCampaignModal] = useState(false);
  const navigate = useNavigate();
  const [hasBusinessProfile, setHasBusinessProfile] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeView, setActiveView] = useState<"dashboard" | "campaigns">(
    "dashboard",
  );
  const [businessProfile, setBusinessProfile] = useState(null);
  const [profileLoading, setProfileLoading] = useState(true);

  // Handle hash-based navigation
  React.useEffect(() => {
    const handleTabChange = (event: CustomEvent) => {
      const tab = event.detail;
      if (
        [
          "overview",
          "campaigns",
          "customers",
          "analytics",
          "profile",
          "settings",
        ].includes(tab)
      ) {
        setActiveTab(tab);
      }
    };

    window.addEventListener(
      "dashboardTabChange",
      handleTabChange as EventListener,
    );

    // Handle initial hash
    const hash = window.location.hash.substring(1);
    if (
      hash &&
      [
        "overview",
        "campaigns",
        "customers",
        "analytics",
        "profile",
        "settings",
      ].includes(hash)
    ) {
      setActiveTab(hash);
    }

    return () => {
      window.removeEventListener(
        "dashboardTabChange",
        handleTabChange as EventListener,
      );
    };
  }, []);

  // Mock data for demonstration
  const dashboardData = {
    campaigns: {
      total: 12,
      active: 8,
      pending: 3,
      completed: 1,
    },
    customers: {
      total: 1247,
      newThisMonth: 156,
      activeReferrers: 89,
    },
    revenue: {
      total: 89400,
      thisMonth: 12850,
      growth: 23.5,
    },
    performance: {
      conversionRate: 8.4,
      avgReward: 25,
      roi: 340,
    },
  };

  const recentCampaigns = [
    {
      id: 1,
      name: "Holiday Coffee Special",
      status: "active",
      referrals: 45,
      conversions: 12,
      reward: "$5 off",
      created: "2024-01-15",
    },
    {
      id: 2,
      name: "New Customer Welcome",
      status: "active",
      referrals: 78,
      conversions: 23,
      reward: "Free appetizer",
      created: "2024-01-10",
    },
    {
      id: 3,
      name: "VIP Member Exclusive",
      status: "pending",
      referrals: 0,
      conversions: 0,
      reward: "20% discount",
      created: "2024-01-20",
    },
  ];

  const handleProfileComplete = (profileData: any) => {
    console.log("✅ Business profile completed:", profileData);
    setBusinessProfile(profileData);
    setHasBusinessProfile(true);
    setActiveTab("overview");
  };

  const StatCard = ({
    icon: Icon,
    title,
    value,
    change,
    color = "blue",
  }: any) => (
    <motion.div
      className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
      whileHover={{ y: -4, scale: 1.02 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <div className="flex items-center justify-between mb-4">
        <div
          className={`w-12 h-12 rounded-xl bg-${color}-100 flex items-center justify-center`}
        >
          <Icon className={`w-6 h-6 text-${color}-600`} />
        </div>
        {change && (
          <div
            className={`text-sm font-semibold ${change > 0 ? "text-green-600" : "text-red-600"} flex items-center`}
          >
            <TrendingUp className="w-4 h-4 mr-1" />
            {change > 0 ? "+" : ""}
            {change}%
          </div>
        )}
      </div>
      <h3 className="text-gray-600 text-sm font-medium mb-1">{title}</h3>
      <p className="text-3xl font-bold text-gray-900">{value}</p>
    </motion.div>
  );

  const tabs = [
    { id: "overview", label: "Overview", icon: BarChart3 },
    { id: "campaigns", label: "Campaigns", icon: Target },
    { id: "promoters", label: "Top Promoters", icon: Trophy },
    { id: "promoter-analytics", label: "Promoter Analytics", icon: Target },
    { id: "customers", label: "Customers", icon: Users },
    { id: "analytics", label: "Analytics", icon: TrendingUp },
  ];

  const timeRanges = [
    { value: "7d", label: "Last 7 days" },
    { value: "30d", label: "Last 30 days" },
    { value: "90d", label: "Last 90 days" },
    { value: "1y", label: "Last year" },
  ];

  const handleEditProfile = () => {
    // Navigate to business page which will show the Create flow with pre-filled data
    navigate("/business");
  };
  const handleCreateCampaign = () => {
    if (!hasBusinessProfile) {
      setShowProfileSetup(true);
      return;
    }
    setShowCreateCampaignModal(true);
  };

  const handleProfileSetupComplete = (profileData: any) => {
    localStorage.setItem("business_profile", JSON.stringify(profileData));
    setHasBusinessProfile(true);
    setShowProfileSetup(false);

    // Trigger a custom event to notify other components
    window.dispatchEvent(
      new CustomEvent("businessProfileUpdated", {
        detail: profileData,
      }),
    );
  };

  const handleMyProfileClick = () => {
    if (!hasBusinessProfile) {
      setShowProfileSetup(true);
    } else {
      setActiveTab("profile");
    }
  };

  useEffect(() => {
    // Simulate loading user data
    const userData = localStorage.getItem("user_data");
    if (userData) {
      const parsedUser = JSON.parse(userData);
      // setUser(parsedUser);
    }
    setIsLoading(false);

    const loadBusinessProfile = async () => {
      try {
        setProfileLoading(true);

        // Get current user data
        const userData = localStorage.getItem("user_data");
        if (!userData) {
          setProfileLoading(false);
          return;
        }

        const user = JSON.parse(userData);
        if (user.user_type !== "business") {
          setProfileLoading(false);
          return;
        }

        console.log("🔍 Checking for business profile for user:", user.id);

        // Try to load from database first
        const profileResult = await businessProfileService.getBusinessProfile(
          user.id,
        );

        if (profileResult.success && profileResult.profile) {
          console.log("✅ Business profile found in database");
          setBusinessProfile(profileResult.profile);
          setHasBusinessProfile(true);

          // Cache it locally for faster access
          localStorage.setItem(
            "business_profile",
            JSON.stringify(profileResult.profile),
          );
        } else {
          // Check localStorage as fallback
          const localProfile = localStorage.getItem("business_profile");
          if (localProfile) {
            console.log("📱 Business profile found in localStorage");
            setBusinessProfile(JSON.parse(localProfile));
            setHasBusinessProfile(true);
          } else {
            console.log("❌ No business profile found");
            setHasBusinessProfile(false);
          }
        }
      } catch (error) {
        console.error("❌ Error loading business profile:", error);
        // Fallback to localStorage
        const localProfile = localStorage.getItem("business_profile");
        setHasBusinessProfile(!!localProfile);
        if (localProfile) {
          setBusinessProfile(JSON.parse(localProfile));
        }
      } finally {
        setProfileLoading(false);
      }
    };

    loadBusinessProfile();

    // Listen for business profile updates
    const handleBusinessProfileUpdate = (event: CustomEvent) => {
      console.log("🔄 Business profile updated event received");
      setBusinessProfile(event.detail);
      setHasBusinessProfile(true);
      setProfileLoading(false);
    };

    window.addEventListener(
      "businessProfileUpdated",
      handleBusinessProfileUpdate as EventListener,
    );

    return () => {
      window.removeEventListener(
        "businessProfileUpdated",
        handleBusinessProfileUpdate as EventListener,
      );
    };
  }, []);

  // Show loading state while checking for profile
  if (profileLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your business dashboard...</p>
        </div>
      </div>
    );
  }

  // Show business profile setup if profile doesn't exist
  // if (!hasBusinessProfile) {
  //   return (
  //     <div className="min-h-screen bg-gray-50">
  //       <BusinessProfileSetup onComplete={handleProfileComplete} />
  //     </div>
  //   );
  // }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        {/* Subtle Profile Completion Prompt */}
        {!hasBusinessProfile && (
          <motion.div
            className="bg-gradient-to-r from-indigo-50 via-purple-50 to-indigo-50 border-b border-indigo-100"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            <div className="max-w-7xl mx-auto px-4 py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <motion.div
                    className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center shadow-sm"
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  >
                    <Building className="w-4 h-4 text-white" />
                  </motion.div>
                  <div>
                    <p className="text-sm font-semibold text-gray-900 flex items-center">
                      Complete your business profile
                      <span className="ml-2 w-1.5 h-1.5 bg-indigo-500 rounded-full animate-pulse"></span>
                    </p>
                    <p className="text-xs text-gray-600">
                      Unlock campaign creation and advanced features
                    </p>
                  </div>
                </div>
                <motion.button
                  onClick={() => setShowProfileSetup(true)}
                  className="group flex items-center px-4 py-2 bg-white text-indigo-700 rounded-lg hover:bg-indigo-50 transition-all duration-200 font-medium text-sm border border-indigo-200 shadow-sm hover:shadow-md"
                  whileHover={{ scale: 1.02, y: -1 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Settings className="w-4 h-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                  Complete Setup
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}

        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center">
                <Building className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Welcome to Referit, {user.full_name || user.name}! 🎉
                </h1>
                <p className="text-gray-600">
                  Ready to grow your business through powerful referrals?
                </p>
                <div className="flex items-center mt-2 space-x-4">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                    <Zap className="w-4 h-4 mr-1" />
                    Quick Start Ready
                  </span>
                  <span className="text-sm text-gray-500">
                    Create your first campaign in under 2 minutes
                  </span>
                </div>
              </div>
            </div>
            <motion.button
              className="flex items-center px-6 py-3 rounded-xl transition-all shadow-lg font-medium bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:from-indigo-700 hover:to-purple-700"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                if (!hasBusinessProfile) {
                  setShowProfileSetup(true);
                } else {
                  setActiveTab("campaigns");
                  setActiveView("campaigns");
                }
              }}
            >
              <Plus className="w-5 h-5 mr-2" />
              New Campaign
            </motion.button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-xl mb-8 w-fit">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <motion.button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id);
                  window.location.hash = tab.id;
                }}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center ${
                  activeTab === tab.id
                    ? "bg-white text-indigo-600 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Icon className="w-4 h-4 mr-2" />
                {tab.label}
              </motion.button>
            );
          })}
        </div>

        {/* Overview Tab Content */}
        {activeTab === "overview" && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-8"
          >
            {dashboardData.campaigns.total === 0 ? (
              /* Empty State for Overview */
              <motion.div
                className="bg-white rounded-3xl shadow-lg border border-gray-100 p-16 text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <div className="max-w-2xl mx-auto">
                  <div className="w-24 h-24 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Target className="w-12 h-12 text-indigo-600" />
                  </div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-4">
                    Welcome to Your Dashboard!
                  </h3>
                  <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                    You're just one campaign away from transforming your
                    customers into your most powerful marketing force. Let's get
                    you set up for success.
                  </p>

                  <div className="grid md:grid-cols-3 gap-6 mb-10">
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                      <Target className="w-10 h-10 text-blue-600 mx-auto mb-4" />
                      <h4 className="font-bold text-gray-900 mb-2">
                        Create Campaigns
                      </h4>
                      <p className="text-sm text-gray-600">
                        Design referral campaigns that motivate customers to
                        share
                      </p>
                    </div>
                    <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-100">
                      <Users className="w-10 h-10 text-green-600 mx-auto mb-4" />
                      <h4 className="font-bold text-gray-900 mb-2">
                        Manage Customers
                      </h4>
                      <p className="text-sm text-gray-600">
                        Track your referrers and reward top performers
                      </p>
                    </div>
                    <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-100">
                      <BarChart3 className="w-10 h-10 text-purple-600 mx-auto mb-4" />
                      <h4 className="font-bold text-gray-900 mb-2">
                        Track Growth
                      </h4>
                      <p className="text-sm text-gray-600">
                        Monitor performance with detailed analytics
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <motion.button
                      onClick={() => {
                        setActiveTab("campaigns");
                        setActiveView("campaigns");
                      }}
                      className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-10 py-4 rounded-2xl font-bold hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 flex items-center mx-auto shadow-xl hover:shadow-2xl text-lg"
                      whileHover={{ y: -2, scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Zap className="w-6 h-6 mr-3" />
                      Create Your First Campaign
                    </motion.button>
                    <p className="text-gray-500">
                      ✨ Most businesses see their first referrals within 24
                      hours
                    </p>
                  </div>
                </div>
              </motion.div>
            ) : (
              <>
                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {[
                    {
                      icon: Target,
                      title: "Active Campaigns",
                      value: dashboardData.campaigns.active.toString(),
                      change: "+12%",
                      color: "blue",
                    },
                    {
                      icon: Users,
                      title: "Total Customers",
                      value: dashboardData.customers.total.toLocaleString(),
                      change: "+8%",
                      color: "green",
                    },
                    {
                      icon: DollarSign,
                      title: "Revenue",
                      value: `$${dashboardData.revenue.total.toLocaleString()}`,
                      change: `+${dashboardData.revenue.growth}%`,
                      color: "purple",
                    },
                    {
                      icon: TrendingUp,
                      title: "Conversion Rate",
                      value: `${dashboardData.performance.conversionRate}%`,
                      change: "+2.1%",
                      color: "amber",
                    },
                  ].map((stat, index) => {
                    const StatIcon = stat.icon;
                    return (
                      <motion.div
                        key={index}
                        className="bg-white rounded-xl p-6 shadow-lg border border-gray-100"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <div className="flex items-center justify-between mb-4">
                          <div
                            className={`w-12 h-12 rounded-xl bg-${stat.color}-100 flex items-center justify-center`}
                          >
                            <StatIcon
                              className={`w-6 h-6 text-${stat.color}-600`}
                            />
                          </div>
                          <span className="text-sm text-green-600 font-semibold bg-green-50 px-2 py-1 rounded-lg">
                            {stat.change}
                          </span>
                        </div>
                        <h3 className="text-gray-600 text-sm font-medium mb-1">
                          {stat.title}
                        </h3>
                        <p className="text-3xl font-bold text-gray-900">
                          {stat.value}
                        </p>
                      </motion.div>
                    );
                  })}
                </div>
              </>
            )}

            {/* Recent Activity & Quick Actions */}
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Recent Campaigns */}
              <div className="lg:col-span-2 bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-gray-900">
                    Recent Campaigns
                  </h2>
                  <button className="text-indigo-600 hover:text-indigo-700 font-medium text-sm flex items-center">
                    View all <ChevronRight className="w-4 h-4 ml-1" />
                  </button>
                </div>

                <div className="space-y-4">
                  {recentCampaigns.map((campaign, index) => (
                    <motion.div
                      key={campaign.id}
                      className="flex items-center justify-between p-4 rounded-xl hover:bg-gray-50 transition-colors"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="flex items-center space-x-4">
                        <div
                          className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                            campaign.status === "active"
                              ? "bg-green-100 text-green-600"
                              : campaign.status === "pending"
                                ? "bg-yellow-100 text-yellow-600"
                                : "bg-gray-100 text-gray-600"
                          }`}
                        >
                          <Gift className="w-6 h-6" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">
                            {campaign.name}
                          </h3>
                          <p className="text-gray-600 text-sm">
                            Reward: {campaign.reward}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-6 text-sm">
                        <div className="text-center">
                          <p className="font-semibold text-gray-900">
                            {campaign.referrals}
                          </p>
                          <p className="text-gray-500">Referrals</p>
                        </div>
                        <div className="text-center">
                          <p className="font-semibold text-gray-900">
                            {campaign.conversions}
                          </p>
                          <p className="text-gray-500">Conversions</p>
                        </div>
                        <div
                          className={`px-3 py-1 rounded-full text-xs font-medium ${
                            campaign.status === "active"
                              ? "bg-green-100 text-green-600"
                              : campaign.status === "pending"
                                ? "bg-yellow-100 text-yellow-600"
                                : "bg-gray-100 text-gray-600"
                          }`}
                        >
                          {campaign.status}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-6">
                  Quick Actions
                </h2>

                <div className="space-y-4">
                  {[
                    {
                      icon: Plus,
                      title: "Create Campaign",
                      desc: "Launch a new referral campaign",
                      color: "indigo",
                      action: () => {
                        setActiveTab("campaigns");
                        setActiveView("campaigns");
                        window.location.hash = "campaigns";
                      },
                    },
                    {
                      icon: Eye,
                      title: "View Analytics",
                      desc: "Check performance insights",
                      color: "green",
                      action: () => {
                        setActiveTab("analytics");
                        window.location.hash = "analytics";
                      },
                    },
                    {
                      icon: Users,
                      title: "Manage Customers",
                      desc: "View customer insights",
                      color: "purple",
                      action: () => {
                        setActiveTab("customers");
                        window.location.hash = "customers";
                      },
                    },
                    {
                      icon: Trophy,
                      title: "Top Promoters",
                      desc: "See your best referrers",
                      color: "amber",
                      action: () => {
                        setActiveTab("promoters");
                        window.location.hash = "promoters";
                      },
                    },
                  ].map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <motion.button
                        key={index}
                        onClick={action.action}
                        className="w-full p-4 rounded-xl border border-gray-200 hover:border-indigo-300 hover:bg-indigo-50 transition-all text-left group"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5 + index * 0.1 }}
                      >
                        <div className="flex items-center space-x-3">
                          <div
                            className={`w-10 h-10 rounded-lg bg-${action.color}-100 group-hover:bg-${action.color}-200 flex items-center justify-center transition-colors`}
                          >
                            <Icon
                              className={`w-5 h-5 text-${action.color}-600`}
                            />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">
                              {action.title}
                            </h3>
                            <p className="text-gray-600 text-sm">
                              {action.desc}
                            </p>
                          </div>
                        </div>
                      </motion.button>
                    );
                  })}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Campaigns Tab */}
        {activeTab === "campaigns" && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <CampaignManager />
          </motion.div>
        )}

        {/* Customers Tab */}
        {activeTab === "customers" && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <CustomerManager />
          </motion.div>
        )}

        {/* Profile Tab */}
        {activeTab === "profile" && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">
                  Business Profile
                </h2>
                <motion.button
                  onClick={handleEditProfile}
                  className="bg-indigo-600 text-white px-4 py-2 rounded-xl font-medium hover:bg-indigo-700 transition-colors flex items-center"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Edit Profile
                </motion.button>
              </div>
              <BusinessProfileSetup onComplete={handleProfileComplete} />
            </div>
          </motion.div>
        )}

        {/* Analytics Tab */}
        {activeTab === "analytics" && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <AnalyticsDashboard />
          </motion.div>
        )}

        {/* Settings Tab */}
        {activeTab === "settings" && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-3xl font-bold text-gray-900">
                  Account Settings
                </h2>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">
                    All systems operational
                  </span>
                </div>
              </div>

              <div className="grid lg:grid-cols-2 gap-8">
                {/* Business Profile Section */}
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Business Profile
                  </h3>

                  <div className="bg-gray-50 rounded-xl p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-semibold text-gray-900">
                        Profile Information
                      </h4>
                      <motion.button
                        onClick={handleEditProfile}
                        className="text-indigo-600 hover:text-indigo-700 font-medium text-sm flex items-center"
                      >
                        <Settings className="w-4 h-4 mr-1" />
                        Edit Profile
                      </motion.button>
                    </div>
                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Business Name:</span>
                        <span className="font-medium">Your Business</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Account Type:</span>
                        <span className="font-medium">Business Premium</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Member Since:</span>
                        <span className="font-medium">January 2024</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-xl p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">
                      Notification Preferences
                    </h4>
                    <div className="space-y-3">
                      {[
                        { label: "New referrals", enabled: true },
                        { label: "Campaign performance", enabled: true },
                        { label: "Weekly reports", enabled: false },
                        { label: "Marketing updates", enabled: false },
                      ].map((pref, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between"
                        >
                          <span className="text-gray-700">{pref.label}</span>
                          <button
                            className={`w-12 h-6 rounded-full transition-colors ${
                              pref.enabled ? "bg-indigo-600" : "bg-gray-300"
                            }`}
                          >
                            <div
                              className={`w-5 h-5 bg-white rounded-full shadow transform transition-transform ${
                                pref.enabled
                                  ? "translate-x-6"
                                  : "translate-x-0.5"
                              }`}
                            />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Account Management Section */}
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Account Management
                  </h3>

                  <div className="bg-gray-50 rounded-xl p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">
                      Security
                    </h4>
                    <div className="space-y-4">
                      <button className="w-full text-left p-4 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="font-medium text-gray-900">
                              Change Password
                            </h5>
                            <p className="text-sm text-gray-600">
                              Update your account password
                            </p>
                          </div>
                          <ArrowRight className="w-5 h-5 text-gray-400" />
                        </div>
                      </button>

                      <button className="w-full text-left p-4 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="font-medium text-gray-900">
                              Two-Factor Authentication
                            </h5>
                            <p className="text-sm text-gray-600">
                              Add an extra layer of security
                            </p>
                          </div>
                          <ArrowRight className="w-5 h-5 text-gray-400" />
                        </div>
                      </button>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-xl p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">
                      Billing & Usage
                    </h4>
                    <div className="space-y-4">
                      <div className="bg-white rounded-lg p-4 border border-gray-200">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-gray-600">Current Plan</span>
                          <span className="font-semibold text-green-600">
                            Pay-per-success
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">
                          Only pay when customers convert
                        </div>
                      </div>

                      <button className="w-full text-left p-4 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="font-medium text-gray-900">
                              View Usage Details
                            </h5>
                            <p className="text-sm text-gray-600">
                              See detailed usage and billing
                            </p>
                          </div>
                          <ArrowRight className="w-5 h-5 text-gray-400" />
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === "promoters" && <TopPromoters />}

        {activeTab === "promoter-analytics" && <PromoterAnalytics />}

        {/* Fallback for other tabs */}
        {![
          "overview",
          "campaigns",
          "customers",
          "profile",
          "analytics",
          "settings",
          "promoters",
          "promoter-analytics",
        ].includes(activeTab) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-2xl p-12 text-center shadow-lg"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Coming
              Soon
            </h2>
            <p className="text-gray-600">
              This section is under development and will be available soon.
            </p>
          </motion.div>
        )}
      </div>

      {/* Profile Setup Modal */}
      <AnimatePresence>
        {showProfileSetup && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-2xl max-w-4xl w-full py-5"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="h-full w-full max-h-[90vh] overflow-y-auto">
                <div className="p-6 pt-1 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-bold text-gray-900">
                      Complete Your Business Profile
                    </h3>
                    <button
                      onClick={() => setShowProfileSetup(false)}
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                  <p className="text-gray-600 mt-2">
                    Set up your business profile to start creating vouchers and
                    campaigns.
                  </p>
                </div>
                <div className="p-6">
                  <BusinessProfileSetup
                    onComplete={handleProfileSetupComplete}
                  />
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Create Campaign Modal */}
      <AnimatePresence>
        {showCreateCampaignModal && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-2xl p-8 max-w-md w-full"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Create New Campaign
              </h3>
              <p className="text-gray-600 mb-6">
                This feature is coming soon! You'll be able to create and manage
                referral campaigns here.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowCreateCampaignModal(false)}
                  className="flex-1 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={() => setShowCreateCampaignModal(false)}
                  className="flex-1 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  Got it
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default BusinessDashboard;
