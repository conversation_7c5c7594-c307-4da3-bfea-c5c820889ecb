import React from 'react';
import { AnimatePresence, motion } from 'framer-motion';

function MyComponent() {
  const items = [1, 2, 3];

  return (
    <div>
      <AnimatePresence>
        {items.map((item) => (
          <motion.div
            key={item}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            Item {item}
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}

export default MyComponent;