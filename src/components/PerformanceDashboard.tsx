
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingUp, 
  DollarSign, 
  Users, 
  Target,
  ArrowUpRight,
  ArrowDownRight,
  BarChart3,
  PieChart,
  Activity,
  Calendar,
  Filter,
  Download,
  Sparkles
} from 'lucide-react';

interface MetricData {
  label: string;
  value: string;
  change: number;
  trend: 'up' | 'down' | 'stable';
  icon: React.ElementType;
  color: string;
  gradient: string;
}

const PerformanceDashboard: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('7d');
  const [activeChart, setActiveChart] = useState('revenue');
  const [animatedValues, setAnimatedValues] = useState<{[key: string]: number}>({});

  const periods = [
    { key: '7d', label: '7 Days' },
    { key: '30d', label: '30 Days' },
    { key: '90d', label: '90 Days' },
    { key: '1y', label: '1 Year' }
  ];

  const metrics: MetricData[] = [
    {
      label: 'Total Revenue',
      value: '$127,543',
      change: 23.4,
      trend: 'up',
      icon: DollarSign,
      color: 'text-green-600',
      gradient: 'from-green-500 to-emerald-600'
    },
    {
      label: 'Active Referrals',
      value: '2,847',
      change: 18.2,
      trend: 'up',
      icon: Users,
      color: 'text-blue-600',
      gradient: 'from-blue-500 to-indigo-600'
    },
    {
      label: 'Conversion Rate',
      value: '94.2%',
      change: 8.1,
      trend: 'up',
      icon: Target,
      color: 'text-purple-600',
      gradient: 'from-purple-500 to-pink-600'
    },
    {
      label: 'Growth Rate',
      value: '156%',
      change: -2.3,
      trend: 'down',
      icon: TrendingUp,
      color: 'text-orange-600',
      gradient: 'from-orange-500 to-red-500'
    }
  ];

  const chartData = [
    { period: 'Mon', revenue: 12500, referrals: 45, conversion: 92 },
    { period: 'Tue', revenue: 15200, referrals: 52, conversion: 94 },
    { period: 'Wed', revenue: 18700, referrals: 68, conversion: 96 },
    { period: 'Thu', revenue: 22100, referrals: 71, conversion: 93 },
    { period: 'Fri', revenue: 25600, referrals: 89, conversion: 97 },
    { period: 'Sat', revenue: 31200, referrals: 112, conversion: 95 },
    { period: 'Sun', revenue: 28900, referrals: 98, conversion: 94 }
  ];

  // Animate counter values once when component first comes into view
  useEffect(() => {
    const timer = setTimeout(() => {
      metrics.forEach((metric, index) => {
        const numericValue = parseFloat(metric.value.replace(/[^0-9.]/g, ''));
        let start = 0;
        const increment = numericValue / 50;
        
        const counter = setInterval(() => {
          start += increment;
          setAnimatedValues(prev => ({
            ...prev,
            [metric.label]: Math.min(start, numericValue)
          }));
          
          if (start >= numericValue) {
            clearInterval(counter);
          }
        }, 30);
      });
    }, 500); // Small delay to start animation after section comes into view

    return () => clearTimeout(timer);
  }, []); // Empty dependency array so it only runs once

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="flex items-center justify-center mb-4">
            <Activity className="w-8 h-8 text-indigo-600 mr-2" />
            <h2 className="text-4xl font-bold text-gray-900">
              Live Performance Dashboard
            </h2>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Real-time insights into your referral performance with advanced analytics
          </p>
        </motion.div>

        {/* Dashboard Container */}
        <div className="bg-white rounded-3xl shadow-2xl border border-gray-200 overflow-hidden">
          {/* Dashboard Header */}
          <div className="p-6 bg-gradient-to-r from-indigo-50 to-purple-50 border-b border-gray-200">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-1">Analytics Overview</h3>
                <p className="text-gray-600">Track your referral success in real-time</p>
              </div>
              
              {/* Period Selector */}
              <div className="flex items-center space-x-2">
                <Calendar className="w-5 h-5 text-gray-500" />
                <div className="flex bg-white rounded-lg p-1 shadow-sm border border-gray-200">
                  {periods.map((period) => (
                    <button
                      key={period.key}
                      onClick={() => setSelectedPeriod(period.key)}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                        selectedPeriod === period.key
                          ? 'bg-indigo-600 text-white shadow-sm'
                          : 'text-gray-600 hover:text-indigo-600 hover:bg-indigo-50'
                      }`}
                    >
                      {period.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Metrics Grid */}
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {metrics.map((metric, index) => {
                const IconComponent = metric.icon;
                const animatedValue = animatedValues[metric.label] || 0;
                
                return (
                  <motion.div
                    key={metric.label}
                    className="bg-white rounded-2xl p-6 border border-gray-200 shadow-lg transition-all duration-300"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.6 }}
                    viewport={{ once: true }}
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${metric.gradient} flex items-center justify-center transition-transform duration-300`}>
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <div className={`flex items-center text-sm font-semibold ${
                        metric.trend === 'up' ? 'text-green-600' : 
                        metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        {metric.trend === 'up' ? (
                          <ArrowUpRight className="w-4 h-4 mr-1" />
                        ) : metric.trend === 'down' ? (
                          <ArrowDownRight className="w-4 h-4 mr-1" />
                        ) : null}
                        {Math.abs(metric.change)}%
                      </div>
                    </div>
                    
                    <div className="mb-2">
                      <div className="text-3xl font-bold text-gray-900 mb-1">
                        {metric.label.includes('Revenue') ? '$' : ''}
                        {metric.label.includes('Rate') ? 
                          `${animatedValue.toFixed(1)}%` : 
                          animatedValue.toLocaleString(undefined, { maximumFractionDigits: 0 })
                        }
                        {metric.label.includes('Revenue') && 'K'}
                      </div>
                      <div className="text-gray-600 text-sm font-medium">{metric.label}</div>
                    </div>

                    {/* Mini progress bar */}
                    <div className="w-full bg-gray-200 rounded-full h-1">
                      <motion.div
                        className={`h-1 rounded-full bg-gradient-to-r ${metric.gradient}`}
                        initial={{ width: 0 }}
                        whileInView={{ width: `${Math.min(animatedValue / 100 * 70 + 30, 100)}%` }}
                        transition={{ delay: 0.5 + index * 0.1, duration: 1 }}
                      />
                    </div>
                  </motion.div>
                );
              })}
            </div>

            {/* Chart Section */}
            <div className="bg-gray-50 rounded-2xl p-6">
              <div className="flex items-center justify-between mb-6">
                <h4 className="text-xl font-bold text-gray-900">Performance Trends</h4>
                <div className="flex items-center space-x-2">
                  <button className="p-2 rounded-lg hover:bg-white transition-colors duration-200">
                    <Filter className="w-5 h-5 text-gray-600" />
                  </button>
                  <button className="p-2 rounded-lg hover:bg-white transition-colors duration-200">
                    <Download className="w-5 h-5 text-gray-600" />
                  </button>
                </div>
              </div>

              {/* Simple Chart Representation */}
              <div className="grid grid-cols-7 gap-2 h-40">
                {chartData.map((data, index) => (
                  <motion.div
                    key={data.period}
                    className="flex flex-col justify-end items-center space-y-2"
                    initial={{ opacity: 0, scale: 0 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.1 * index, duration: 0.5 }}
                    viewport={{ once: true }}
                  >
                    <motion.div
                      className="w-full bg-gradient-to-t from-indigo-500 to-purple-600 rounded-t-lg relative group cursor-pointer"
                      style={{ height: `${(data.revenue / 35000) * 100}%` }}
                      whileHover={{ scale: 1.05 }}
                      initial={{ height: 0 }}
                      animate={{ height: `${(data.revenue / 35000) * 100}%` }}
                      transition={{ delay: 0.5 + index * 0.1, duration: 0.8 }}
                    >
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                        ${(data.revenue / 1000).toFixed(1)}K
                      </div>
                    </motion.div>
                    <div className="text-xs font-medium text-gray-600">{data.period}</div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="mt-8 flex flex-wrap gap-3 justify-center">
              {[
                { label: 'View Full Report', icon: BarChart3, color: 'bg-indigo-600 hover:bg-indigo-700' },
                { label: 'Export Data', icon: Download, color: 'bg-green-600 hover:bg-green-700' },
                { label: 'Share Insights', icon: Sparkles, color: 'bg-purple-600 hover:bg-purple-700' }
              ].map((action, index) => {
                const IconComponent = action.icon;
                return (
                  <motion.button
                    key={action.label}
                    className={`inline-flex items-center px-6 py-3 ${action.color} text-white rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-1`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 + index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <IconComponent className="w-5 h-5 mr-2" />
                    {action.label}
                  </motion.button>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PerformanceDashboard;
