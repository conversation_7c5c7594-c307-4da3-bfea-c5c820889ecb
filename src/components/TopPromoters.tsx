import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Trophy, 
  Users, 
  TrendingUp, 
  Star, 
  Gift, 
  DollarSign, 
  Mail, 
  Award,
  Crown,
  Medal,
  Target,
  Send,
  Plus,
  Calendar,
  CheckCircle,
  Zap,
  Eye,
  Filter,
  Download,
  Phone,
  MapPin,
  Share2,
  Heart,
  Sparkles,
  Flame,
  X
} from 'lucide-react';

interface TopPromoter {
  id: string;
  name: string;
  email: string;
  avatar: string;
  totalReferrals: number;
  successfulConversions: number;
  totalEarnings: number;
  monthlyReferrals: number;
  conversionRate: number;
  joinedDate: string;
  lastActivity: string;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
  badges: string[];
  preferredChannels: string[];
  location: string;
  lifetimeValue: number;
  streak: number;
}

interface Reward {
  id: string;
  type: 'cash' | 'voucher' | 'exclusive';
  title: string;
  description: string;
  value: string;
  targetUsers: string[];
  expiryDate: string;
  status: 'draft' | 'sent' | 'redeemed';
  createdAt: string;
}

interface Promoter {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  totalReferrals: number;
  successfulReferrals: number;
  totalEarnings: number;
  conversionRate: number;
  rank: number;
  tier: 'platinum' | 'gold' | 'silver' | 'bronze';
  joinedDate: string;
  lastActivity: string;
}

const TopPromoters: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [promoters] = useState<TopPromoter[]>([
    {
      id: '1',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      avatar: '/images/woman_blonde_smile.jpg',
      totalReferrals: 47,
      successfulConversions: 34,
      totalEarnings: 680,
      monthlyReferrals: 12,
      conversionRate: 72.3,
      joinedDate: '2023-08-15',
      lastActivity: '2024-01-20',
      tier: 'diamond',
      badges: ['super-sharer', 'conversion-king', 'streak-master'],
      preferredChannels: ['instagram', 'whatsapp', 'email'],
      location: 'New York, NY',
      lifetimeValue: 2340,
      streak: 15
    },
    {
      id: '2',
      name: 'Michael Chen',
      email: '<EMAIL>',
      avatar: '/images/man_young_professional.jpg',
      totalReferrals: 32,
      successfulConversions: 28,
      totalEarnings: 560,
      monthlyReferrals: 8,
      conversionRate: 87.5,
      joinedDate: '2023-09-22',
      lastActivity: '2024-01-19',
      tier: 'platinum',
      badges: ['quality-first', 'consistent-performer'],
      preferredChannels: ['linkedin', 'twitter', 'email'],
      location: 'San Francisco, CA',
      lifetimeValue: 1840,
      streak: 9
    },
    {
      id: '3',
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      avatar: '/images/woman_brunette_professional.jpg',
      totalReferrals: 29,
      successfulConversions: 21,
      totalEarnings: 420,
      monthlyReferrals: 7,
      conversionRate: 72.4,
      joinedDate: '2023-10-10',
      lastActivity: '2024-01-18',
      tier: 'gold',
      badges: ['social-star', 'growth-driver'],
      preferredChannels: ['facebook', 'instagram', 'sms'],
      location: 'Austin, TX',
      lifetimeValue: 1260,
      streak: 6
    }
  ]);
  const [autoRewardTriggers, setAutoRewardTriggers] = useState([
    { id: '1', name: 'Monthly Top Performer', condition: 'monthlyReferrals >= 10', reward: '$25 Bonus', active: true },
    { id: '2', name: 'Conversion Champion', condition: 'conversionRate >= 80', reward: 'Exclusive Voucher', active: true },
    { id: '3', name: 'Streak Superstar', condition: 'streak >= 14', reward: 'VIP Status', active: false }
  ]);

  const [showAnalytics, setShowAnalytics] = useState(false);
  const [timeFilter, setTimeFilter] = useState('month');

  const [rewards, setRewards] = useState<Reward[]>([
    {
      id: 'r1',
      type: 'cash',
      title: '$50 Performance Bonus',
      description: 'Special bonus for reaching 15+ referrals this month',
      value: '$50',
      targetUsers: ['1'],
      expiryDate: '2024-02-01',
      status: 'sent',
      createdAt: '2024-01-15'
    }
  ]);

  const [selectedPromoters, setSelectedPromoters] = useState<string[]>([]);
  const [showCreateReward, setShowCreateReward] = useState(false);
  const [newReward, setNewReward] = useState({
    type: 'cash' as 'cash' | 'voucher' | 'exclusive',
    title: '',
    description: '',
    value: '',
    expiryDate: ''
  });
  const topPromoters: Promoter[] = [
    {
      id: '1',
      name: 'Emma Rodriguez',
      email: '<EMAIL>',
      avatar: '/images/woman_brunette_professional.jpg',
      totalReferrals: 47,
      successfulReferrals: 32,
      totalEarnings: 960,
      conversionRate: 68.1,
      rank: 1,
      tier: 'platinum',
      joinedDate: '2023-07-10',
      lastActivity: '2024-01-22'
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      avatar: '/images/woman_blonde_smile.jpg',
      totalReferrals: 38,
      successfulReferrals: 24,
      totalEarnings: 720,
      conversionRate: 63.2,
      rank: 2,
      tier: 'gold',
      joinedDate: '2023-08-15',
      lastActivity: '2024-01-20'
    },
    {
      id: '3',
      name: 'Michael Chen',
      email: '<EMAIL>',
      avatar: '/images/man_young_professional.jpg',
      totalReferrals: 29,
      successfulReferrals: 18,
      totalEarnings: 540,
      conversionRate: 62.1,
      rank: 3,
      tier: 'gold',
      joinedDate: '2023-09-22',
      lastActivity: '2024-01-18'
    },
    {
      id: '4',
      name: 'Jennifer Martinez',
      email: '<EMAIL>',
      avatar: '/images/woman_redhead_creative.jpg',
      totalReferrals: 22,
      successfulReferrals: 13,
      totalEarnings: 390,
      conversionRate: 59.1,
      rank: 4,
      tier: 'silver',
      joinedDate: '2023-10-05',
      lastActivity: '2024-01-19'
    },
    {
      id: '5',
      name: 'David Kim',
      email: '<EMAIL>',
      avatar: '/images/man_designer_glasses.jpg',
      totalReferrals: 18,
      successfulReferrals: 10,
      totalEarnings: 300,
      conversionRate: 55.6,
      rank: 5,
      tier: 'silver',
      joinedDate: '2023-11-12',
      lastActivity: '2024-01-17'
    }
  ];

  const getTierConfig = (tier: string) => {
    switch (tier) {
      case 'platinum':
        return { 
          color: 'bg-gradient-to-r from-purple-500 to-purple-600',
          icon: Crown,
          textColor: 'text-purple-600',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200'
        };
      case 'gold':
        return { 
          color: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
          icon: Trophy,
          textColor: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200'
        };
      case 'silver':
        return { 
          color: 'bg-gradient-to-r from-gray-400 to-gray-500',
          icon: Medal,
          textColor: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        };
      default:
        return { 
          color: 'bg-gradient-to-r from-orange-600 to-orange-700',
          icon: Award,
          textColor: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200'
        };
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return { icon: Crown, color: 'text-purple-600' };
      case 2:
        return { icon: Trophy, color: 'text-yellow-600' };
      case 3:
        return { icon: Medal, color: 'text-orange-600' };
      default:
        return { icon: Star, color: 'text-blue-600' };
    }
  };

  const togglePromoterSelection = (promoterId: string) => {
    setSelectedPromoters(prev => 
      prev.includes(promoterId) 
        ? prev.filter(id => id !== promoterId)
        : [...prev, promoterId]
    );
  };

  const createReward = () => {
    if (!newReward.title || selectedPromoters.length === 0) return;

    const reward: Reward = {
      id: Date.now().toString(),
      type: newReward.type,
      title: newReward.title,
      description: newReward.description,
      value: newReward.value,
      targetUsers: [...selectedPromoters],
      expiryDate: newReward.expiryDate,
      status: 'draft',
      createdAt: new Date().toISOString()
    };

    setRewards(prev => [...prev, reward]);
    setNewReward({ type: 'cash', title: '', description: '', value: '', expiryDate: '' });
    setSelectedPromoters([]);
    setShowCreateReward(false);
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
           <Trophy className="w-8 h-8 text-yellow-500 mr-3" />
            Top Promoters
          </h1>
          <p className="text-gray-600 mt-2">Recognize and reward your most valuable referrers</p>
        </div>

        {/* Period Selector */}
        <div className="flex items-center space-x-2 bg-gray-100 rounded-xl p-1">
          {(['week', 'month', 'quarter', 'year'] as const).map((period) => (
            <button
              key={period}
              onClick={() => {
                setSelectedPeriod(period);
                setTimeFilter(period);
              }}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                selectedPeriod === period
                  ? 'bg-white text-indigo-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {period.charAt(0).toUpperCase() + period.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[
          { 
            icon: Users, 
            title: "Active Promoters", 
            value: topPromoters.length.toString(), 
            change: "+12%", 
            color: "blue" 
          },
          { 
            icon: TrendingUp, 
            title: "Total Referrals", 
            value: topPromoters.reduce((sum, p) => sum + p.totalReferrals, 0).toString(), 
            change: "+23%", 
            color: "green" 
          },
          { 
            icon: Target, 
            title: "Avg Conversion", 
            value: `${(topPromoters.reduce((sum, p) => sum + p.conversionRate, 0) / topPromoters.length).toFixed(1)}%`, 
            change: "+8%", 
            color: "purple" 
          },
          { 
            icon: DollarSign, 
            title: "Total Rewards", 
            value: `$${topPromoters.reduce((sum, p) => sum + p.totalEarnings, 0).toLocaleString()}`, 
            change: "+31%", 
            color: "amber" 
          }
        ].map((stat, index) => {
          const StatIcon = stat.icon;
          return (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-6 shadow-lg border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 rounded-xl bg-${stat.color}-100 flex items-center justify-center`}>
                  <StatIcon className={`w-6 h-6 text-${stat.color}-600`} />
                </div>
                <span className="text-sm text-green-600 font-semibold bg-green-50 px-2 py-1 rounded-lg">
                  {stat.change}
                </span>
              </div>
              <h3 className="text-gray-600 text-sm font-medium mb-1">{stat.title}</h3>
              <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
            </motion.div>
          );
        })}
      </div>

      {/* Top Promoters List */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="p-6 border-b border-gray-200 flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-gray-900 flex items-center">
              <Medal className="w-6 h-6 text-yellow-500 mr-2" />
              Leaderboard
            </h2>
            <p className="text-gray-600 text-sm mt-1">Your top performing referrers this {selectedPeriod}</p>
          </div>
          <div className="flex space-x-3">
            <select 
              value={timeFilter}
              onChange={(e) => {
                setTimeFilter(e.target.value);
                setSelectedPeriod(e.target.value as any);
              }}
              className="px-4 py-2 border border-gray-300 rounded-xl bg-white text-gray-700"
            >
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
              <option value="year">This Year</option>
            </select>
            <button 
              onClick={() => setShowAnalytics(!showAnalytics)}
              className="flex items-center px-4 py-2 text-gray-600 border border-gray-300 rounded-xl hover:bg-gray-50"
            >
              <TrendingUp className="w-5 h-5 mr-2" />
              Analytics
            </button>
            <button className="flex items-center px-4 py-2 text-gray-600 border border-gray-300 rounded-xl hover:bg-gray-50">
              <Download className="w-5 h-5 mr-2" />
              Export
            </button>
            <button
              onClick={() => setShowCreateReward(true)}
              disabled={selectedPromoters.length === 0}
              className={`flex items-center px-6 py-3 rounded-xl font-semibold transition-all ${
                selectedPromoters.length > 0
                  ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:from-indigo-700 hover:to-purple-700'
                  : 'bg-gray-200 text-gray-500 cursor-not-allowed'
              }`}
            >
              <Gift className="w-5 h-5 mr-2" />
              Create Reward ({selectedPromoters.length})
            </button>
          </div>
        </div>

        <div className="divide-y divide-gray-200">
          {topPromoters.length === 0 ? (
            <div className="p-16 text-center">
              <div className="max-w-lg mx-auto">
                <div className="w-24 h-24 bg-gradient-to-r from-yellow-100 to-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Trophy className="w-12 h-12 text-yellow-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Your Champions Await!</h3>
                <p className="text-gray-600 mb-8 leading-relaxed">
                  Once your customers start referring friends, you'll see your top promoters here. 
                  These are the customers who believe in your brand and actively share it with others.
                </p>
                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-6 mb-8">
                  <h4 className="font-semibold text-gray-900 mb-4">What you'll see here:</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <Star className="w-4 h-4 text-yellow-600" />
                      <span>Top referrers by volume</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Target className="w-4 h-4 text-blue-600" />
                      <span>Conversion rates</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <DollarSign className="w-4 h-4 text-green-600" />
                      <span>Earnings & rewards</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Award className="w-4 h-4 text-purple-600" />
                      <span>Performance tiers</span>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <motion.button
                    onClick={() => window.location.hash = 'campaigns'}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-2xl font-bold hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 flex items-center mx-auto shadow-lg hover:shadow-xl"
                    whileHover={{ y: -2, scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Target className="w-5 h-5 mr-2" />
                    Launch Your First Campaign
                  </motion.button>
                  <p className="text-sm text-gray-500">
                    Start driving referrals and watch your champions emerge
                  </p>
                </div>
              </div>
            </div>
          ) : (
            topPromoters.map((promoter, index) => {
            const tierConfig = getTierConfig(promoter.tier);
            const rankConfig = getRankIcon(promoter.rank);
            const RankIcon = rankConfig.icon;
            const TierIcon = tierConfig.icon;
            const isSelected = selectedPromoters.includes(promoter.id);

            return (
              <motion.div
                key={promoter.id}
                className={`p-6 hover:bg-gray-50 transition-colors group ${
                  isSelected ? 'bg-indigo-50 border-l-4 border-indigo-500' : ''
                }`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => togglePromoterSelection(promoter.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {/* Rank */}
                    <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 group-hover:bg-white transition-colors">
                      <RankIcon className={`w-6 h-6 ${rankConfig.color}`} />
                    </div>

                    {/* Avatar */}
                    <div className="relative">
                      <div className="w-14 h-14 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center text-white font-semibold text-lg">
                        {promoter.name.charAt(0)}
                      </div>
                      <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full ${tierConfig.color} flex items-center justify-center`}>
                        <TierIcon className="w-3 h-3 text-white" />
                      </div>
                    </div>

                    {/* Info */}
                    <div>
                      <div className="flex items-center space-x-3">
                        <h3 className="font-bold text-gray-900 text-lg">{promoter.name}</h3>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${tierConfig.bgColor} ${tierConfig.textColor} ${tierConfig.borderColor} border`}>
                          {promoter.tier.charAt(0).toUpperCase() + promoter.tier.slice(1)}
                        </span>
                      </div>
                      <p className="text-gray-600">{promoter.email}</p>
                      <p className="text-sm text-gray-500">
                        Member since {new Date(promoter.joinedDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-4 gap-8 text-center">
                    <div>
                      <p className="text-2xl font-bold text-gray-900">{promoter.totalReferrals}</p>
                      <p className="text-sm text-gray-500">Total Referrals</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-green-600">{promoter.successfulReferrals}</p>
                      <p className="text-sm text-gray-500">Successful</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-purple-600">{promoter.conversionRate}%</p>
                      <p className="text-sm text-gray-500">Conversion</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-amber-600">${promoter.totalEarnings}</p>
                      <p className="text-sm text-gray-500">Earned</p>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <button 
                      onClick={() => {
                        window.open(`mailto:${promoter.email}?subject=Thank you for being a top promoter!&body=Hi ${promoter.name},%0D%0A%0D%0AThank you for being one of our top promoters! Here's a special reward...`);
                      }}
                      className="p-2 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors"
                      title="Send thank you email"
                    >
                      <Mail className="w-5 h-5" />
                    </button>
                    <button 
                      onClick={() => {
                        alert(`Creating special reward for ${promoter.name}...`);
                      }}
                      className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors"
                      title="Create special reward"
                    >
                      <Gift className="w-5 h-5" />
                    </button>
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all ${
                      isSelected 
                        ? 'bg-indigo-600 border-indigo-600 text-white' 
                        : 'border-gray-300 hover:border-indigo-400'
                    }`}>
                      {isSelected && <CheckCircle className="w-4 h-4" />}
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })
          )}
        </div>
      </div>
       {/* Recent Rewards */}
       <div className="bg-white rounded-2xl shadow-lg border border-gray-100">
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-xl font-bold text-gray-900 flex items-center">
            <Gift className="w-6 h-6 text-purple-500 mr-2" />
            Recent Rewards
          </h2>
        </div>

        <div className="divide-y divide-gray-100">
          {rewards.length === 0 ? (
            <div className="p-12 text-center">
              <Gift className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No rewards sent yet</h3>
              <p className="text-gray-600">Select promoters above to send them targeted rewards</p>
            </div>
          ) : (
            rewards.map((reward) => (
              <div key={reward.id} className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                      reward.type === 'cash' ? 'bg-green-100' : reward.type === 'voucher' ? 'bg-purple-100' : 'bg-blue-100'
                    }`}>
                      {reward.type === 'cash' ? (
                        <DollarSign className="w-6 h-6 text-green-600" />
                      ) : reward.type === 'voucher' ? (
                        <Gift className="w-6 h-6 text-purple-600" />
                      ) : (
                        <Star className="w-6 h-6 text-blue-600" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{reward.title}</h3>
                      <p className="text-gray-600 text-sm">{reward.description}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        Sent to {reward.targetUsers.length} promoter{reward.targetUsers.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className="text-lg font-bold text-gray-900">{reward.value}</span>
                    <div className={`px-3 py-1 rounded-full text-xs font-medium mt-1 ${
                      reward.status === 'sent' ? 'bg-green-100 text-green-600' :
                      reward.status === 'redeemed' ? 'bg-blue-100 text-blue-600' :
                      'bg-gray-100 text-gray-600'
                    }`}>
                      {reward.status}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Create Reward Modal */}
      
        {showCreateReward && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-2xl max-w-2xl w-full p-6 max-h-[90vh] overflow-y-auto"
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold text-gray-900">Create Targeted Reward</h3>
                <button
                  onClick={() => setShowCreateReward(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* Selected Promoters */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 mb-3">
                  Selected Promoters ({selectedPromoters.length})
                </h4>
                <div className="flex flex-wrap gap-2">
                  {selectedPromoters.map(promoterId => {
                    const promoter = promoters.find(p => p.id === promoterId);
                    return promoter ? (
                      <div key={promoterId} className="flex items-center space-x-2 bg-indigo-100 px-3 py-2 rounded-lg">
                        <img src={promoter.avatar} alt={promoter.name} className="w-6 h-6 rounded-full" />
                        <span className="text-sm font-medium text-indigo-900">{promoter.name}</span>
                      </div>
                    ) : null;
                  })}
                </div>
              </div>

              {/* Reward Type */}
              <div className="mb-6">
                <label className="block text-sm font-semibold text-gray-700 mb-3">Reward Type</label>
                <div className="grid grid-cols-3 gap-3">
                  {[
                    { type: 'cash', label: 'Cash Bonus', icon: DollarSign, desc: 'Direct payment' },
                    { type: 'voucher', label: 'Exclusive Voucher', icon: Gift, desc: 'Special offer' },
                    { type: 'exclusive', label: 'VIP Access', icon: Crown, desc: 'Premium perks' }
                  ].map(option => (
                    <button
                      key={option.type}
                      onClick={() => setNewReward(prev => ({ ...prev, type: option.type as any }))}
                      className={`p-4 rounded-xl border-2 transition-all text-left ${
                        newReward.type === option.type
                          ? 'border-indigo-500 bg-indigo-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <option.icon className={`w-6 h-6 mb-2 ${
                        newReward.type === option.type ? 'text-indigo-600' : 'text-gray-400'
                      }`} />
                      <h5 className="font-medium text-gray-900">{option.label}</h5>
                      <p className="text-xs text-gray-500">{option.desc}</p>
                    </button>
                  ))}
                </div>
              </div>

              {/* Reward Details */}
              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">Title</label>
                  <input
                    type="text"
                    value={newReward.title}
                    onChange={(e) => setNewReward(prev => ({ ...prev, title: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500"
                    placeholder="e.g., Special Performance Bonus"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">Description</label>
                  <textarea
                    value={newReward.description}
                    onChange={(e) => setNewReward(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 resize-none"
                    placeholder="Describe the reward and why they've earned it"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">Value</label>
                    <input
                      type="text"
                      value={newReward.value}
                      onChange={(e) => setNewReward(prev => ({ ...prev, value: e.target.value }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500"
                      placeholder={newReward.type === 'cash' ? '$50' : newReward.type === 'voucher' ? '20% off next purchase' : 'VIP status for 3 months'}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">Expiry Date</label>
                    <input
                      type="date"
                      value={newReward.expiryDate}
                      onChange={(e) => setNewReward(prev => ({ ...prev, expiryDate: e.target.value }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500"
                    />
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowCreateReward(false)}
                  className="flex-1 py-3 text-gray-600 hover:text-gray-800 transition-colors"                >
                  Cancel
                </button>
                <button
                  onClick={createReward}
                  disabled={!newReward.title}
                  className={`flex-1 py-3 rounded-xl font-semibold transition-all ${
                    newReward.title
                      ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:from-indigo-700 hover:to-purple-700'
                      : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  Send Reward
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      
    </div>
  );
};

export default TopPromoters;