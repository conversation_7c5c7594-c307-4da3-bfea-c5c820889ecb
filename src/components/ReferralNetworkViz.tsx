
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Users, Award, TrendingUp } from 'lucide-react';

interface NetworkNode {
  id: string;
  x: number;
  y: number;
  level: number;
  earned: number;
  connections: string[];
}

const ReferralNetworkViz: React.FC = () => {
  const [nodes, setNodes] = useState<NetworkNode[]>([]);
  const [activeNode, setActiveNode] = useState<string | null>(null);
  const [totalEarned, setTotalEarned] = useState(0);

  useEffect(() => {
    // Generate sample network data
    const initialNodes: NetworkNode[] = [
      { id: 'you', x: 50, y: 50, level: 0, earned: 150, connections: ['friend1', 'friend2', 'friend3'] },
      { id: 'friend1', x: 30, y: 30, level: 1, earned: 75, connections: ['friend4', 'friend5'] },
      { id: 'friend2', x: 70, y: 30, level: 1, earned: 100, connections: ['friend6'] },
      { id: 'friend3', x: 50, y: 20, level: 1, earned: 50, connections: [] },
      { id: 'friend4', x: 20, y: 15, level: 2, earned: 25, connections: [] },
      { id: 'friend5', x: 40, y: 10, level: 2, earned: 30, connections: [] },
      { id: 'friend6', x: 80, y: 15, level: 2, earned: 40, connections: [] }
    ];
    
    setNodes(initialNodes);
    setTotalEarned(initialNodes.reduce((sum, node) => sum + node.earned, 0));
  }, []);

  const getNodeColor = (level: number) => {
    const colors = ['from-blue-500 to-purple-600', 'from-green-500 to-emerald-600', 'from-orange-500 to-red-500'];
    return colors[level] || 'from-gray-400 to-gray-600';
  };

  const getNodeSize = (level: number) => {
    return level === 0 ? 'w-16 h-16' : level === 1 ? 'w-12 h-12' : 'w-8 h-8';
  };

  return (
    <section className="py-24 bg-gradient-to-br from-slate-900 to-purple-900 text-white relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0">
        {Array.from({ length: 50 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full opacity-20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.2, 0.8, 0.2],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="max-w-7xl mx-auto px-4 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Watch Your Network Grow
          </h2>
          <p className="text-xl text-purple-200 max-w-3xl mx-auto mb-8">
            Visualize how your referrals create a network effect, generating rewards across multiple levels
          </p>
          
          <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto">
            <motion.div 
              className="text-center"
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-3xl font-bold text-green-400">${totalEarned}</div>
              <div className="text-purple-200">Total Earned</div>
            </motion.div>
            <motion.div 
              className="text-center"
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-3xl font-bold text-blue-400">{nodes.length}</div>
              <div className="text-purple-200">Network Size</div>
            </motion.div>
            <motion.div 
              className="text-center"
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-3xl font-bold text-orange-400">3</div>
              <div className="text-purple-200">Levels Deep</div>
            </motion.div>
          </div>
        </motion.div>

        {/* Interactive Network Visualization */}
        <div className="relative h-96 bg-black bg-opacity-30 rounded-2xl p-8 backdrop-blur-sm border border-purple-500/30">
          <svg className="absolute inset-0 w-full h-full">
            {/* Draw connections */}
            {nodes.map(node => 
              node.connections.map(connId => {
                const connNode = nodes.find(n => n.id === connId);
                if (!connNode) return null;
                
                return (
                  <motion.line
                    key={`${node.id}-${connId}`}
                    x1={`${node.x}%`}
                    y1={`${node.y}%`}
                    x2={`${connNode.x}%`}
                    y2={`${connNode.y}%`}
                    stroke="rgba(139, 92, 246, 0.4)"
                    strokeWidth="2"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{ duration: 1, delay: 0.5 }}
                  />
                );
              })
            )}
          </svg>

          {/* Network Nodes */}
          {nodes.map((node, index) => (
            <motion.div
              key={node.id}
              className={`absolute ${getNodeSize(node.level)} bg-gradient-to-br ${getNodeColor(node.level)} rounded-full flex items-center justify-center cursor-pointer shadow-lg border-2 border-white/20`}
              style={{
                left: `${node.x}%`,
                top: `${node.y}%`,
                transform: 'translate(-50%, -50%)'
              }}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: index * 0.1, duration: 0.5 }}
              whileHover={{ scale: 1.2, zIndex: 10 }}
              onClick={() => setActiveNode(activeNode === node.id ? null : node.id)}
            >
              {node.level === 0 ? (
                <Award className="w-8 h-8 text-white" />
              ) : (
                <Users className={`${node.level === 1 ? 'w-6 h-6' : 'w-4 h-4'} text-white`} />
              )}

              <AnimatePresence>
                {activeNode === node.id && (
                  <motion.div
                    className="absolute top-full mt-2 bg-white text-gray-900 px-3 py-2 rounded-lg shadow-xl text-sm whitespace-nowrap z-20"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                  >
                    <div className="font-semibold">{node.id === 'you' ? 'You' : `Level ${node.level}`}</div>
                    <div className="text-green-600">${node.earned} earned</div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 1, duration: 0.6 }}
          viewport={{ once: true }}
        >
          <button className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-2xl transform hover:scale-105">
            Start Building Your Network
          </button>
        </motion.div>
      </div>
    </section>
  );
};

export default ReferralNetworkViz;
