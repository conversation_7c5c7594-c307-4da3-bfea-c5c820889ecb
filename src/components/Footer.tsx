import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Twitter, 
  Linkedin, 
  Github, 
  Mail, 
  MapPin, 
  Phone,
  ArrowUpRight,
  Heart,
  Sparkles,
  ExternalLink,
  Send,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { emailService } from '../services/emailService'; // Import the email service

const Footer: React.FC = () => {
  const [newsletterEmail, setNewsletterEmail] = useState('');
  const [newsletterStatus, setNewsletterStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [newsletterMessage, setNewsletterMessage] = useState('');

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newsletterEmail.trim()) return;

    setNewsletterStatus('loading');

    try {
      const success = await emailService.sendNewsletterConfirmation({ email: newsletterEmail });

      if (success) {
        setNewsletterStatus('success');
        setNewsletterMessage('Welcome to our newsletter! Check your email for confirmation.');
        setNewsletterEmail('');
        setTimeout(() => setNewsletterStatus('idle'), 5000);
      } else {
        setNewsletterStatus('error');
        setNewsletterMessage('Failed to subscribe. Please try again.');
        setTimeout(() => setNewsletterStatus('idle'), 3000);
      }
    } catch (error) {
      setNewsletterStatus('error');
      setNewsletterMessage('An error occurred. Please try again later.');
      setTimeout(() => setNewsletterStatus('idle'), 3000);
    }
  };

  const socialLinks = [
    { icon: Twitter, href: '#', label: 'Twitter', color: 'hover:text-blue-400' },
    { icon: Linkedin, href: '#', label: 'LinkedIn', color: 'hover:text-blue-500' },
    { icon: Github, href: '#', label: 'GitHub', color: 'hover:text-purple-400' },
    { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email', color: 'hover:text-green-400' }
  ];

  const currentYear = new Date().getFullYear();

  const navigationLinks = [
    { path: '/', label: 'Home' },
    { path: '/customers', label: 'For Customers' },
    { path: '/business', label: 'For Business' },
    { path: '/contact', label: 'Contact' },
  ];

  const accountLinks = [
    { path: '/login', label: 'Log In' },
    { path: '/signup', label: 'Sign Up' },
  ];

  const supportLinks = [
    { href: '#', label: 'Help Center' },
    { href: '#', label: 'Support' },
    { href: '#', label: 'FAQ' },
    { href: '#', label: 'API Docs' },
  ];

  const legalLinks = [
    { href: '#', label: 'Privacy Policy' },
    { href: '#', label: 'Terms of Service' },
    { href: '#', label: 'Cookie Policy' },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6, ease: [0.21, 0.47, 0.32, 0.98] }
    }
  };

  return (
    <footer className="relative bg-gradient-to-br from-gray-900 via-indigo-900 to-purple-900 text-white overflow-hidden" role="contentinfo">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-blue-400 rounded-full blur-3xl opacity-10 animate-pulse"></div>
        <div className="absolute bottom-0 right-0 w-80 h-80 bg-purple-400 rounded-full blur-3xl opacity-10 animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-emerald-400 rounded-full blur-3xl opacity-5 animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 to-transparent"></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Newsletter Section */}
        <motion.div 
          className="py-16 border-b border-white/10"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-10%" }}
          variants={containerVariants}
        >
          <div className="text-center max-w-2xl mx-auto">
            <motion.div variants={itemVariants}>
              <Sparkles className="w-8 h-8 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-3xl font-bold mb-4 text-white">Stay in the Loop</h3>
              <p className="text-gray-300 text-lg mb-8">
                Get the latest updates on new features, success stories, and referral tips delivered to your inbox.
              </p>
            </motion.div>

            {newsletterStatus === 'success' ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="flex items-center justify-center p-6 bg-green-500/20 border border-green-400/30 rounded-xl max-w-md mx-auto"
                >
                  <CheckCircle className="w-6 h-6 text-green-400 mr-3" />
                  <span className="text-green-300 text-center">{newsletterMessage}</span>
                </motion.div>
              ) : (
                <motion.form 
                  onSubmit={handleNewsletterSubmit}
                  className="space-y-4 max-w-md mx-auto"
                  variants={itemVariants}
                >
                  <div className="flex flex-col sm:flex-row gap-4">
                    <input
                      type="email"
                      value={newsletterEmail}
                      onChange={(e) => setNewsletterEmail(e.target.value)}
                      placeholder="Enter your email"
                      required
                      disabled={newsletterStatus === 'loading'}
                      className="flex-1 px-6 py-4 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    />
                    <motion.button
                      type="submit"
                      disabled={newsletterStatus === 'loading' || !newsletterEmail.trim()}
                      className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl font-semibold text-white hover:from-blue-600 hover:to-purple-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                      whileHover={{ scale: newsletterStatus === 'loading' ? 1 : 1.05 }}
                      whileTap={{ scale: newsletterStatus === 'loading' ? 1 : 0.95 }}
                    >
                      {newsletterStatus === 'loading' ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Subscribing...
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4 mr-2" />
                          Subscribe
                        </>
                      )}
                    </motion.button>
                  </div>

                  {newsletterStatus === 'error' && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center justify-center p-3 bg-red-500/20 border border-red-400/30 rounded-xl"
                    >
                      <AlertCircle className="w-5 h-5 text-red-400 mr-2" />
                      <span className="text-red-300 text-sm">{newsletterMessage}</span>
                    </motion.div>
                  )}
                </motion.form>
              )}
          </div>
        </motion.div>

        {/* Main Footer Content */}
        <motion.div 
          className="py-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-10%" }}
          variants={containerVariants}
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-8 lg:gap-12">
            {/* Company Info - Spans 2 columns */}
            <motion.div className="lg:col-span-2" variants={itemVariants}>
              <Link 
                to="/" 
                className="text-3xl font-black bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent hover:from-blue-300 hover:to-purple-300 transition-all duration-300 inline-block mb-6"
                aria-label="Referit - Go to homepage"
              >
                Referit
              </Link>
              <p className="text-gray-300 text-lg leading-relaxed mb-8 max-w-md">
                Transforming word-of-mouth into measurable growth. 
                Join thousands of businesses and customers earning through our referral platform.
              </p>

              {/* Enhanced Social Media Links */}
              <div className="flex space-x-4">
                {socialLinks.map((link) => (
                  <motion.a
                    key={link.label}
                    href={link.href}
                    className={`text-gray-400 ${link.color} transition-all duration-300 p-4 rounded-xl hover:bg-white/10 backdrop-blur-sm border border-transparent hover:border-white/20 min-w-[44px] min-h-[44px] flex items-center justify-center`}
                    aria-label={`Follow us on ${link.label}`}
                    whileHover={{ scale: 1.1, y: -2 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <link.icon className="h-5 w-5" />
                  </motion.a>
                ))}
              </div>
            </motion.div>

            {/* Navigation */}
            <motion.div variants={itemVariants}>
              <h3 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                Platform
              </h3>
              <ul className="space-y-4">
                {navigationLinks.map((link) => (
                  <li key={link.path}>
                    <Link 
                      to={link.path} 
                      className="text-gray-300 hover:text-white transition-all duration-200 text-base flex items-center gap-2 group"
                    >
                      {link.label}
                      <ArrowUpRight className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Account */}
            <motion.div variants={itemVariants}>
              <h3 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
                <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                Account
              </h3>
              <ul className="space-y-4">
                {accountLinks.map((link) => (
                  <li key={link.path}>
                    <Link 
                      to={link.path} 
                      className="text-gray-300 hover:text-white transition-all duration-200 text-base flex items-center gap-2 group"
                    >
                      {link.label}
                      <ArrowUpRight className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                    </Link>
                  </li>
                ))}
                {supportLinks.map((link) => (
                  <li key={link.label}>
                    <a 
                      href={link.href} 
                      className="text-gray-300 hover:text-white transition-all duration-200 text-base flex items-center gap-2 group"
                    >
                      {link.label}
                      <ExternalLink className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Support & Contact */}
            <motion.div variants={itemVariants}>
              <h3 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                Contact
              </h3>
              <ul className="space-y-4">
                <li>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-gray-300 hover:text-white transition-all duration-200 text-base flex items-center gap-3 group"
                  >
                    <Mail className="w-4 h-4 text-gray-400 group-hover:text-emerald-400 transition-colors duration-200" />
                    <EMAIL>
                  </a>
                </li>
                <li>
                  <a 
                    href="tel:******-0123" 
                    className="text-gray-300 hover:text-white transition-all duration-200 text-base flex items-center gap-3 group"
                  >
                    <Phone className="w-4 h-4 text-gray-400 group-hover:text-blue-400 transition-colors duration-200" />
                    +****************
                  </a>
                </li>
                <li>
                  <div className="text-gray-300 text-base flex items-center gap-3">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    Available 24/7
                  </div>
                </li>
              </ul>
            </motion.div>
          </div>
        </motion.div>

        {/* Bottom Section */}
        <motion.div 
          className="border-t border-white/10 py-8"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-6 lg:space-y-0">
            <motion.div 
              className="text-gray-400 text-center lg:text-left flex items-center gap-2"
              variants={itemVariants}
            >
              <p>&copy; {currentYear} Referit. Made with</p>
              <Heart className="w-4 h-4 text-red-400 animate-pulse" />
              <p>for better connections.</p>
            </motion.div>

            <motion.div 
              className="flex flex-wrap justify-center lg:justify-end items-center gap-6 text-sm"
              variants={itemVariants}
            >
              {legalLinks.map((link, index) => (
                <React.Fragment key={link.label}>
                  <a 
                    href={link.href} 
                    className="text-gray-400 hover:text-white transition-colors duration-200 hover:underline underline-offset-4"
                  >
                    {link.label}
                  </a>
                  {index < legalLinks.length - 1 && (
                    <span className="text-gray-600 hidden sm:inline">•</span>
                  )}
                </React.Fragment>
              ))}
            </motion.div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;