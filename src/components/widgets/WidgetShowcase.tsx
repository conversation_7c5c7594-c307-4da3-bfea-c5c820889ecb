
import React from 'react';
import { motion } from 'framer-motion';
import {
  StatWidget,
  ProfileWidget,
  CalendarWidget,
  PaymentWidget,
  ListWidget,
  QuickActionWidget,
  NotificationWidget
} from './WidgetSystem';
import {
  Users,
  DollarSign,
  TrendingUp,
  Star,
  Share2,
  Gift,
  Target,
  Zap,
  Bell,
  Calendar,
  CreditCard,
  Award
} from 'lucide-react';

const WidgetShowcase: React.FC = () => {
  // Sample data
  const sampleEvents = [
    { date: '12', title: 'Team Meeting', time: '10:00 AM' },
    { date: '15', title: 'Product Launch', time: '2:00 PM' },
    { date: '20', title: 'Client Review', time: '4:30 PM' }
  ];

  const sampleTravelers = [
    {
      id: '1',
      name: '<PERSON>',
      detail: 'Product Manager',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=64&h=64&fit=crop&crop=face',
      status: 'Active'
    },
    {
      id: '2',
      name: '<PERSON>',
      detail: 'UX Designer',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=64&h=64&fit=crop&crop=face',
      status: 'Online'
    },
    {
      id: '3',
      name: 'Emily <PERSON>',
      detail: 'Developer',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=64&h=64&fit=crop&crop=face',
      status: 'Away'
    }
  ];

  const sampleNotifications = [
    {
      id: '1',
      title: 'New Referral!',
      message: 'John referred a new customer - you earned $25!',
      time: '2 minutes ago',
      type: 'success' as const
    },
    {
      id: '2',
      title: 'Achievement Unlocked',
      message: 'You\'ve reached 100 successful referrals!',
      time: '1 hour ago',
      type: 'info' as const
    },
    {
      id: '3',
      title: 'Payment Due',
      message: 'Monthly subscription payment is due tomorrow',
      time: '1 day ago',
      type: 'warning' as const
    }
  ];

  const quickActions = [
    { label: 'Share', icon: Share2, color: 'from-blue-500 to-indigo-600' },
    { label: 'Reward', icon: Gift, color: 'from-purple-500 to-pink-600' },
    { label: 'Track', icon: Target, color: 'from-green-500 to-emerald-600' },
    { label: 'Boost', icon: Zap, color: 'from-yellow-500 to-orange-600' }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 min-h-screen">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Interactive Widget System
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Beautiful, reusable components that bring your data to life with smooth animations and modern design
          </p>
        </motion.div>

        {/* Widget Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 auto-rows-max">
          
          {/* Stats Row */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <StatWidget
              title="Active Users"
              value="12,543"
              change="+12.5%"
              trend="up"
              icon={Users}
              gradient="from-blue-500 to-indigo-600"
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <StatWidget
              title="Revenue"
              value="$45,720"
              change="+8.2%"
              trend="up"
              icon={DollarSign}
              gradient="from-green-500 to-emerald-600"
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <StatWidget
              title="Referrals"
              value="2,847"
              change="+24.1%"
              trend="up"
              icon={TrendingUp}
              gradient="from-purple-500 to-pink-600"
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <StatWidget
              title="Rating"
              value="4.9"
              change="+0.2"
              trend="up"
              icon={Star}
              gradient="from-yellow-500 to-orange-600"
            />
          </motion.div>

          {/* Profile Widget */}
          <motion.div
            className="md:col-span-2 lg:col-span-1"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <ProfileWidget
              name="Alex Morgan"
              role="Top Referrer"
              avatar="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=64&h=64&fit=crop&crop=face"
              stats={[
                { label: 'Referrals', value: '156' },
                { label: 'Earned', value: '$2.4K' }
              ]}
            />
          </motion.div>

          {/* Payment Widget */}
          <motion.div
            className="md:col-span-1"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <PaymentWidget
              cardType="VISA"
              lastFour="7852"
              balance="$3,240"
              gradient="from-purple-600 to-blue-600"
            />
          </motion.div>

          {/* Calendar Widget */}
          <motion.div
            className="md:col-span-2 lg:col-span-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
          >
            <CalendarWidget events={sampleEvents} />
          </motion.div>

          {/* List Widget */}
          <motion.div
            className="md:col-span-2 lg:col-span-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <ListWidget
              title="Top Referrers"
              items={sampleTravelers}
              icon={Award}
            />
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            className="md:col-span-1 lg:col-span-1"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9 }}
          >
            <QuickActionWidget
              title="Quick Actions"
              description="Manage your referrals"
              actions={quickActions}
            />
          </motion.div>

          {/* Notifications */}
          <motion.div
            className="md:col-span-2 lg:col-span-3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 }}
          >
            <NotificationWidget notifications={sampleNotifications} />
          </motion.div>

        </div>
      </div>
    </section>
  );
};

export default WidgetShowcase;
