
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  Star, 
  Calendar,
  MapPin,
  Clock,
  Target,
  Award,
  Zap,
  Heart,
  Share2,
  BarChart3,
  Bell,
  Gift,
  CreditCard,
  Plane,
  Building,
  User,
  CheckCircle,
  ArrowRight,
  Plus,
  MoreVertical
} from 'lucide-react';

// Widget base component with glassmorphism
const WidgetBase: React.FC<{
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  hoverScale?: boolean;
}> = ({ children, className = '', size = 'md', hoverScale = true }) => {
  const sizeClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10'
  };

  return (
    <motion.div
      className={`
        backdrop-blur-xl bg-white/80 rounded-3xl shadow-xl border border-white/20
        hover:shadow-2xl hover:bg-white/90 transition-all duration-500
        ${sizeClasses[size]} ${className}
      `}
      whileHover={hoverScale ? { scale: 1.02, y: -2 } : undefined}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  );
};

// Stat Widget
export const StatWidget: React.FC<{
  title: string;
  value: string | number;
  change?: string;
  trend?: 'up' | 'down' | 'neutral';
  icon: React.ComponentType<any>;
  gradient: string;
}> = ({ title, value, change, trend, icon: Icon, gradient }) => {
  return (
    <WidgetBase size="sm" className="relative overflow-hidden">
      <div className={`absolute top-0 right-0 w-20 h-20 bg-gradient-to-br ${gradient} rounded-full blur-2xl opacity-20`} />
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-3">
          <div className={`w-10 h-10 rounded-2xl bg-gradient-to-br ${gradient} flex items-center justify-center`}>
            <Icon className="w-5 h-5 text-white" />
          </div>
          {change && (
            <div className={`flex items-center text-sm font-medium ${
              trend === 'up' ? 'text-green-600' : trend === 'down' ? 'text-red-600' : 'text-gray-600'
            }`}>
              <TrendingUp className="w-4 h-4 mr-1" />
              {change}
            </div>
          )}
        </div>
        
        <div className="text-2xl font-bold text-gray-900 mb-1">{value}</div>
        <div className="text-sm text-gray-600">{title}</div>
      </div>
    </WidgetBase>
  );
};

// Profile Widget
export const ProfileWidget: React.FC<{
  name: string;
  role: string;
  avatar: string;
  stats: { label: string; value: string }[];
}> = ({ name, role, avatar, stats }) => {
  return (
    <WidgetBase className="text-center">
      <motion.img
        src={avatar}
        alt={name}
        className="w-16 h-16 rounded-full mx-auto mb-4 border-4 border-white shadow-lg"
        whileHover={{ scale: 1.1 }}
      />
      
      <h3 className="text-xl font-bold text-gray-900 mb-1">{name}</h3>
      <p className="text-gray-600 mb-4">{role}</p>
      
      <div className="grid grid-cols-2 gap-4">
        {stats.map((stat, index) => (
          <div key={index} className="text-center">
            <div className="text-lg font-bold text-purple-600">{stat.value}</div>
            <div className="text-xs text-gray-500">{stat.label}</div>
          </div>
        ))}
      </div>
    </WidgetBase>
  );
};

// Calendar Widget
export const CalendarWidget: React.FC<{
  events: { date: string; title: string; time: string }[];
}> = ({ events }) => {
  const [selectedDate, setSelectedDate] = useState<string | null>(null);

  return (
    <WidgetBase size="lg">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-gray-900">Upcoming Events</h3>
        <Calendar className="w-6 h-6 text-purple-600" />
      </div>
      
      <div className="space-y-3">
        {events.map((event, index) => (
          <motion.div
            key={index}
            className="flex items-center p-3 rounded-2xl bg-gradient-to-r from-purple-50 to-indigo-50 hover:from-purple-100 hover:to-indigo-100 cursor-pointer"
            whileHover={{ scale: 1.02 }}
            onClick={() => setSelectedDate(selectedDate === event.date ? null : event.date)}
          >
            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 to-indigo-600 flex items-center justify-center text-white font-bold text-sm mr-3">
              {event.date}
            </div>
            
            <div className="flex-1">
              <div className="font-semibold text-gray-900">{event.title}</div>
              <div className="text-sm text-gray-600 flex items-center">
                <Clock className="w-4 h-4 mr-1" />
                {event.time}
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </WidgetBase>
  );
};

// Payment Card Widget
export const PaymentWidget: React.FC<{
  cardType: string;
  lastFour: string;
  balance: string;
  gradient: string;
}> = ({ cardType, lastFour, balance, gradient }) => {
  return (
    <WidgetBase className="relative overflow-hidden">
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-90`} />
      <div className="absolute top-4 right-4 w-16 h-16 bg-white/20 rounded-full blur-xl" />
      
      <div className="relative z-10 text-white">
        <div className="flex items-center justify-between mb-8">
          <CreditCard className="w-8 h-8" />
          <div className="text-right">
            <div className="text-sm opacity-80">{cardType}</div>
            <div className="font-bold">•••• {lastFour}</div>
          </div>
        </div>
        
        <div className="text-3xl font-bold mb-1">{balance}</div>
        <div className="text-sm opacity-80">Available Balance</div>
      </div>
    </WidgetBase>
  );
};

// Interactive List Widget
export const ListWidget: React.FC<{
  title: string;
  items: { id: string; name: string; detail: string; avatar?: string; status?: string }[];
  icon: React.ComponentType<any>;
}> = ({ title, items, icon: Icon }) => {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());

  const toggleItem = (id: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedItems(newSelected);
  };

  return (
    <WidgetBase size="lg">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mr-3">
            <Icon className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-xl font-bold text-gray-900">{title}</h3>
        </div>
        <MoreVertical className="w-5 h-5 text-gray-400" />
      </div>
      
      <div className="space-y-2">
        {items.map((item) => (
          <motion.div
            key={item.id}
            className={`flex items-center p-3 rounded-2xl cursor-pointer transition-all duration-300 ${
              selectedItems.has(item.id) 
                ? 'bg-gradient-to-r from-purple-100 to-indigo-100 border-2 border-purple-200' 
                : 'hover:bg-gray-50'
            }`}
            whileHover={{ scale: 1.01 }}
            onClick={() => toggleItem(item.id)}
          >
            {item.avatar && (
              <img
                src={item.avatar}
                alt={item.name}
                className="w-10 h-10 rounded-full mr-3 border-2 border-white shadow-sm"
              />
            )}
            
            <div className="flex-1">
              <div className="font-semibold text-gray-900">{item.name}</div>
              <div className="text-sm text-gray-600">{item.detail}</div>
            </div>
            
            {item.status && (
              <div className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                {item.status}
              </div>
            )}
            
            {selectedItems.has(item.id) && (
              <CheckCircle className="w-5 h-5 text-purple-600 ml-2" />
            )}
          </motion.div>
        ))}
      </div>
    </WidgetBase>
  );
};

// Quick Action Widget
export const QuickActionWidget: React.FC<{
  title: string;
  description: string;
  actions: { label: string; icon: React.ComponentType<any>; color: string }[];
}> = ({ title, description, actions }) => {
  return (
    <WidgetBase>
      <h3 className="text-xl font-bold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-6">{description}</p>
      
      <div className="grid grid-cols-2 gap-3">
        {actions.map((action, index) => {
          const Icon = action.icon;
          return (
            <motion.button
              key={index}
              className={`flex items-center justify-center p-4 rounded-2xl bg-gradient-to-br ${action.color} text-white font-semibold shadow-lg`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Icon className="w-5 h-5 mr-2" />
              {action.label}
            </motion.button>
          );
        })}
      </div>
    </WidgetBase>
  );
};

// Notification Widget
export const NotificationWidget: React.FC<{
  notifications: { id: string; title: string; message: string; time: string; type: 'success' | 'info' | 'warning' }[];
}> = ({ notifications }) => {
  const [dismissed, setDismissed] = useState<Set<string>>(new Set());

  const dismissNotification = (id: string) => {
    setDismissed(new Set([...dismissed, id]));
  };

  const typeColors = {
    success: 'from-green-500 to-emerald-600',
    info: 'from-blue-500 to-indigo-600',
    warning: 'from-yellow-500 to-orange-600'
  };

  return (
    <WidgetBase size="lg">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-gray-900">Notifications</h3>
        <Bell className="w-6 h-6 text-purple-600" />
      </div>
      
      <div className="space-y-3">
        <AnimatePresence>
          {notifications
            .filter(n => !dismissed.has(n.id))
            .map((notification) => (
              <motion.div
                key={notification.id}
                className="flex items-start p-4 rounded-2xl bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-100"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                layout
              >
                <div className={`w-3 h-3 rounded-full bg-gradient-to-br ${typeColors[notification.type]} mr-3 mt-2 flex-shrink-0`} />
                
                <div className="flex-1">
                  <div className="font-semibold text-gray-900 mb-1">{notification.title}</div>
                  <div className="text-sm text-gray-600 mb-2">{notification.message}</div>
                  <div className="text-xs text-gray-500">{notification.time}</div>
                </div>
                
                <button
                  onClick={() => dismissNotification(notification.id)}
                  className="text-gray-400 hover:text-gray-600 ml-2"
                >
                  <Plus className="w-4 h-4 transform rotate-45" />
                </button>
              </motion.div>
            ))}
        </AnimatePresence>
      </div>
    </WidgetBase>
  );
};

// Export all widgets
export {
  WidgetBase,
  StatWidget,
  ProfileWidget,
  CalendarWidget,
  PaymentWidget,
  ListWidget,
  QuickActionWidget,
  NotificationWidget
};
