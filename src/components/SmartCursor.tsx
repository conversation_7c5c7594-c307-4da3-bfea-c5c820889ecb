
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const SmartCursor: React.FC = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const [isClicking, setIsClicking] = useState(false);
  const [cursorVariant, setCursorVariant] = useState('default');

  useEffect(() => {
    const updateMousePosition = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    const handleMouseDown = () => setIsClicking(true);
    const handleMouseUp = () => setIsClicking(false);

    const handleMouseEnter = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      
      if (target.matches('button, a, [role="button"]')) {
        setIsHovering(true);
        setCursorVariant('button');
      } else if (target.matches('input, textarea')) {
        setIsHovering(true);
        setCursorVariant('text');
      } else if (target.matches('[data-cursor="pointer"]')) {
        setIsHovering(true);
        setCursorVariant('pointer');
      }
    };

    const handleMouseLeave = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      
      if (target.matches('button, a, [role="button"], input, textarea, [data-cursor="pointer"]')) {
        setIsHovering(false);
        setCursorVariant('default');
      }
    };

    document.addEventListener('mousemove', updateMousePosition);
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mouseover', handleMouseEnter);
    document.addEventListener('mouseout', handleMouseLeave);

    return () => {
      document.removeEventListener('mousemove', updateMousePosition);
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mouseover', handleMouseEnter);
      document.removeEventListener('mouseout', handleMouseLeave);
    };
  }, []);

  const variants = {
    default: {
      x: mousePosition.x - 6,
      y: mousePosition.y - 6,
      scale: 1,
      backgroundColor: 'rgba(99, 102, 241, 0.8)',
    },
    button: {
      x: mousePosition.x - 16,
      y: mousePosition.y - 16,
      scale: 1.5,
      backgroundColor: 'rgba(16, 185, 129, 0.8)',
    },
    text: {
      x: mousePosition.x - 1,
      y: mousePosition.y - 12,
      scale: 1,
      backgroundColor: 'rgba(139, 92, 246, 0.8)',
      height: 24,
      width: 2,
      borderRadius: 1,
    },
    pointer: {
      x: mousePosition.x - 12,
      y: mousePosition.y - 12,
      scale: 1.2,
      backgroundColor: 'rgba(245, 158, 11, 0.8)',
    }
  };

  return (
    <>
      <motion.div
        className="fixed top-0 left-0 pointer-events-none z-50 w-3 h-3 bg-indigo-500 rounded-full mix-blend-difference"
        animate={variants[cursorVariant as keyof typeof variants]}
        transition={{ type: 'spring', stiffness: 500, damping: 28 }}
        style={{
          display: window.innerWidth < 768 ? 'none' : 'block' // Hide on mobile
        }}
      />
      
      {/* Cursor trail effect */}
      <motion.div
        className="fixed top-0 left-0 pointer-events-none z-40 w-8 h-8 border-2 border-indigo-400 rounded-full opacity-30"
        animate={{
          x: mousePosition.x - 16,
          y: mousePosition.y - 16,
          scale: isClicking ? 0.8 : isHovering ? 1.5 : 1,
        }}
        transition={{ type: 'spring', stiffness: 200, damping: 20 }}
        style={{
          display: window.innerWidth < 768 ? 'none' : 'block'
        }}
      />
    </>
  );
};

export default SmartCursor;
