
import { useState, useCallback, useRef } from 'react';

interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface AsyncOperationOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
  retryAttempts?: number;
}

export function useAsyncOperation<T>(
  asyncFn: (...args: any[]) => Promise<T>,
  options: AsyncOperationOptions = {}
) {
  const [state, setState] = useState<AsyncState<T>>({
    data: null,
    loading: false,
    error: null
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const retryCountRef = useRef(0);

  const execute = useCallback(async (...args: any[]) => {
    // Cancel previous request if running
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();
    retryCountRef.current = 0;

    const attemptOperation = async (): Promise<void> => {
      try {
        setState(prev => ({ ...prev, loading: true, error: null }));

        const result = await asyncFn(...args);

        setState({
          data: result,
          loading: false,
          error: null
        });

        options.onSuccess?.(result);
      } catch (error: any) {
        if (error.name === 'AbortError') {
          return; // Request was cancelled
        }

        const shouldRetry = retryCountRef.current < (options.retryAttempts || 0);
        
        if (shouldRetry) {
          retryCountRef.current++;
          console.warn(`Operation failed, retrying (${retryCountRef.current}/${options.retryAttempts}):`, error);
          
          // Exponential backoff
          const delay = Math.pow(2, retryCountRef.current - 1) * 1000;
          setTimeout(attemptOperation, delay);
          return;
        }

        const errorMessage = error?.message || 'Operation failed';
        
        setState({
          data: null,
          loading: false,
          error: errorMessage
        });

        console.error('Async operation failed:', error);
        options.onError?.(errorMessage);
      }
    };

    await attemptOperation();
  }, [asyncFn, options]);

  const reset = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    setState({
      data: null,
      loading: false,
      error: null
    });
  }, []);

  return {
    ...state,
    execute,
    reset
  };
}
