import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Customer, Business } from '../types/database';
import { databaseService } from '../services/database';

interface User extends Partial<Customer & Business> {
  id: string;
  full_name: string;
  email: string;
  avatar_url?: string;
  provider: string;
  user_type: 'customer' | 'business';
  created_at: Date;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (provider: string, userData: any, userType?: 'customer' | 'business') => Promise<void>;
  register: (userData: any, userType: 'customer' | 'business') => Promise<void>;
  forgotPassword: (email: string, userType: 'customer' | 'business') => Promise<{ success: boolean; message: string }>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => Promise<void>;
  refreshToken: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [token, setToken] = useState<string | null>(null);

  // Check for existing session on mount and validate token
  useEffect(() => {
    const checkAuthState = async () => {
      try {
        const token = localStorage.getItem('auth_token');
        const savedUser = localStorage.getItem('user_data');

        if (token && savedUser) {
          // Check if token is expired
          if (isTokenExpired(token)) {
            console.log('Token expired, logging out');
            await logout();
            return;
          }

          const userData = JSON.parse(savedUser);
          setUser(userData);
          setToken(token);
        }
      } catch (error) {
        console.error('Error checking auth state:', error);
        // Clear potentially corrupted data
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthState();
  }, []);

  // Check token expiration periodically
  useEffect(() => {
    if (!token) return;

    const checkTokenExpiration = () => {
      if (isTokenExpired(token)) {
        console.log('Token expired during session, logging out');
        logout();
      }
    };

    // Check every 5 minutes
    const interval = setInterval(checkTokenExpiration, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [token]);

  // Helper function to check if JWT token is expired
  const isTokenExpired = (token: string): boolean => {
    const parts = token.split(".");
    console.log('token: ', parts);
    if (parts.length != 3) {
      console.error('Token is not a correct JWT token');
      return true;
    }

    try {
      if (!/^[a-zA-Z0-9+/]*={0,2}$/.test(parts[1])) {
        console.error('Payload is not correctly base64 encoded');
        return true;
      }

      const payload = JSON.parse(atob(parts[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      console.error('Error parsing token:', error);
      return true; // Treat malformed tokens as expired
    }
  };

  const login = async (provider: string, userData: any, userType: 'customer' | 'business' = 'customer') => {
    try {
      setIsLoading(true);

      let authResponse;

      if (provider === 'email') {
        // Email/password login
        authResponse = await databaseService.loginUser(userData.email, userData.password, userType);
      } else {
        // Social login
        authResponse = await databaseService.socialAuth(provider, userData, userType);
      }

      if (!authResponse.success || !authResponse.user) {
        throw new Error(authResponse.message || 'Authentication failed');
      }

      const user = authResponse.user as User;

      // Store auth data
      localStorage.setItem('auth_token', authResponse.token!);
      localStorage.setItem('user_data', JSON.stringify(user));

      setUser(user);
      setToken(authResponse.token!);

      // Trigger success events
      window.dispatchEvent(new CustomEvent('auth:login', { detail: { user, provider } }));

      // Redirect based on user type
      redirectBasedOnAccountType(user);

    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);

      // Clear storage
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');

      // Clear user state
      setUser(null);
      setToken(null);

      // In a real app, you would make an API call to invalidate the token

      // Trigger logout events
      window.dispatchEvent(new CustomEvent('auth:logout'));

    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: any, accountType: 'customer' | 'business' = 'customer') => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🔍 AuthContext register called with:', { userData, accountType });
      console.log('🎯 Will create account via:', accountType === 'business' ? '/api/businesses' : '/api/customers');

      let result;

      if (userData.provider === 'email') {
        // Email registration - route to correct endpoint based on account type
        if (accountType === 'business') {
          console.log('🏢 Creating business account via createBusiness()');
          result = await databaseService.createBusiness({
            email: userData.email,
            password: userData.password,
            full_name: userData.full_name,
            company_name: userData.company_name || userData.full_name,
            company_size: userData.company_size,
            industry: userData.industry,
            website: userData.website,
            avatar_url: userData.avatar_url,
            provider: 'email'
          });
        } else {
          console.log('👤 Creating customer account via createCustomer()');
          result = await databaseService.createCustomer({
            email: userData.email,
            password: userData.password,
            full_name: userData.full_name,
            avatar_url: userData.avatar_url,
            provider: 'email'
          });
        }
      } else {
        // Social registration - route to correct endpoint based on account type
        console.log('🔐 Social registration for account type:', accountType);
        result = await databaseService.socialAuth(userData.provider, userData, accountType);
      }

      console.log('🔍 Registration result:', result);

      if (result.success && result.user) {
        const userWithAccountType = {
          ...result.user,
          accountType: accountType,
          user_type: accountType
        };

        setUser(userWithAccountType);
        setToken(result.token);

        // Store in localStorage
        localStorage.setItem('user_data', JSON.stringify(userWithAccountType));
        localStorage.setItem('auth_token', result.token);

        console.log('✅ Registration successful, user set in context');
        console.log('🎯 Account type created:', userWithAccountType.accountType);

        // Redirect based on account type
        redirectBasedOnAccountType(userWithAccountType);
      } else {
        throw new Error(result.message || 'Registration failed');
      }
    } catch (error: any) {
      console.error('❌ Registration error in AuthContext:', error);
      setError(error.message || 'Registration failed');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const forgotPassword = async (email: string, userType: 'customer' | 'business') => {
    try {
      return await databaseService.requestPasswordReset({ email, user_type: userType });
    } catch (error) {
      console.error('Forgot password error:', error);
      return { success: false, message: 'Failed to send password reset email' };
    }
  };

  const updateUser = async (userData: Partial<User>) => {
    try {
      if (!user) throw new Error('No user logged in');

      const updatedUser = { ...user, ...userData };

      // Update storage
      localStorage.setItem('user_data', JSON.stringify(updatedUser));

      setUser(updatedUser);

      // In a real app, you would make an API call to update user data

    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    }
  };

  const refreshToken = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) throw new Error('No token found');

      // In a real app, you would make an API call to refresh the token
      const newToken = `refreshed_${Date.now()}_${user?.id}`;
      localStorage.setItem('auth_token', newToken);

    } catch (error) {
      console.error('Token refresh error:', error);
      await logout(); // Force logout if refresh fails
      throw error;
    }
  };

  const redirectBasedOnAccountType = (user: User) => {
    const accountType = user.user_type || user.accountType;
    console.log('🎯 Redirecting user based on account type:', accountType);
    
    if (accountType === 'business') {
      console.log('🏢 Redirecting to business dashboard');
      window.location.href = '/dashboard#business-profile';
    } else {
      console.log('👤 Redirecting to customer dashboard');
      window.location.href = '/dashboard';
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    register,
    forgotPassword,
    logout,
    updateUser,
    refreshToken
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;