
import React, { createContext, useContext, useState, useEffect } from 'react';

interface BusinessProfile {
  businessName: string;
  logoUrl: string;
  primaryAddress: {
    street_address: string;
    address_line_2?: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
    latitude?: number;
    longitude?: number;
    formatted_address?: string;
  };
  website: string;
  description: string;
  phoneNumber: string;
  contactEmail: string;
  locations: Array<{
    id: string;
    name: string;
    address: {
      street_address: string;
      address_line_2?: string;
      city: string;
      state: string;
      postal_code: string;
      country: string;
      latitude?: number;
      longitude?: number;
      formatted_address?: string;
    };
    phone?: string;
    website?: string;
    usePrimaryWebsite?: boolean;
    isPrimary?: boolean;
    operatingHours?: Record<string, { open: string; close: string; closed: boolean }>;
  }>;
  businessCategory?: string;
  businessTags?: string[];
}

interface BusinessProfileContextType {
  profile: BusinessProfile | null;
  updateProfile: (profileData: BusinessProfile) => void;
  isProfileComplete: boolean;
}

const BusinessProfileContext = createContext<BusinessProfileContextType | undefined>(undefined);

export const useBusinessProfile = () => {
  const context = useContext(BusinessProfileContext);
  if (context === undefined) {
    throw new Error('useBusinessProfile must be used within a BusinessProfileProvider');
  }
  return context;
};

export const BusinessProfileProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [profile, setProfile] = useState<BusinessProfile | null>(() => {
    // Load from localStorage if available
    const saved = localStorage.getItem('businessProfile');
    return saved ? JSON.parse(saved) : null;
  });

  const updateProfile = (profileData: BusinessProfile) => {
    setProfile(profileData);
    localStorage.setItem('businessProfile', JSON.stringify(profileData));
    
    // Dispatch custom event to update navigation
    window.dispatchEvent(new CustomEvent('businessProfileUpdated', { 
      detail: profileData 
    }));
  };

  const isProfileComplete = profile ? 
    Boolean(profile.businessName && profile.primaryAddress?.street_address && profile.phoneNumber && profile.businessCategory) : 
    false;

  // Save to localStorage whenever profile changes
  useEffect(() => {
    if (profile) {
      localStorage.setItem('businessProfile', JSON.stringify(profile));
    }
  }, [profile]);

  return (
    <BusinessProfileContext.Provider value={{ profile, updateProfile, isProfileComplete }}>
      {children}
    </BusinessProfileContext.Provider>
  );
};
