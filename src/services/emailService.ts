import { APP_CONFIG } from "../utils/constants";

// Frontend email service - uses fetch to communicate with backend
interface EmailData {
  firstName: string;
  lastName: string;
  email: string;
  subject: string;
  message: string;
}

interface NewsletterData {
  email: string;
}

interface PasswordResetData {
  email: string;
}

interface WelcomeEmailData {
  customerName: string;
  email: string;
  loginUrl?: string;
}

interface BusinessWelcomeEmailData {
  businessName: string;
  ownerName: string;
  email: string;
  dashboardUrl?: string;
}

class EmailService {
  private apiUrl = APP_CONFIG.API.BASE_URL;

  async sendContactEmail(data: EmailData): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/api/send-contact-notification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          subject: data.subject,
          message: data.message
        }),
      });

      return response.ok;
    } catch (error) {
      console.error('Error sending contact email:', error);
      return false;
    }
  }

  async sendNewsletterConfirmation(data: NewsletterData): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/api/send-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: data.email,
          subject: 'Newsletter Subscription Confirmed',
          text: 'Thanks for subscribing to our newsletter! You\'ll receive the latest updates on new features, success stories, referral tips, and exclusive deals.',
          html: `
            <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
              <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <div style="font-size: 48px; margin-bottom: 20px;">✨</div>
                <h1>You're In!</h1>
                <p>Thanks for subscribing to our newsletter</p>
              </div>
              <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
                <p>You'll now receive the latest updates on:</p>
                <ul>
                  <li>New features and improvements</li>
                  <li>Success stories from our community</li>
                  <li>Referral tips and best practices</li>
                  <li>Exclusive deals and opportunities</li>
                </ul>
                <p>Stay tuned for amazing content!</p>
              </div>
            </div>
          `
        }),
      });

      return response.ok;
    } catch (error) {
      console.error('Error sending newsletter confirmation:', error);
      return false;
    }
  }

  async sendPasswordResetEmail(data: PasswordResetData): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/api/send-password-reset-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: data.email
        }),
      });

      return response.ok;
    } catch (error) {
      console.error('Error sending password reset email:', error);
      return false;
    }
  }

  async sendWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/api/send-welcome-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerName: data.customerName,
          email: data.email,
          loginUrl: data.loginUrl
        }),
      });

      return response.ok;
    } catch (error) {
      console.error('Error sending welcome email:', error);
      return false;
    }
  }

  async sendBusinessWelcomeEmail(data: BusinessWelcomeEmailData): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/api/send-business-welcome-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessName: data.businessName,
          ownerName: data.ownerName,
          email: data.email,
          dashboardUrl: data.dashboardUrl
        }),
      });

      return response.ok;
    } catch (error) {
      console.error('Error sending business welcome email:', error);
      return false;
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/api/test-email`, {
        method: 'GET',
      });
      return response.ok;
    } catch (error) {
      console.error('Email connection test failed:', error);
      return false;
    }
  }
}

export const emailService = new EmailService();
export default EmailService;
