import { APP_CONFIG } from "../utils/constants";

export interface BusinessProfileData {
  businessName: string;
  primaryAddress: string;
  website: string;
  description: string;
  phoneNumber: string;
  logoUrl: string;
  contactEmail: string;
  locations: any[];
  businessCategory?: string;
  businessTags?: string[];
  businessType?: 'physical' | 'service';
  serviceArea?: string;
  primaryHours?: any;
  primaryAddressCoordinates?: { lat: number; lng: number };
  formattedAddress?: string;
  timezone?: string;
  isOpen24Hours?: boolean;
  subscriptionPlan?: string;
  locationLimit?: number;
  maxFreeLocations?: number;
  primaryProfileLocked?: boolean;
}

export interface BusinessProfileResponse {
  success: boolean;
  message?: string;
  profile?: any;
}

class BusinessProfileService {
  private baseUrl = APP_CONFIG.API.BASE_URL;

  private isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      console.error('Error parsing token:', error);
      return true; // Treat malformed tokens as expired
    }
  }

  async saveBusinessProfile(businessId: string, profileData: BusinessProfileData): Promise<BusinessProfileResponse> {
    try {
      const authToken = localStorage.getItem('auth_token');

      if (!authToken) {
        throw new Error('Authentication required - please log in');
      }

      // Check if token is expired
      if (this.isTokenExpired(authToken)) {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        throw new Error('Session expired - please log in again');
      }

      if (!businessId || businessId === 'undefined' || businessId === 'null') {
        console.error('❌ Invalid business ID provided:', businessId);
        throw new Error('Invalid business ID provided');
      }

      const validBusinessId = businessId;
      console.log('🔍 Using business ID for profile save:', validBusinessId);

      console.log('💾 Saving business profile for business ID:', businessId);
      console.log('📝 Profile data:', profileData);
      console.log('🔑 Using auth token:', authToken ? 'Present' : 'Missing');

      const response = await fetch(`${this.baseUrl}/businesses/${validBusinessId}/profile`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(profileData)
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      let result;
      try {
        result = await response.json();
      } catch (parseError) {
        console.error('❌ Failed to parse response JSON:', parseError);
        throw new Error(`Server returned invalid JSON (status: ${response.status})`);
      }

      console.log('📡 Response data:', result);

      if (response.ok && result.success) {
        console.log('✅ Business profile saved successfully');
        return {
          success: true,
          profile: result.profile,
          message: result.message
        };
      } else {
        console.error('❌ Failed to save business profile:', result.message);
        console.error('❌ Response status:', response.status);
        console.error('❌ Full response:', result);

        if (response.status === 401) {
          throw new Error('Authentication failed - please log in again');
        } else if (response.status === 403) {
          throw new Error('Permission denied - only business accounts can create profiles');
        } else if (response.status === 404) {
          throw new Error(`Business not found with ID: ${businessId}. Please ensure you have a valid business account.`);
        }

        return {
          success: false,
          message: result.message || `Server error (status: ${response.status})`
        };
      }

    } catch (error) {
      console.error('❌ Error saving business profile:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'An error occurred while saving the business profile'
      };
    }
  }

  async getBusinessProfile(businessId: string): Promise<BusinessProfileResponse> {
    try {
      const authToken = localStorage.getItem('auth_token');

      if (!authToken) {
        throw new Error('Authentication required');
      }

      const validBusinessId = businessId;

      console.log('📡 Fetching business profile for business ID:', businessId);

      const response = await fetch(`${this.baseUrl}/businesses/${validBusinessId}/profile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      const result = await response.json();

      if (response.ok && result.success) {
        console.log('✅ Business profile fetched successfully');
        return {
          success: true,
          profile: result.profile
        };
      } else {
        console.log('ℹ️ Business profile not found or failed to fetch:', result.message);
        return {
          success: false,
          message: result.message || 'Failed to fetch business profile'
        };
      }

    } catch (error) {
      console.error('❌ Error fetching business profile:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'An error occurred while fetching the business profile'
      };
    }
  }

  async hasBusinessProfile(businessId: string): Promise<boolean> {
    const result = await this.getBusinessProfile(businessId);
    return result.success;
  }
}

export const businessProfileService = new BusinessProfileService();