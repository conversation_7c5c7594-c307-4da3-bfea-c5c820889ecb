
export interface CustomerTag {
  id: string;
  name: string;
  category: 'behavior' | 'demographics' | 'lifecycle' | 'value' | 'engagement' | 'location' | 'custom';
  description?: string;
  isDefault: boolean;
  createdAt: Date;
  businessId?: string;
}

export interface TagCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  tags: CustomerTag[];
}

// Default starter tags that every new business gets
export const DEFAULT_CUSTOMER_TAGS: CustomerTag[] = [
  // Lifecycle Tags
  {
    id: 'new-customer',
    name: 'new-customer',
    category: 'lifecycle',
    description: 'Recently joined customers (within 30 days)',
    isDefault: true,
    createdAt: new Date()
  },
  {
    id: 'loyal-customer',
    name: 'loyalty-member',
    category: 'lifecycle',
    description: 'Enrolled in loyalty program',
    isDefault: true,
    createdAt: new Date()
  },
  {
    id: 'returning-customer',
    name: 'frequent-buyer',
    category: 'lifecycle',
    description: 'Makes repeat purchases regularly',
    isDefault: true,
    createdAt: new Date()
  },

  // Value Tags
  {
    id: 'high-value',
    name: 'high-value',
    category: 'value',
    description: 'Customers with high lifetime value',
    isDefault: true,
    createdAt: new Date()
  },
  {
    id: 'vip',
    name: 'vip',
    category: 'value',
    description: 'VIP customers with special privileges',
    isDefault: true,
    createdAt: new Date()
  },

  // Behavior Tags
  {
    id: 'referral-source',
    name: 'referral-source',
    category: 'behavior',
    description: 'Active referrers who bring new customers',
    isDefault: true,
    createdAt: new Date()
  },
  {
    id: 'social-media',
    name: 'social-media',
    category: 'engagement',
    description: 'Engaged on social media platforms',
    isDefault: true,
    createdAt: new Date()
  },

  // Demographics
  {
    id: 'corporate',
    name: 'corporate',
    category: 'demographics',
    description: 'Corporate or business customers',
    isDefault: true,
    createdAt: new Date()
  }
];

export const TAG_CATEGORIES: TagCategory[] = [
  {
    id: 'lifecycle',
    name: 'Customer Lifecycle',
    description: 'Track where customers are in their journey',
    icon: '🔄',
    tags: DEFAULT_CUSTOMER_TAGS.filter(tag => tag.category === 'lifecycle')
  },
  {
    id: 'value',
    name: 'Customer Value',
    description: 'Segment by spending and value',
    icon: '💎',
    tags: DEFAULT_CUSTOMER_TAGS.filter(tag => tag.category === 'value')
  },
  {
    id: 'behavior',
    name: 'Behavior Patterns',
    description: 'Group by customer behaviors and actions',
    icon: '📊',
    tags: DEFAULT_CUSTOMER_TAGS.filter(tag => tag.category === 'behavior')
  },
  {
    id: 'engagement',
    name: 'Engagement Level',
    description: 'Track customer engagement across channels',
    icon: '🚀',
    tags: DEFAULT_CUSTOMER_TAGS.filter(tag => tag.category === 'engagement')
  },
  {
    id: 'demographics',
    name: 'Demographics',
    description: 'Organize by customer demographics',
    icon: '👥',
    tags: DEFAULT_CUSTOMER_TAGS.filter(tag => tag.category === 'demographics')
  }
];

export class TagService {
  private static businessTags: Map<string, CustomerTag[]> = new Map();

  // Initialize tags for a new business
  static initializeBusinessTags(businessId: string): CustomerTag[] {
    const businessTags = DEFAULT_CUSTOMER_TAGS.map(tag => ({
      ...tag,
      businessId,
      id: `${businessId}-${tag.id}`
    }));
    
    this.businessTags.set(businessId, businessTags);
    return businessTags;
  }

  // Get all tags for a business
  static getBusinessTags(businessId: string): CustomerTag[] {
    if (!this.businessTags.has(businessId)) {
      return this.initializeBusinessTags(businessId);
    }
    return this.businessTags.get(businessId) || [];
  }

  // Add a new tag
  static addTag(businessId: string, tagData: Omit<CustomerTag, 'id' | 'isDefault' | 'createdAt' | 'businessId'>): CustomerTag {
    const existingTags = this.getBusinessTags(businessId);
    
    // Check if tag name already exists
    const existingTag = existingTags.find(tag => tag.name.toLowerCase() === tagData.name.toLowerCase());
    if (existingTag) {
      console.warn('Tag already exists:', tagData.name);
      return existingTag;
    }
    
    const newTag: CustomerTag = {
      ...tagData,
      id: `${businessId}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      isDefault: false,
      createdAt: new Date(),
      businessId
    };

    const updatedTags = [...existingTags, newTag];
    this.businessTags.set(businessId, updatedTags);
    
    console.log('✅ Tag added to service:', newTag.name, 'Total tags:', updatedTags.length);
    return newTag;
  }

  // Remove a tag
  static removeTag(businessId: string, tagId: string): { success: boolean; removedTag?: CustomerTag } {
    const existingTags = this.getBusinessTags(businessId);
    const tagToRemove = existingTags.find(tag => tag.id === tagId);
    
    if (!tagToRemove) {
      return { success: false };
    }

    // Don't allow removal of default tags that are still useful
    if (tagToRemove.isDefault) {
      console.warn('Attempting to remove default tag:', tagToRemove.name);
    }

    const updatedTags = existingTags.filter(tag => tag.id !== tagId);
    this.businessTags.set(businessId, updatedTags);
    
    return { success: true, removedTag: tagToRemove };
  }

  // Update a tag
  static updateTag(businessId: string, tagId: string, updates: Partial<Pick<CustomerTag, 'name' | 'description' | 'category'>>): CustomerTag | null {
    const existingTags = this.getBusinessTags(businessId);
    const tagIndex = existingTags.findIndex(tag => tag.id === tagId);
    
    if (tagIndex === -1) return null;

    const updatedTag = { ...existingTags[tagIndex], ...updates };
    const updatedTags = [...existingTags];
    updatedTags[tagIndex] = updatedTag;
    
    this.businessTags.set(businessId, updatedTags);
    return updatedTag;
  }

  // Process tags from CSV import
  static processCSVTags(businessId: string, csvTagString: string): string[] {
    if (!csvTagString || csvTagString.trim() === '') return [];
    
    const existingTags = this.getBusinessTags(businessId);
    const existingTagNames = existingTags.map(tag => tag.name.toLowerCase());
    
    // Parse CSV tags (comma-separated)
    const csvTags = csvTagString
      .split(',')
      .map(tag => tag.trim().toLowerCase())
      .filter(tag => tag.length > 0);

    // Add any new tags found in CSV
    csvTags.forEach(tagName => {
      if (!existingTagNames.includes(tagName)) {
        this.addTag(businessId, {
          name: tagName,
          category: 'custom',
          description: `Imported from CSV`
        });
      }
    });

    return csvTags;
  }

  // Get tags by category
  static getTagsByCategory(businessId: string, category: CustomerTag['category']): CustomerTag[] {
    const allTags = this.getBusinessTags(businessId);
    return allTags.filter(tag => tag.category === category);
  }

  // Search tags
  static searchTags(businessId: string, query: string): CustomerTag[] {
    const allTags = this.getBusinessTags(businessId);
    const lowercaseQuery = query.toLowerCase();
    
    return allTags.filter(tag => 
      tag.name.toLowerCase().includes(lowercaseQuery) ||
      tag.description?.toLowerCase().includes(lowercaseQuery) ||
      tag.category.toLowerCase().includes(lowercaseQuery)
    );
  }

  // Get tag usage statistics (mock implementation)
  static getTagUsageStats(businessId: string): Record<string, number> {
    // In a real implementation, this would query the database
    const tags = this.getBusinessTags(businessId);
    const usage: Record<string, number> = {};
    
    tags.forEach(tag => {
      // Mock usage data - in real implementation, count customer assignments
      usage[tag.name] = Math.floor(Math.random() * 50);
    });
    
    return usage;
  }
}
