import {
  Customer,
  Business,
  AuthResponse,
  PasswordResetRequest,
} from "../types/database";
import { APP_CONFIG, ERROR_MESSAGES } from "../utils/constants";

class DatabaseService {
  private baseUrl: string;
  private timeout = APP_CONFIG.API.TIMEOUT;

  constructor() {
    // Use relative path construction for Replit
    this.baseUrl = this.getApiUrl();
    console.log(
      "🎯 DatabaseService initialized with RELATIVE baseUrl:",
      this.baseUrl,
    );
    console.log("✅ Window location:", window.location.href);
    console.log(
      "🔧 All requests will use relative paths - this ensures proper Replit proxy routing",
    );
  }

  private getApiUrl(): string {
    // Always use relative paths for Replit - this ensures proper proxy routing
    console.log("🔗 Using relative API path - Replit will handle routing");
    return APP_CONFIG.API.BASE_URL;
  }

  // Force refresh the base URL (useful for debugging cache issues)
  public refreshBaseUrl(): void {
    const oldUrl = this.baseUrl;
    this.baseUrl = this.getApiUrl();
    console.log("🔄 Base URL refreshed:", { old: oldUrl, new: this.baseUrl });
  }

  private async makeRequest(
    endpoint: string,
    options: RequestInit = {},
    retryCount = 0,
  ): Promise<Response> {
    // Use relative path construction - Replit proxy will handle routing
    const fullUrl = endpoint.startsWith("/api")
      ? endpoint
      : `${this.baseUrl}${endpoint.startsWith("/") ? endpoint : `/${endpoint}`}`;
    console.log("🌐 Making request to URL:", fullUrl);
    console.log("🔧 Request options:", {
      method: options.method || "GET",
      headers: options.headers,
      body: options.body ? "Present" : "None",
    });

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(fullUrl, {
        ...options,
        signal: controller.signal,
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "Cache-Control": "no-cache",
          ...options.headers,
        },
        mode: "cors",
      });

      clearTimeout(timeoutId);
      console.log("✅ Response received:", {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        url: response.url,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("❌ Server error response:", errorText);
        throw new Error(
          `HTTP ${response.status}: ${response.statusText} - ${errorText}`,
        );
      }

      return response;
    } catch (error: any) {
      clearTimeout(timeoutId);
      console.error("❌ Request failed:", error.message);
      console.error("🔍 Error details:", {
        name: error.name,
        message: error.message,
        url: fullUrl,
        stack: error.stack?.split("\n").slice(0, 3),
      });

      // Check if it's a network connectivity issue
      if (error.message === "Failed to fetch") {
        console.error("🚨 Network Error - This usually indicates:");
        console.error("   1. CORS issues");
        console.error("   2. Backend server not responding");
        console.error("   3. DNS/routing problems");
        console.error("   4. SSL certificate issues");
        console.error("🔧 Troubleshooting steps:");
        console.error("   - Check if backend is running on port 5000");
        console.error("   - Verify CORS configuration");
        console.error("   - Test direct API access in browser");
      }

      if (error.name === "AbortError") {
        throw new Error("Request timeout");
      }

      // Retry logic for network issues
      if (
        retryCount < APP_CONFIG.API.RETRY_ATTEMPTS &&
        (error.message.includes("timeout") ||
          error.message.includes("NetworkError"))
      ) {
        console.warn(
          `Request failed, retrying (${retryCount + 1}/${APP_CONFIG.API.RETRY_ATTEMPTS}):`,
          error,
        );
        await new Promise((resolve) =>
          setTimeout(resolve, Math.pow(2, retryCount) * 1000),
        );
        return this.makeRequest(endpoint, options, retryCount + 1);
      }

      throw error;
    }
  }

  async createCustomer(userData: {
    email: string;
    password?: string;
    full_name: string;
    avatar_url?: string;
    provider: string;
  }): Promise<AuthResponse> {
    try {
      console.log("🎯 Creating CUSTOMER account via /api/customers");
      console.log("📝 Customer data:", {
        email: userData.email,
        full_name: userData.full_name,
        provider: userData.provider,
        hasPassword: !!userData.password,
      });

      const response = await this.makeRequest("/customers", {
        method: "POST",
        body: JSON.stringify({
          ...userData,
          user_type: "customer",
          referral_code: this.generateReferralCode(),
          total_earnings: 0,
          total_referrals: 0,
          email_verified: userData.provider !== "email",
          preferences: {
            notifications: true,
            theme: "light",
            language: "en",
          },
        }),
      });

      const responseData = await response.json();

      if (response.ok && responseData.success) {
        return {
          success: true,
          user: responseData.user,
          token: responseData.token,
          message: responseData.message
        };
      } else {
        // Handle specific error cases
        let errorMessage = 'Failed to create customer account';

        if (response.status === 409) {
          errorMessage = 'An account with this email already exists. Please log in or use a different email.';
        } else if (response.status === 400) {
          if (responseData.message?.includes('required')) {
            errorMessage = 'Please fill out all required fields before continuing.';
          } else if (responseData.message?.includes('password')) {
            errorMessage = 'Password must be at least 8 characters long.';
          } else {
            errorMessage = responseData.message || 'Please check your information and try again.';
          }
        } else if (responseData.message) {
          errorMessage = responseData.message;
        }

        return {
          success: false,
          message: errorMessage
        };
      }
    } catch (error) {
      console.error('❌ Customer registration error:', error);
      
      // Handle network errors vs server errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        return {
          success: false,
          message: 'Unable to connect to the server. Please check your internet connection and try again.'
        };
      }
      
      return {
        success: false,
        message: 'Unable to create account. Please check your connection and try again.'
      };
    }
  }

  async createBusiness(userData: {
    email: string;
    password?: string;
    full_name: string;
    company_name?: string;
    company_size?: string;
    industry?: string;
    website?: string;
    avatar_url?: string;
    provider: string;
  }): Promise<AuthResponse> {
    try {
      console.log("🏢 Creating BUSINESS account via /api/businesses");
      console.log("📝 Business data:", {
        email: userData.email,
        full_name: userData.full_name,
        company_name: userData.company_name,
        provider: userData.provider,
        hasPassword: !!userData.password,
      });

      const businessData = {
        ...userData,
        user_type: "business",
        company_name: userData.company_name || userData.full_name,
        company_size: userData.company_size || "1-10",
        industry: userData.industry || "Technology",
        subscription_plan: "free",
        subscription_status: "active",
        email_verified: userData.provider !== "email",
        website: userData.website || "",
      };

      console.log(
        "🚀 Sending business data to backend - Email:",
        businessData.email,
        "Company:",
        businessData.company_name,
      );

      const response = await this.makeRequest("/businesses", {
        method: "POST",
        body: JSON.stringify(businessData),
      });

      const responseData = await response.json();
      console.log('🔍 Registration result:', responseData);

      if (response.ok && responseData.success) {
        return {
          success: true,
          user: responseData.data?.user || responseData.user,
          token: responseData.token,
          message: responseData.message
        };
      } else {
        // Handle specific error cases
        let errorMessage = 'Failed to create business account';

        if (response.status === 409) {
          errorMessage = 'An account with this email already exists. Please log in or use a different email.';
        } else if (response.status === 400) {
          if (responseData.message?.includes('required')) {
            errorMessage = 'Please fill out all required fields before continuing.';
          } else if (responseData.message?.includes('password')) {
            errorMessage = 'Password must be at least 8 characters long.';
          } else {
            errorMessage = responseData.message || 'Please check your information and try again.';
          }
        } else if (responseData.message) {
          errorMessage = responseData.message;
        }

        return {
          success: false,
          message: errorMessage
        };
      }
    } catch (error) {
      console.error('❌ Business registration error:', error);
      
      // Handle network errors vs server errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        return {
          success: false,
          message: 'Unable to connect to the server. Please check your internet connection and try again.'
        };
      }
      
      return {
        success: false,
        message: 'Unable to create account. Please check your connection and try again.'
      };
    }
  }

  async loginUser(
    email: string,
    password: string,
    userType: "customer" | "business",
  ): Promise<AuthResponse> {
    try {
      console.log('🔐 Attempting login for:', email, 'Type:', userType);

      const response = await this.makeRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ email, password, user_type: userType }),
      });

      const responseData = await response.json();

      if (response.ok && responseData.success) {
        return {
          success: true,
          user: responseData.user,
          token: responseData.token,
          message: responseData.message
        };
      } else {
        // Handle specific login error cases
        let errorMessage = 'Login failed';
        let needsVerification = false;

        if (response.status === 401) {
          if (responseData.message?.includes('verify') || responseData.message?.includes('verification')) {
            errorMessage = 'Please verify your email address before logging in. Check your inbox or request a new verification email.';
            needsVerification = true;
          } else {
            errorMessage = 'Incorrect password. Please try again.';
          }
        } else if (response.status === 404) {
          errorMessage = 'No account found with this email address.';
        } else if (responseData.message) {
          errorMessage = responseData.message;
        }

        return {
          success: false,
          message: errorMessage,
          needsVerification
        };
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      return {
        success: false,
        message: 'Unable to log in. Please check your connection and try again.'
      };
    }
  }

  async socialAuth(
    provider: string,
    userData: any,
    userType: "customer" | "business",
  ): Promise<AuthResponse> {
    try {
      console.log("🔐 Social auth routing based on user type:", userType);

      // Route to the correct endpoint based on account type
      if (userType === "business") {
        console.log(
          "🏢 Social auth creating business account via /api/businesses",
        );
        return await this.createBusiness({
          email: userData.email,
          full_name: userData.full_name || userData.name,
          company_name:
            userData.company_name || userData.full_name || userData.name,
          avatar_url: userData.avatar_url || userData.picture,
          provider: provider,
        });
      } else {
        console.log(
          "👤 Social auth creating customer account via /api/customers",
        );
        return await this.createCustomer({
          email: userData.email,
          full_name: userData.full_name || userData.name,
          avatar_url: userData.avatar_url || userData.picture,
          provider: provider,
        });
      }
    } catch (error) {
      console.error("Error with social auth:", error);
      return { success: false, message: "Social authentication failed" };
    }
  }

  async requestPasswordReset(
    request: PasswordResetRequest,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await this.makeRequest("/auth/forgot-password", {
        method: "POST",
        body: JSON.stringify(request),
      });

      return await response.json();
    } catch (error) {
      console.error("Error requesting password reset:", error);
      return { success: false, message: "Failed to send password reset email" };
    }
  }

  async resetPassword(
    token: string,
    newPassword: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await this.makeRequest("/auth/reset-password", {
        method: "POST",
        body: JSON.stringify({ token, new_password: newPassword }),
      });

      return await response.json();
    } catch (error) {
      console.error("Error resetting password:", error);
      return { success: false, message: "Failed to reset password" };
    }
  }

  async verifyEmail(
    token: string,
    email: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await this.makeRequest("/auth/verify-email", {
        method: "POST",
        body: JSON.stringify({ token, email }),
      });

      return await response.json();
    } catch (error) {
      console.error("Error verifying email:", error);
      return { success: false, message: "Email verification failed" };
    }
  }

  async sendVerificationEmail(
    email: string,
    userType: "customer" | "business",
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await this.makeRequest("/auth/send-verification", {
        method: "POST",
        body: JSON.stringify({ email, user_type: userType }),
      });

      return await response.json();
    } catch (error) {
      console.error("Error sending verification email:", error);
      return { success: false, message: "Failed to send verification email" };
    }
  }

  private generateReferralCode(): string {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "";
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Mock implementation for development - replace with actual API calls
  async mockCreateUser(
    userData: any,
    userType: "customer" | "business",
  ): Promise<AuthResponse> {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const baseUser = {
      id: Math.random().toString(36).substr(2, 9),
      email: userData.email,
      full_name: userData.full_name || userData.name || "User",
      avatar_url:
        userData.avatar_url ||
        userData.picture ||
        `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.full_name || "User")}&background=6366f1&color=fff`,
      provider: userData.provider || "email",
      email_verified: userData.provider !== "email",
      created_at: new Date(),
      updated_at: new Date(),
    };

    if (userType === "customer") {
      const customer: Customer = {
        ...baseUser,
        user_type: "customer",
        referral_code: this.generateReferralCode(),
        total_earnings: 0,
        total_referrals: 0,
        preferences: {
          notifications: true,
          theme: "light",
          language: "en",
        },
      };
      return {
        success: true,
        user: customer,
        token: `mock_token_${customer.id}`,
      };
    } else {
      const business: Business = {
        ...baseUser,
        user_type: "business",
        company_name: userData.company_name || "Company",
        company_size: userData.company_size || "1-10",
        industry: userData.industry || "Technology",
        subscription_plan: "free",
        subscription_status: "active",
      };
      return {
        success: true,
        user: business,
        token: `mock_token_${business.id}`,
      };
    }
  }
}

export const databaseService = new DatabaseService();