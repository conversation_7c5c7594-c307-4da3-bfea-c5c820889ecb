
export interface BaseUser {
  id: string;
  email: string;
  password_hash?: string;
  full_name: string;
  avatar_url?: string;
  provider: 'email' | 'google' | 'apple' | 'microsoft';
  email_verified: boolean;
  reset_token?: string;
  reset_token_expires?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface Customer extends BaseUser {
  user_type: 'customer';
  referral_code: string;
  referred_by?: string;
  total_earnings: number;
  total_referrals: number;
  tags: string[];
  preferences: {
    notifications: boolean;
    theme: 'light' | 'dark';
    language: string;
  };
  custom_fields?: Record<string, any>;
}

export interface Address {
  id?: string;
  street_address: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  latitude?: number;
  longitude?: number;
  formatted_address?: string;
  address_type?: 'primary' | 'billing' | 'shipping' | 'location';
}

export interface Business extends BaseUser {
  user_type: 'business';
  company_name: string;
  company_size: string;
  industry: string;
  website?: string;
  phone?: string;
  subscription_plan: 'free' | 'basic' | 'premium' | 'enterprise';
  subscription_status: 'active' | 'inactive' | 'cancelled';
  primary_address?: Address;
  billing_address?: Address;
  business_locations?: BusinessLocation[];
  location_limit: number; // Based on subscription plan
  max_free_locations: number; // Default 2 (primary + 1 additional)
  primary_profile_locked: boolean; // Cannot delete primary profile once created
}

export interface BusinessLocation {
  id: string;
  business_id: string;
  name: string;
  address: Address;
  phone?: string;
  website?: string;
  use_primary_website?: boolean;
  operating_hours?: Record<string, { open: string; close: string; closed: boolean }>;
  use_primary_hours?: boolean;
  business_type?: 'physical' | 'service';
  business_category?: string;
  business_tags?: string[];
  use_primary_category?: boolean;
  use_primary_tags?: boolean;
  is_primary: boolean;
  can_be_deleted: boolean; // Primary location cannot be deleted
  created_at: Date;
  updated_at: Date;
}

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
}

export interface AuthResponse {
  success: boolean;
  user?: Customer | Business;
  token?: string;
  message?: string;
}

export interface PasswordResetRequest {
  email: string;
  user_type: 'customer' | 'business';
}
