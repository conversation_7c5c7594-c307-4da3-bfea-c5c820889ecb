
export interface VoucherLocationAssignment {
  voucher_id: string;
  location_id: string;
  business_id: string;
  assigned_at: Date;
  is_active: boolean;
}

export interface VoucherRedemption {
  id: string;
  voucher_id: string;
  location_id: string;
  customer_id: string;
  redeemed_at: Date;
  redemption_value: number;
}

export interface LocationVoucherStats {
  location_id: string;
  location_name: string;
  total_vouchers_assigned: number;
  total_redemptions: number;
  total_revenue: number;
  top_performing_voucher?: string;
}
