{"name": "react-typescript", "version": "1.0.0", "type": "module", "description": "React TypeScript on Replit, using Vite bundler", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit", "validate": "npm run type-check && npm run lint && npm run test", "pre-commit": "npm run validate"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "concurrently": "^9.2.0", "typescript": "^4.7.4"}, "dependencies": {"@vitejs/plugin-react": "^4.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "vite": "^6.3.5", "@supabase/supabase-js": "^2.50.0", "@tailwindcss/forms": "^0.5.10", "@types/mapbox-gl": "^3.4.1", "@types/nodemailer": "^6.4.17", "@types/react-router-dom": "^5.3.3", "@vis.gl/react-google-maps": "^1.5.3", "autoprefixer": "^10.4.21", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "express-rate-limit": "^7.5.1", "framer-motion": "^12.18.1", "lucide-react": "^0.518.0", "mapbox-gl": "^3.13.0", "nodemailer": "^7.0.3", "postcss": "^8.5.6", "react-map-gl": "^8.0.4", "react-router-dom": "^7.6.2", "tailwindcss": "^3.4.17", "vitest": "^3.2.4"}}