
const { Pool } = require('pg');
require('dotenv').config();

// Database configuration
let connectionString = process.env.DATABASE_URL || process.env.REPLIT_DB_URL;

if (connectionString && connectionString.includes('supabase.co')) {
  try {
    const url = new URL(connectionString);
    const decodedPassword = decodeURIComponent(url.password);
    connectionString = `postgresql://${url.username}:${encodeURIComponent(decodedPassword)}@${url.hostname}:${url.port}${url.pathname}${url.search}`;
  } catch (parseError) {
    console.error('Failed to parse connection string:', parseError.message);
  }
}

const pool = new Pool({
  connectionString: connectionString,
  ssl: connectionString && connectionString.includes('supabase.co') ? { 
    rejectUnauthorized: false 
  } : false,
  max: 20,
  min: 2,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 20000,
  acquireTimeoutMillis: 20000
});

async function clearDatabase() {
  try {
    console.log('🗑️  Starting database cleanup...');
    
    // Connect to database
    await pool.query('SELECT NOW()');
    console.log('✅ Database connection successful');

    // Clear all tables in the correct order (respecting foreign key constraints)
    console.log('\n📋 Clearing tables...');

    // 1. Clear referrals (references customers and businesses)
    const referralsResult = await pool.query('DELETE FROM referrals RETURNING id');
    console.log(`✅ Cleared ${referralsResult.rowCount} referrals`);

    // 2. Clear business locations (references business_profiles)
    const locationsResult = await pool.query('DELETE FROM business_locations RETURNING id');
    console.log(`✅ Cleared ${locationsResult.rowCount} business locations`);

    // 3. Clear business profiles (references businesses)
    const profilesResult = await pool.query('DELETE FROM business_profiles RETURNING id');
    console.log(`✅ Cleared ${profilesResult.rowCount} business profiles`);

    // 4. Clear customers
    const customersResult = await pool.query('DELETE FROM customers RETURNING id');
    console.log(`✅ Cleared ${customersResult.rowCount} customers`);

    // 5. Clear businesses
    const businessesResult = await pool.query('DELETE FROM businesses RETURNING id');
    console.log(`✅ Cleared ${businessesResult.rowCount} businesses`);

    // If you have campaigns and vouchers tables, clear them too
    try {
      const campaignsResult = await pool.query('DELETE FROM campaigns RETURNING id');
      console.log(`✅ Cleared ${campaignsResult.rowCount} campaigns`);
    } catch (error) {
      console.log('ℹ️  No campaigns table found (or already empty)');
    }

    try {
      const vouchersResult = await pool.query('DELETE FROM vouchers RETURNING id');
      console.log(`✅ Cleared ${vouchersResult.rowCount} vouchers`);
    } catch (error) {
      console.log('ℹ️  No vouchers table found (or already empty)');
    }

    // Reset sequences if they exist
    try {
      await pool.query('ALTER SEQUENCE IF EXISTS customers_id_seq RESTART WITH 1');
      await pool.query('ALTER SEQUENCE IF EXISTS businesses_id_seq RESTART WITH 1');
      await pool.query('ALTER SEQUENCE IF EXISTS business_profiles_id_seq RESTART WITH 1');
      console.log('✅ Reset ID sequences');
    } catch (error) {
      console.log('ℹ️  No sequences to reset (using UUIDs)');
    }

    console.log('\n🎉 Database cleanup completed successfully!');
    console.log('📊 Summary:');
    console.log(`   - ${referralsResult.rowCount} referrals deleted`);
    console.log(`   - ${locationsResult.rowCount} business locations deleted`);
    console.log(`   - ${profilesResult.rowCount} business profiles deleted`);
    console.log(`   - ${customersResult.rowCount} customers deleted`);
    console.log(`   - ${businessesResult.rowCount} businesses deleted`);
    
    console.log('\n✨ Your database is now clean and ready for fresh data!');

  } catch (error) {
    console.error('❌ Error clearing database:', error);
    console.error('Error details:', error.message);
  } finally {
    await pool.end();
    console.log('🔌 Database connection closed');
  }
}

// Run the cleanup
clearDatabase();
