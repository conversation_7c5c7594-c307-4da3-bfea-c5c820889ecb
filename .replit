run = "npm run dev"
modules = ["nodejs-20"]
hidden = [".config", "tsconfig.json", "tsconfig.node.json", "vite.config.mjs", ".gitignore"]

[nix]
channel = "stable-24_05"

[env]
NODE_ENV = "development"
PORT = "5173"

[[ports]]
localPort = 5000
externalPort = 5000

[[ports]]
localPort = 5173
externalPort = 80

[[ports]]
localPort = 5174
externalPort = 3003

[[ports]]
localPort = 5175
externalPort = 3000

[[ports]]
localPort = 5176
externalPort = 3001

[[ports]]
localPort = 5177
externalPort = 3002

[workflows]
runButton = "Full Stack"

[[workflows.workflow]]
name = "Full Stack"
author = 42889347
mode = "parallel"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "cd server && node index.js"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"

[[workflows.workflow]]
name = "Frontend Only"
author = 42889347
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"

[[workflows.workflow]]
name = "Backend Only"
author = 42889347
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "cd server && node index.js"
