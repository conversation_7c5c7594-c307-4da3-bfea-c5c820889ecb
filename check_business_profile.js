
const { Pool } = require('pg');
require('dotenv').config();

// Database configuration
let connectionString = process.env.DATABASE_URL || process.env.REPLIT_DB_URL;

if (connectionString && connectionString.includes('supabase.co')) {
  try {
    const url = new URL(connectionString);
    const decodedPassword = decodeURIComponent(url.password);
    connectionString = `postgresql://${url.username}:${encodeURIComponent(decodedPassword)}@${url.hostname}:${url.port}${url.pathname}${url.search}`;
  } catch (parseError) {
    console.error('Failed to parse connection string:', parseError.message);
  }
}

const pool = new Pool({
  connectionString: connectionString,
  ssl: connectionString && connectionString.includes('supabase.co') ? { 
    rejectUnauthorized: false 
  } : false,
  max: 20,
  min: 2,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 20000,
  acquireTimeoutMillis: 20000
});

async function checkBusinessProfile() {
  const email = '<EMAIL>';
  
  try {
    console.log('🔍 Checking database for business account:', email);
    
    // First, check if the business account exists
    const businessResult = await pool.query(
      'SELECT id, email, full_name, company_name, created_at FROM businesses WHERE email = $1',
      [email]
    );
    
    if (businessResult.rows.length === 0) {
      console.log('❌ No business account found with email:', email);
      return;
    }
    
    const business = businessResult.rows[0];
    console.log('✅ Business account found:');
    console.log('   ID:', business.id);
    console.log('   Email:', business.email);
    console.log('   Full Name:', business.full_name);
    console.log('   Company Name:', business.company_name);
    console.log('   Created At:', business.created_at);
    
    // Now check for business profile
    const profileResult = await pool.query(
      'SELECT * FROM business_profiles WHERE business_id = $1',
      [business.id]
    );
    
    if (profileResult.rows.length === 0) {
      console.log('❌ No business profile found for this business account');
      console.log('   Business ID:', business.id);
      console.log('   This means the business needs to complete profile setup');
    } else {
      const profile = profileResult.rows[0];
      console.log('✅ Business profile found:');
      console.log('   Profile ID:', profile.id);
      console.log('   Business Name:', profile.business_name);
      console.log('   Phone Number:', profile.phone_number);
      console.log('   Business Category:', profile.business_category);
      console.log('   Created At:', profile.created_at);
      console.log('   Updated At:', profile.updated_at);
      
      // Check for locations
      const locationsResult = await pool.query(
        'SELECT * FROM business_locations WHERE business_id = $1',
        [business.id]
      );
      
      console.log('📍 Business locations:', locationsResult.rows.length);
      locationsResult.rows.forEach((location, index) => {
        console.log(`   Location ${index + 1}:`, location.name);
      });
    }
    
  } catch (error) {
    console.error('❌ Error checking business profile:', error);
  } finally {
    await pool.end();
  }
}

checkBusinessProfile();
