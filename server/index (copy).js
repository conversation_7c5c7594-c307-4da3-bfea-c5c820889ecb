const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const nodemailer = require('nodemailer');
const crypto = require('crypto');
const emailTemplates = require('./emailTemplates');
require('dotenv').config();

const app = express();
const port = 5000;

// ===== SECURITY & PERFORMANCE CONFIGURATION =====
app.set('trust proxy', true);
app.disable('x-powered-by');

// ===== CORS CONFIGURATION =====
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);

    // Allow all origins in development, restrict in production
    const allowedOrigins = [
      /\.replit\.dev$/,
      /localhost:\d+$/,
      /127\.0\.0\.1:\d+$/,
      'https://referit.com',
      'https://www.referit.com'
    ];

    const isAllowed = allowedOrigins.some(pattern => {
      if (pattern instanceof RegExp) {
        return pattern.test(origin);
      }
      return pattern === origin;
    });

    if (isAllowed) {
      callback(null, true);
    } else {
      console.warn('CORS blocked origin:', origin);
      callback(null, true); // Allow all in development
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control',
    'Pragma'
  ],
  maxAge: 86400, // 24 hours
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));

// ===== EXPLICIT OPTIONS HANDLER =====
app.options('*', cors(corsOptions));

// ===== BODY PARSING MIDDLEWARE =====
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// ===== REQUEST LOGGING MIDDLEWARE (SIMPLIFIED) =====
app.use((req, res, next) => {
  if (process.env.NODE_ENV !== 'production') {
    console.log(`${new Date().toISOString()} ${req.method} ${req.path}`);
  }
  next();
});

// ===== DATABASE CONFIGURATION =====
let connectionString = process.env.DATABASE_URL || process.env.REPLIT_DB_URL;

if (connectionString && connectionString.includes('supabase.co')) {
  try {
    const url = new URL(connectionString);
    const decodedPassword = decodeURIComponent(url.password);
    connectionString = `postgresql://${url.username}:${encodeURIComponent(decodedPassword)}@${url.hostname}:${url.port}${url.pathname}${url.search}`;
  } catch (parseError) {
    console.error('Failed to parse connection string:', parseError.message);
  }
}

const pool = new Pool({
  connectionString: connectionString,
  ssl: connectionString && connectionString.includes('supabase.co') ? { 
    rejectUnauthorized: false 
  } : false,
  max: 20,
  min: 2,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 20000,
  acquireTimeoutMillis: 20000
});

// ===== EMAIL CONFIGURATION =====
const emailTransporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp-relay.brevo.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

// ===== DATABASE INITIALIZATION =====
async function initDatabase() {
  try {
    await pool.query('SELECT NOW()');
    console.log('Database connected successfully');

    // Create customers table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS customers (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash TEXT,
        full_name VARCHAR(255) NOT NULL,
        avatar_url TEXT,
        provider VARCHAR(50) DEFAULT 'email',
        email_verified BOOLEAN DEFAULT false,
        user_type VARCHAR(20) DEFAULT 'customer',
        phone VARCHAR(20),
        date_of_birth DATE,
        gender VARCHAR(10),
        address JSONB,
        preferences JSONB DEFAULT '{}',
        total_earnings DECIMAL(10,2) DEFAULT 0,
        total_referrals INTEGER DEFAULT 0,
        successful_referrals INTEGER DEFAULT 0,
        tier VARCHAR(20) DEFAULT 'bronze',
        social_links JSONB DEFAULT '{}',
        reset_token TEXT,
        reset_token_expires TIMESTAMP,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);

    // Create businesses table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS businesses (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash TEXT,
        full_name VARCHAR(255) NOT NULL,
        avatar_url TEXT,
        provider VARCHAR(50) DEFAULT 'email',
        email_verified BOOLEAN DEFAULT false,
        user_type VARCHAR(20) DEFAULT 'business',
        company_name VARCHAR(255) NOT NULL,
        company_size VARCHAR(50),
        industry VARCHAR(100),
        website VARCHAR(255),
        phone VARCHAR(20),
        subscription_plan VARCHAR(20) DEFAULT 'free',
        subscription_status VARCHAR(20) DEFAULT 'active',
        billing_address JSONB,
        reset_token TEXT,
        reset_token_expires TIMESTAMP,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);

    // Create business_profiles table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS business_profiles (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
        business_name VARCHAR(255) NOT NULL,
        logo_url TEXT,
        primary_address JSONB NOT NULL,
        website VARCHAR(255),
        description TEXT,
        phone_number VARCHAR(20) NOT NULL,
        contact_email VARCHAR(255),
        business_category VARCHAR(100),
        business_tags TEXT[],
        business_type VARCHAR(50) DEFAULT 'physical',
        service_area VARCHAR(255),
        primary_hours JSONB,
        timezone VARCHAR(100),
        is_open_24_hours BOOLEAN DEFAULT false,
        location_limit INTEGER DEFAULT 2,
        max_free_locations INTEGER DEFAULT 2,
        primary_profile_locked BOOLEAN DEFAULT true,
        formatted_address TEXT,
        coordinates JSONB,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW(),
        UNIQUE(business_id)
      )
    `);

    // Create business_locations table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS business_locations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
        profile_id UUID REFERENCES business_profiles(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        address JSONB NOT NULL,
        phone VARCHAR(20),
        website VARCHAR(255),
        use_primary_website BOOLEAN DEFAULT true,
        operating_hours JSONB,
        use_primary_hours BOOLEAN DEFAULT true,
        business_type VARCHAR(50),
        business_category VARCHAR(100),
        business_tags TEXT[],
        use_primary_category BOOLEAN DEFAULT true,
        use_primary_tags BOOLEAN DEFAULT true,
        is_primary BOOLEAN DEFAULT false,
        can_be_deleted BOOLEAN DEFAULT true,
        coordinates JSONB,
        formatted_address TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);

    await pool.query(`
      CREATE TABLE IF NOT EXISTS referrals (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        referrer_id UUID REFERENCES customers(id),
        referred_id UUID REFERENCES customers(id),
        business_id UUID REFERENCES businesses(id),
        amount DECIMAL(10,2),
        status VARCHAR(20) DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT NOW()
      )
    `);

    console.log('Database tables initialized successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
  }
}

// ===== UTILITY ROUTES =====
app.get('/', (req, res) => {
  res.json({ 
    message: 'Referit API Server', 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

app.get('/api', (req, res) => {
  res.json({ 
    message: 'Referit API is running', 
    status: 'ok',
    endpoints: {
      health: '/api/health',
      auth: {
        register: 'POST /api/auth/register',
        login: 'POST /api/auth/login',
        social: 'POST /api/auth/social',
        resetPassword: 'POST /api/auth/reset-password'
      },
      businesses: {
        list: 'GET /api/businesses',
        create: 'POST /api/businesses',
        getById: 'GET /api/businesses/:id'
      },
      customers: {
        list: 'GET /api/customers',
        create: 'POST /api/customers',
        getById: 'GET /api/customers/:id'
      }
    }
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    server: {
      port: port,
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime()
    },
    database: {
      configured: !!connectionString,
      connected: true
    }
  });
});

// ===== AUTHENTICATION ROUTES =====
app.post('/api/auth/register', async (req, res) => {
  try {
    const { email, password, fullName, userType, companyName } = req.body;

    if (!email || !password || !fullName || !userType) {
      return res.status(400).json({ 
        success: false, 
        message: 'Missing required fields: email, password, fullName, userType' 
      });
    }

    const passwordHash = await bcrypt.hash(password, 12);
    let result, user;

    if (userType === 'business') {
      if (!companyName) {
        return res.status(400).json({ 
          success: false, 
          message: 'Company name is required for business accounts' 
        });
      }

      result = await pool.query(
        'INSERT INTO businesses (email, password_hash, full_name, company_name) VALUES ($1, $2, $3, $4) RETURNING id, email, full_name, user_type, company_name',
        [email, passwordHash, fullName, companyName]
      );
    } else {
      result = await pool.query(
        'INSERT INTO customers (email, password_hash, full_name) VALUES ($1, $2, $3) RETURNING id, email, full_name, user_type',
        [email, passwordHash, fullName]
      );
    }

    user = result.rows[0];
    user.accountType = userType;
    const token = jwt.sign(
      { id: user.id, email: user.email, user_type: userType }, 
      process.env.JWT_SECRET || 'default-secret', 
      { expiresIn: '7d' }
    );

    res.status(201).json({ success: true, user, token });
  } catch (error) {
    console.error('Registration error:', error);
    if (error.constraint && error.constraint.includes('email')) {
      res.status(409).json({ success: false, message: 'Email already exists' });
    } else {
      res.status(500).json({ success: false, message: 'Registration failed' });
    }
  }
});

app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ 
        success: false, 
        message: 'Email and password are required' 
      });
    }

    let user, userType;

    // Check customers first
    let result = await pool.query('SELECT * FROM customers WHERE email = $1', [email]);
    if (result.rows.length > 0) {
      user = result.rows[0];
      userType = 'customer';
    } else {
      // Check businesses
      result = await pool.query('SELECT * FROM businesses WHERE email = $1', [email]);
      if (result.rows.length > 0) {
        user = result.rows[0];
        userType = 'business';
      }
    }

    if (!user || !await bcrypt.compare(password, user.password_hash)) {
      return res.status(401).json({ success: false, message: 'Invalid credentials' });
    }

    delete user.password_hash;
    user.user_type = userType;
    user.accountType = userType;

    const token = jwt.sign(
      { id: user.id, email: user.email, user_type: userType }, 
      process.env.JWT_SECRET || 'default-secret', 
      { expiresIn: '7d' }
    );

    res.json({ success: true, user, token });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, message: 'Login failed' });
  }
});

app.post('/api/auth/social', async (req, res) => {
  try {
    const { provider, userData, user_type } = req.body;

    if (!provider || !userData || !userData.email || !user_type) {
      return res.status(400).json({ 
        success: false, 
        message: 'Missing required fields: provider, userData.email, user_type' 
      });
    }

    const table = user_type === 'customer' ? 'customers' : 'businesses';
    let existingUser = await pool.query(`SELECT * FROM ${table} WHERE email = $1`, [userData.email]);

    if (existingUser.rows.length > 0) {
      const user = existingUser.rows[0];
      delete user.password_hash;
      user.user_type = user_type;
      user.accountType = user_type;

      const token = jwt.sign(
        { id: user.id, email: user.email, user_type }, 
        process.env.JWT_SECRET || 'default-secret', 
        { expiresIn: '7d' }
      );

      return res.json({ success: true, user, token });
    }

    // Create new user
    let result;
    if (user_type === 'customer') {
      result = await pool.query(`
        INSERT INTO customers (email, full_name, avatar_url, provider, user_type, email_verified, total_earnings, total_referrals)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id, email, full_name, user_type, avatar_url, provider, email_verified, created_at
      `, [
        userData.email, 
        userData.name || userData.full_name, 
        userData.avatar_url || userData.picture,
        provider, 
        'customer', 
        true, 
        0, 
        0
      ]);
    } else {
      result = await pool.query(`
        INSERT INTO businesses (email, full_name, company_name, avatar_url, provider, user_type, email_verified, subscription_plan, subscription_status)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING id, email, full_name, user_type, company_name, avatar_url, provider, email_verified, created_at
      `, [
        userData.email, 
        userData.name || userData.full_name, 
        userData.company_name || userData.name || userData.full_name,
        userData.avatar_url || userData.picture, 
        provider, 
        'business', 
        true, 
        'free',
        'active'
      ]);
    }

    const user = result.rows[0];
    user.accountType = user_type;
    const token = jwt.sign(
      { id: user.id, email: user.email, user_type }, 
      process.env.JWT_SECRET || 'default-secret', 
      { expiresIn: '7d' }
    );

    res.status(201).json({ success: true, user, token });
  } catch (error) {
    console.error('Social auth error:', error);
    res.status(500).json({ success: false, message: 'Social authentication failed' });
  }
});

app.post('/api/auth/reset-password', async (req, res) => {
  try {
    const { token, email, password } = req.body;

    if (!token || !email || !password) {
      return res.status(400).json({ 
        success: false, 
        message: 'Missing required fields: token, email, password' 
      });
    }

    if (password.length < 8) {
      return res.status(400).json({ 
        success: false, 
        message: 'Password must be at least 8 characters long' 
      });
    }

    let user = null, userType = null, tableName = null;

    // Check customers table
    let result = await pool.query(
      'SELECT id, full_name, reset_token_expires FROM customers WHERE email = $1 AND reset_token = $2',
      [email, token]
    );

    if (result.rows.length > 0) {
      user = result.rows[0];
      userType = 'customer';
      tableName = 'customers';
    } else {
      // Check businesses table
      result = await pool.query(
        'SELECT id, full_name, reset_token_expires FROM businesses WHERE email = $1 AND reset_token = $2',
        [email, token]
      );

      if (result.rows.length > 0) {
        user = result.rows[0];
        userType = 'business';
        tableName = 'businesses';
      }
    }

    if (!user) {
      return res.status(400).json({ success: false, message: 'Invalid or expired reset token' });
    }

    if (user.reset_token_expires && new Date() > new Date(user.reset_token_expires)) {
      return res.status(400).json({ success: false, message: 'Reset token has expired' });
    }

    const passwordHash = await bcrypt.hash(password, 12);
    await pool.query(
      `UPDATE ${tableName} SET password_hash = $1, reset_token = NULL, reset_token_expires = NULL WHERE id = $2`,
      [passwordHash, user.id]
    );

    res.json({ success: true, message: 'Password reset successfully' });
  } catch (error) {
    console.error('Password reset error:', error);
    res.status(500).json({ success: false, message: 'Failed to reset password' });
  }
});

// ===== BUSINESS ROUTES =====
app.get('/api/businesses', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT id, email, full_name, company_name, company_size, industry, 
             website, avatar_url, provider, user_type, subscription_plan, 
             subscription_status, email_verified, created_at
      FROM businesses 
      ORDER BY created_at DESC
    `);

    res.json({ 
      success: true, 
      businesses: result.rows,
      total: result.rows.length 
    });
  } catch (error) {
    console.error('Error fetching businesses:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch businesses', 
      error: error.message 
    });
  }
});

app.post('/api/businesses', async (req, res) => {
  try {
    const {
      email,
      password,
      full_name,
      company_name,
      company_size = '1-10',
      industry = 'Technology',
      website = '',
      provider = 'email',
      user_type = 'business',
      subscription_plan = 'free',
      subscription_status = 'active',
      email_verified = false
    } = req.body;

    if (!email || !full_name || !company_name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: email, full_name, and company_name are required'
      });
    }

    // Check if business already exists
    const existingBusiness = await pool.query(
      'SELECT id FROM businesses WHERE email = $1',
      [email]
    );

    if (existingBusiness.rows.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Business account with this email already exists'
      });
    }

    let passwordHash = null;
    if (password) {
      if (password.length < 8) {
        return res.status(400).json({
          success: false,
          message: 'Password must be at least 8 characters long'
        });
      }
      passwordHash = await bcrypt.hash(password, 12);
    }

    const result = await pool.query(`
      INSERT INTO businesses (
        email, password_hash, full_name, company_name, company_size, 
        industry, website, provider, user_type, subscription_plan, 
        subscription_status, email_verified
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING id, email, full_name, company_name, company_size, industry, 
                website, provider, user_type, subscription_plan, 
                subscription_status, email_verified, created_at
    `, [
      email, passwordHash, full_name, company_name, company_size,
      industry, website, provider, user_type, subscription_plan,
      subscription_status, email_verified
    ]);

    const business = result.rows[0];

    res.status(201).json({
      success: true,
      message: 'Business account created successfully',
      data: { user: business }
    });
  } catch (error) {
    console.error('Business registration failed:', error);
    if (error.constraint && error.constraint.includes('email')) {
      res.status(409).json({
        success: false,
        message: 'Email already exists'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to create business account',
        error: error.message
      });
    }
  }
});

app.get('/api/businesses/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(`
      SELECT id, email, full_name, company_name, company_size, industry, 
             website, avatar_url, provider, user_type, subscription_plan, 
             subscription_status, email_verified, created_at, updated_at
      FROM businesses 
      WHERE id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ 
        success: false, 
        message: 'Business not found' 
      });
    }

    res.json({ 
      success: true, 
      business: result.rows[0] 
    });
  } catch (error) {
    console.error('Error fetching business:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch business', 
      error: error.message 
    });
  }
});

// ===== CUSTOMER ROUTES =====
app.get('/api/customers', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT id, email, full_name, avatar_url, provider, user_type, 
             total_earnings, total_referrals, successful_referrals, tier,
             email_verified, created_at
      FROM customers 
      ORDER BY created_at DESC
    `);

    res.json({ 
      success: true, 
      customers: result.rows,
      total: result.rows.length 
    });
  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch customers', 
      error: error.message 
    });
  }
});

app.post('/api/customers', async (req, res) => {
  try {
    const { 
      email, 
      password, 
      full_name, 
      avatar_url, 
      provider = 'email', 
      user_type = 'customer', 
      total_earnings = 0, 
      total_referrals = 0, 
      email_verified = false, 
      preferences = {} 
    } = req.body;

    if (!email || !full_name) {
      return res.status(400).json({ 
        success: false, 
        message: 'Email and full name are required' 
      });
    }

    let passwordHash = null;
    if (password) {
      if (password.length < 8) {
        return res.status(400).json({ 
          success: false, 
          message: 'Password must be at least 8 characters long' 
        });
      }
      passwordHash = await bcrypt.hash(password, 12);
    }

    const result = await pool.query(`
      INSERT INTO customers (
        email, password_hash, full_name, avatar_url, provider, user_type, 
        total_earnings, total_referrals, email_verified, preferences
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING id, email, full_name, user_type, avatar_url, provider, email_verified, created_at
    `, [
      email, passwordHash, full_name, avatar_url, provider, 
      user_type, total_earnings, total_referrals, 
      email_verified, JSON.stringify(preferences)
    ]);

    const user = result.rows[0];
    const token = jwt.sign(
      { id: user.id, email: user.email, user_type: 'customer' }, 
      process.env.JWT_SECRET || 'default-secret', 
      { expiresIn: '7d' }
    );

    res.status(201).json({ success: true, user, token });
  } catch (error) {
    console.error('Error creating customer:', error);
    if (error.constraint && error.constraint.includes('email')) {
      res.status(409).json({ success: false, message: 'Email already exists' });
    } else {
      res.status(500).json({ 
        success: false, 
        message: 'Failed to create customer account', 
        error: error.message 
      });
    }
  }
});

app.get('/api/customers/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(`
      SELECT id, email, full_name, avatar_url, provider, user_type, 
             total_earnings, total_referrals, successful_referrals, tier,
             email_verified, preferences, created_at, updated_at
      FROM customers 
      WHERE id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ 
        success: false, 
        message: 'Customer not found' 
      });
    }

    const customer = result.rows[0];
    if (customer.preferences && typeof customer.preferences === 'string') {
      customer.preferences = JSON.parse(customer.preferences);
    }

    res.json({ 
      success: true, 
      customer: customer 
    });
  } catch (error) {
    console.error('Error fetching customer:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch customer', 
      error: error.message 
    });
  }
});

// ===== EMAIL ROUTES =====
app.post('/api/send-email', async (req, res) => {
  try {
    const { to, subject, text, html } = req.body;

    if (!to || !subject || (!text && !html)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Missing required fields: to, subject, and text or html' 
      });
    }

    const mailOptions = {
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to,
      subject,
      text,
      html
    };

    const info = await emailTransporter.sendMail(mailOptions);
    res.json({ success: true, messageId: info.messageId });
  } catch (error) {
    console.error('Error sending email:', error);
    res.status(500).json({ success: false, message: 'Failed to send email' });
  }
});

// ===== TEST ROUTES (Development Only) =====
if (process.env.NODE_ENV !== 'production') {
  app.get('/api/test', (req, res) => {
    res.json({ 
      message: 'API routing is working!',
      timestamp: new Date().toISOString(),
      server: 'alive'
    });
  });

  app.post('/api/test', (req, res) => {
    res.json({ 
      message: 'API POST routing is working!',
      body: req.body,
      timestamp: new Date().toISOString()
    });
  });

  app.get('/api/debug-urls', (req, res) => {
    const replId = process.env.REPL_ID;
    const replOwner = process.env.REPL_OWNER;
    const replSlug = process.env.REPL_SLUG;
    const replUrl = process.env.REPL_URL;

    res.json({
      message: 'Debug information',
      environment: {
        REPL_ID: replId || 'not-set',
        REPL_OWNER: replOwner || 'not-set', 
        REPL_SLUG: replSlug || 'not-set',
        REPL_URL: replUrl || 'not-set'
      },
      request: {
        origin: req.get('Origin'),
        host: req.get('Host'),
        url: req.url,
        protocol: req.protocol
      }
    });
  });
}

// ===== 404 HANDLER =====
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.path,
    method: req.method,
    availableEndpoints: [
      'GET /api/health',
      'POST /api/auth/register',
      'POST /api/auth/login',
      'GET /api/businesses',
      'POST /api/businesses',
      'GET /api/customers',
      'POST /api/customers'
    ]
  });
});

// ===== ERROR HANDLER =====
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ 
    success: false, 
    message: 'Internal server error',
    ...(process.env.NODE_ENV !== 'production' && { error: err.message })
  });
});

// ===== SERVER STARTUP =====
initDatabase().then(() => {
  const server = app.listen(port, '0.0.0.0', () => {
    console.log(`🚀 Server successfully started on port ${port}`);
    console.log(`📡 Local API: http://0.0.0.0:${port}/api`);
    console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log('🔒 CORS: Configured for Replit domains');
    console.log('✅ Database: Connected and initialized');

    if (process.env.SMTP_USER && process.env.SMTP_PASS) {
      console.log('📧 Email: Configured');
    } else {
      console.log('⚠️  Email: Not configured');
    }
  });

  server.on('error', (err) => {
    console.error('❌ Server startup error:', err);
    if (err.code === 'EADDRINUSE') {
      console.error(`❌ Port ${port} is already in use!`);
      process.exit(1);
    }
  });

  process.on('SIGTERM', () => {
    console.log('🛑 SIGTERM received, shutting down gracefully');
    server.close(() => {
      console.log('💤 Server closed');
      pool.end();
      process.exit(0);
    });
  });
});