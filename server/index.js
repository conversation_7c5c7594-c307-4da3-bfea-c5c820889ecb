const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const nodemailer = require('nodemailer');
const crypto = require('crypto');
const emailTemplates = require('./emailTemplates');
require('dotenv').config();

const app = express();
const port = 5000;

// ===== SECURITY & PERFORMANCE CONFIGURATION =====
app.set('trust proxy', true);
app.disable('x-powered-by');

// ===== CORS CONFIGURATION =====
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);

    // Allow all origins in development, restrict in production
    const allowedOrigins = [
      /\.replit\.dev$/,
      /localhost:\d+$/,
      /127\.0\.0\.1:\d+$/,
      'https://referit.com',
      'https://www.referit.com'
    ];

    const isAllowed = allowedOrigins.some(pattern => {
      if (pattern instanceof RegExp) {
        return pattern.test(origin);
      }
      return pattern === origin;
    });

    if (isAllowed) {
      callback(null, true);
    } else {
      console.warn('CORS blocked origin:', origin);
      callback(null, true); // Allow all in development
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control',
    'Pragma'
  ],
  maxAge: 86400, // 24 hours
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));

// ===== EXPLICIT OPTIONS HANDLER =====
app.options('*', cors(corsOptions));

// ===== BODY PARSING MIDDLEWARE =====
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// ===== REQUEST LOGGING MIDDLEWARE (SIMPLIFIED) =====
app.use((req, res, next) => {
  if (process.env.NODE_ENV !== 'production') {
    console.log(`${new Date().toISOString()} ${req.method} ${req.path}`);
  }
  next();
});

// ===== DATABASE CONFIGURATION =====
let connectionString = process.env.DATABASE_URL || process.env.REPLIT_DB_URL;

if (connectionString && connectionString.includes('supabase.co')) {
  try {
    const url = new URL(connectionString);
    const decodedPassword = decodeURIComponent(url.password);
    connectionString = `postgresql://${url.username}:${encodeURIComponent(decodedPassword)}@${url.hostname}:${url.port}${url.pathname}${url.search}`;
  } catch (parseError) {
    console.error('Failed to parse connection string:', parseError.message);
  }
}
console.log('Database connection string:', connectionString);

const pool = new Pool({
  connectionString: connectionString,
  ssl: connectionString && connectionString.includes('supabase.co') ? { 
    rejectUnauthorized: false 
  } : false,
  max: 20,
  min: 2,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 20000,
  acquireTimeoutMillis: 20000
});

// ===== EMAIL CONFIGURATION =====
const emailTransporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp-relay.brevo.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

// ===== DATABASE INITIALIZATION =====
async function initDatabase() {
  try {
    await pool.query('SELECT NOW()');
    console.log('Database connected successfully');

    // Create customers table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS customers (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash TEXT,
        full_name VARCHAR(255) NOT NULL,
        avatar_url TEXT,
        provider VARCHAR(50) DEFAULT 'email',
        email_verified BOOLEAN DEFAULT false,
        user_type VARCHAR(20) DEFAULT 'customer',
        phone VARCHAR(20),
        date_of_birth DATE,
        gender VARCHAR(10),
        address JSONB,
        preferences JSONB DEFAULT '{}',
        total_earnings DECIMAL(10,2) DEFAULT 0,
        total_referrals INTEGER DEFAULT 0,
        successful_referrals INTEGER DEFAULT 0,
        tier VARCHAR(20) DEFAULT 'bronze',
        social_links JSONB DEFAULT '{}',
        reset_token TEXT,
        reset_token_expires TIMESTAMP,
        verification_token TEXT,
        verification_token_expires TIMESTAMP,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);

    // Create businesses table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS businesses (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash TEXT,
        full_name VARCHAR(255) NOT NULL,
        avatar_url TEXT,
        provider VARCHAR(50) DEFAULT 'email',
        email_verified BOOLEAN DEFAULT false,
        user_type VARCHAR(20) DEFAULT 'business',
        company_name VARCHAR(255) NOT NULL,
        company_size VARCHAR(50),
        industry VARCHAR(100),
        website VARCHAR(255),
        phone VARCHAR(20),
        subscription_plan VARCHAR(20) DEFAULT 'free',
        subscription_status VARCHAR(20) DEFAULT 'active',
        billing_address JSONB,
        reset_token TEXT,
        reset_token_expires TIMESTAMP,
        verification_token TEXT,
        verification_token_expires TIMESTAMP,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);

    // Create business_profiles table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS business_profiles (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
        business_name VARCHAR(255) NOT NULL,
        logo_url TEXT,
        primary_address JSONB NOT NULL,
        website VARCHAR(255),
        description TEXT,
        phone_number VARCHAR(20) NOT NULL,
        contact_email VARCHAR(255),
        business_category VARCHAR(100),
        business_tags TEXT[],
        business_type VARCHAR(50) DEFAULT 'physical',
        service_area VARCHAR(255),
        primary_hours JSONB,
        timezone VARCHAR(100),
        is_open_24_hours BOOLEAN DEFAULT false,
        location_limit INTEGER DEFAULT 2,
        max_free_locations INTEGER DEFAULT 2,
        primary_profile_locked BOOLEAN DEFAULT true,
        formatted_address TEXT,
        coordinates JSONB,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);

    // Create business_locations table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS business_locations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
        profile_id UUID REFERENCES business_profiles(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        address JSONB NOT NULL,
        phone VARCHAR(20),
        website VARCHAR(255),
        use_primary_website BOOLEAN DEFAULT true,
        operating_hours JSONB,
        use_primary_hours BOOLEAN DEFAULT true,
        business_type VARCHAR(50),
        business_category VARCHAR(100),
        business_tags TEXT[],
        use_primary_category BOOLEAN DEFAULT true,
        use_primary_tags BOOLEAN DEFAULT true,
        is_primary BOOLEAN DEFAULT false,
        can_be_deleted BOOLEAN DEFAULT true,
        coordinates JSONB,
        formatted_address TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);

    await pool.query(`
      CREATE TABLE IF NOT EXISTS referrals (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        referrer_id UUID REFERENCES customers(id),
        referred_id UUID REFERENCES customers(id),
        business_id UUID REFERENCES businesses(id),
        amount DECIMAL(10,2),
        status VARCHAR(20) DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT NOW()
      )
    `);

    console.log('Database tables initialized successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
  }
}

// ===== UTILITY ROUTES =====
app.get('/', (req, res) => {
  res.json({ 
    message: 'Referit API Server', 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

app.get('/api', (req, res) => {
  res.json({ 
    message: 'Referit API is running', 
    status: 'ok',
    endpoints: {
      health: '/api/health',
      auth: {
        register: 'POST /api/auth/register',
        login: 'POST /api/auth/login',
        social: 'POST /api/auth/social',
        resetPassword: 'POST /api/auth/reset-password'
      },
      businesses: {
        list: 'GET /api/businesses',
        create: 'POST /api/businesses',
        getById: 'GET /api/businesses/:id'
      },
      customers: {
        list: 'GET /api/customers',
        create: 'POST /api/customers',
        getById: 'GET /api/customers/:id'
      }
    }
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    server: {
      port: port,
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime()
    },
    database: {
      configured: !!connectionString,
      connected: true
    }
  });
});

// ===== AUTHENTICATION ROUTES =====
app.post('/api/auth/register', async (req, res) => {
  try {
    const { email, password, fullName, userType, companyName } = req.body;

    if (!email || !password || !fullName || !userType) {
      return res.status(400).json({ 
        success: false, 
        message: 'Missing required fields: email, password, fullName, userType' 
      });
    }

    const passwordHash = await bcrypt.hash(password, 12);
    let result, user;

    if (userType === 'business') {
      // Use fullName as company name if not provided
      const businessCompanyName = companyName || fullName;

      result = await pool.query(
        'INSERT INTO businesses (email, password_hash, full_name, company_name) VALUES ($1, $2, $3, $4) RETURNING id, email, full_name, user_type, company_name',
        [email, passwordHash, fullName, businessCompanyName]
      );
    } else {
      result = await pool.query(
        'INSERT INTO customers (email, password_hash, full_name) VALUES ($1, $2, $3) RETURNING id, email, full_name, user_type',
        [email, passwordHash, fullName]
      );
    }

    user = result.rows[0];
    user.accountType = userType;
    const token = jwt.sign(
      { id: user.id, email: user.email, user_type: userType }, 
      process.env.JWT_SECRET || 'default-secret', 
      { expiresIn: '7d' }
    );

    res.status(201).json({ success: true, user, token });
  } catch (error) {
    console.error('Registration error:', error);
    if (error.constraint && error.constraint.includes('email')) {
      res.status(409).json({ success: false, message: 'Email already exists' });
    } else {
      res.status(500).json({ success: false, message: 'Registration failed' });
    }
  }
});

app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ 
        success: false, 
        message: 'Email and password are required' 
      });
    }

    let user, userType;

    // Check customers first
    let result = await pool.query('SELECT * FROM customers WHERE email = $1', [email]);
    if (result.rows.length > 0) {
      user = result.rows[0];
      userType = 'customer';
    } else {
      // Check businesses
      result = await pool.query('SELECT * FROM businesses WHERE email = $1', [email]);
      if (result.rows.length > 0) {
        user = result.rows[0];
        userType = 'business';
      }
    }

    if (!user || !await bcrypt.compare(password, user.password_hash)) {
      return res.status(401).json({ success: false, message: 'Invalid credentials' });
    }

    delete user.password_hash;
    user.user_type = userType;
    user.accountType = userType;

    const token = jwt.sign(
      { id: user.id, email: user.email, user_type: userType }, 
      process.env.JWT_SECRET || 'default-secret', 
      { expiresIn: '7d' }
    );

    res.json({ success: true, user, token });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, message: 'Login failed' });
  }
});

app.post('/api/auth/social', async (req, res) => {
  try {
    const { provider, userData, user_type } = req.body;

    if (!provider || !userData || !userData.email || !user_type) {
      return res.status(400).json({ 
        success: false, 
        message: 'Missing required fields: provider, userData.email, user_type' 
      });
    }

    const table = user_type === 'customer' ? 'customers' : 'businesses';
    let existingUser = await pool.query(`SELECT * FROM ${table} WHERE email = $1`, [userData.email]);

    if (existingUser.rows.length > 0) {
      const user = existingUser.rows[0];
      delete user.password_hash;
      user.user_type = user_type;
      user.accountType = user_type;

      const token = jwt.sign(
        { id: user.id, email: user.email, user_type }, 
        process.env.JWT_SECRET || 'default-secret', 
        { expiresIn: '7d' }
      );

      return res.json({ success: true, user, token });
    }

    // Create new user
    let result;
    if (user_type === 'customer') {
      result = await pool.query(`
        INSERT INTO customers (email, full_name, avatar_url, provider, user_type, email_verified, total_earnings, total_referrals)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id, email, full_name, user_type, avatar_url, provider, email_verified, created_at
      `, [
        userData.email, 
        userData.name || userData.full_name, 
        userData.avatar_url || userData.picture,
        provider, 
        'customer', 
        true, 
        0, 
        0
      ]);
    } else {
      result = await pool.query(`
        INSERT INTO businesses (email, full_name, company_name, avatar_url, provider, user_type, email_verified, subscription_plan, subscription_status)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING id, email, full_name, user_type, company_name, avatar_url, provider, email_verified, created_at
      `, [
        userData.email, 
        userData.name || userData.full_name, 
        userData.company_name || userData.name || userData.full_name,
        userData.avatar_url || userData.picture, 
        provider, 
        'business', 
        true, 
        'free',
        'active'
      ]);
    }

    const user = result.rows[0];
    user.accountType = user_type;
    const token = jwt.sign(
      { id: user.id, email: user.email, user_type }, 
      process.env.JWT_SECRET || 'default-secret', 
      { expiresIn: '7d' }
    );

    res.status(201).json({ success: true, user, token });
  } catch (error) {
    console.error('Social auth error:', error);
    res.status(500).json({ success: false, message: 'Social authentication failed' });
  }
});

app.post('/api/auth/reset-password', async (req, res) => {
  try {
    const { token, email, password } = req.body;

    if (!token || !email || !password) {
      return res.status(400).json({ 
        success: false, 
        message: 'Missing required fields: token, email, password' 
      });
    }

    if (password.length < 8) {
      return res.status(400).json({ 
        success: false, 
        message: 'Password must be at least 8 characters long' 
      });
    }

    let user = null, userType = null, tableName = null;

    // Check customers table
    let result = await pool.query(
      'SELECT id, full_name, reset_token_expires FROM customers WHERE email = $1 AND reset_token = $2',
      [email, token]
    );

    if (result.rows.length > 0) {
      user = result.rows[0];
      userType = 'customer';
      tableName = 'customers';
    } else {
      // Check businesses table
      result = await pool.query(
        'SELECT id, full_name, reset_token_expires FROM businesses WHERE email = $1 AND reset_token = $2',
        [email, token]
      );

      if (result.rows.length > 0) {
        user = result.rows[0];
        userType = 'business';
        tableName = 'businesses';
      }
    }

    if (!user) {
      return res.status(400).json({ success: false, message: 'Invalid or expired reset token' });
    }

    if (user.reset_token_expires && new Date() > new Date(user.reset_token_expires)) {
      return res.status(400).json({ success: false, message: 'Reset token has expired' });
    }

    const passwordHash = await bcrypt.hash(password, 12);
    await pool.query(
      `UPDATE ${tableName} SET password_hash = $1, reset_token = NULL, reset_token_expires = NULL WHERE id = $2`,
      [passwordHash, user.id]
    );

    res.json({ success: true, message: 'Password reset successfully' });
  } catch (error) {
    console.error('Password reset error:', error);
    res.status(500).json({ success: false, message: 'Failed to reset password' });
  }
});

// ===== BUSINESS ROUTES =====
app.get('/api/businesses', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT id, email, full_name, company_name, company_size, industry, 
             website, avatar_url, provider, user_type, subscription_plan, 
             subscription_status, email_verified, created_at
      FROM businesses 
      ORDER BY created_at DESC
    `);

    res.json({ 
      success: true, 
      businesses: result.rows,
      total: result.rows.length 
    });
  } catch (error) {
    console.error('Error fetching businesses:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch businesses', 
      error: error.message 
    });
  }
});

app.post('/api/businesses', async (req, res) => {
  try {
    const {
      email,
      password,
      full_name,
      company_name,
      company_size = '1-10',
      industry = 'Technology',
      website = '',
      provider = 'email',
      user_type = 'business',
      subscription_plan = 'free',
      subscription_status = 'active',
      email_verified = false
    } = req.body;

    if (!email || !full_name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: email and full_name are required'
      });
    }

    // Check if business already exists
    const existingBusiness = await pool.query(
      'SELECT id FROM businesses WHERE email = $1',
      [email]
    );

    if (existingBusiness.rows.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Business account with this email already exists'
      });
    }

    let passwordHash = null;
    if (password) {
      if (password.length < 8) {
        return res.status(400).json({
          success: false,
          message: 'Password must be at least 8 characters long'
        });
      }
      passwordHash = await bcrypt.hash(password, 12);
    }

    // Use full_name as company_name if not provided
    const businessCompanyName = company_name || full_name;

    const result = await pool.query(`
      INSERT INTO businesses (
        email, password_hash, full_name, company_name, company_size, 
        industry, website, provider, user_type, subscription_plan, 
        subscription_status, email_verified
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING id, email, full_name, company_name, company_size, industry, 
                website, provider, user_type, subscription_plan, 
                subscription_status, email_verified, created_at
    `, [
      email, passwordHash, full_name, businessCompanyName, company_size,
      industry, website, provider, user_type, subscription_plan,
      subscription_status, email_verified
    ]);

    const business = result.rows[0];

    res.status(201).json({
      success: true,
      message: 'Business account created successfully',
      data: { user: business }
    });
  } catch (error) {
    console.error('Business registration failed:', error);
    if (error.constraint && error.constraint.includes('email')) {
      res.status(409).json({
        success: false,
        message: 'Email already exists'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to create business account',
        error: error.message
      });
    }
  }
});

app.get('/api/businesses/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(`
      SELECT id, email, full_name, company_name, company_size, industry, 
             website, avatar_url, provider, user_type, subscription_plan, 
             subscription_status, email_verified, created_at, updated_at
      FROM businesses 
      WHERE id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ 
        success: false, 
        message: 'Business not found' 
      });
    }

    res.json({ 
      success: true, 
      business: result.rows[0] 
    });
  } catch (error) {
    console.error('Error fetching business:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch business', 
      error: error.message 
    });
  }
});

// ===== CUSTOMER ROUTES =====
// ===== CUSTOMER MANAGEMENT ROUTES =====

// Global customer creation (for user registration)
app.post('/api/customers', async (req, res) => {
  try {
    console.log('📡 POST /api/customers - Creating new customer (global)');
    console.log('📝 Request body:', JSON.stringify(req.body, null, 2));

    const { email, password, full_name, fullName, phone, avatar_url, provider, referral_code, preferences, address, tags, business_id } = req.body;

    // Use fullName if full_name is not provided (for consistency with frontend)
    const customerName = full_name || fullName;

    if (!customerName) {
      return res.status(400).json({
        success: false,
        message: 'Full name is required'
      });
    }

    // Check if either email or phone is provided
    if (!email && !phone) {
      return res.status(400).json({
        success: false,
        message: 'Either email or phone number is required'
      });
    }

    // Check if customer exists (by email if provided)
    if (email) {
      const existingCustomer = await pool.query('SELECT id FROM customers WHERE email = $1', [email]);
      if (existingCustomer.rows.length > 0) {
        return res.status(409).json({
          success: false,
          message: 'Customer with this email already exists'
        });
      }
    }

    // Hash password if provided
    let hashedPassword = null;
    if (password) {
      hashedPassword = await bcrypt.hash(password, 10);
    }

    // Generate referral code if not provided
    const finalReferralCode = referral_code || Math.random().toString(36).substr(2, 8).toUpperCase();

    // Insert customer with optional business_id
    const result = await pool.query(
      `INSERT INTO customers (email, password_hash, full_name, phone, avatar_url, provider, referral_code, email_verified, preferences, business_id, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
       RETURNING id, email, full_name, phone, avatar_url, provider, referral_code, email_verified, preferences, business_id, created_at`,
      [
        email || null, 
        hashedPassword, 
        customerName, 
        phone || null, 
        avatar_url || null, 
        provider || 'manual', 
        finalReferralCode, 
        provider !== 'email' && provider !== 'manual', 
        JSON.stringify(preferences || {}),
        business_id || null
      ]
    );

    const customer = result.rows[0];
    console.log('✅ Customer created successfully:', customer.id);

    // Generate JWT token if this is for authentication
    let token = null;
    if (provider && provider !== 'manual') {
      token = jwt.sign(
        { 
          id: customer.id, 
          email: customer.email, 
          user_type: 'customer',
          full_name: customer.full_name 
        },
        process.env.JWT_SECRET || 'default-secret',
        { expiresIn: '24h' }
      );
    }

    res.status(201).json({
      success: true,
      message: 'Customer created successfully',
      customer: {
        id: customer.id,
        email: customer.email,
        full_name: customer.full_name,
        phone: customer.phone,
        avatar_url: customer.avatar_url,
        provider: customer.provider,
        referral_code: customer.referral_code,
        email_verified: customer.email_verified,
        preferences: customer.preferences,
        business_id: customer.business_id,
        user_type: 'customer',
        created_at: customer.created_at
      },
      user: token ? {
        id: customer.id,
        email: customer.email,
        full_name: customer.full_name,
        phone: customer.phone,
        avatar_url: customer.avatar_url,
        provider: customer.provider,
        referral_code: customer.referral_code,
        email_verified: customer.email_verified,
        preferences: customer.preferences,
        business_id: customer.business_id,
        user_type: 'customer',
        created_at: customer.created_at
      } : undefined,
      token
    });
  } catch (error) {
    console.error('❌ Error creating customer:', error);

    // Handle specific database errors
    if (error.constraint && error.constraint.includes('email')) {
      return res.status(409).json({
        success: false,
        message: 'A customer with this email already exists'
      });
    }

    if (error.code === '23505') { // PostgreSQL unique violation
      return res.status(409).json({
        success: false,
        message: 'Customer with this information already exists'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create customer: ' + error.message
    });
  }
});

// Get all customers (for business dashboard) - returns empty array instead of error when no customers exist
app.get('/api/customers', async (req, res) => {
  try {
    console.log('📡 GET /api/customers - Fetching all customers');
    const result = await pool.query('SELECT * FROM customers ORDER BY created_at DESC');
    console.log('📊 Found', result.rows.length, 'customers');

    // Always return success, even with empty array
    res.json({
      success: true,
      customers: result.rows,
      total: result.rows.length
    });
  } catch (error) {
    console.error('❌ Error fetching customers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customers',
      customers: [] // Return empty array on error
    });
  }
});

// ===== BUSINESS-SPECIFIC CUSTOMER ROUTES =====

// Get customers for a specific business
app.get('/api/businesses/:businessId/customers', async (req, res) => {
  try {
    const { businessId } = req.params;
    console.log('📡 GET /api/businesses/:businessId/customers - Fetching customers for business:', businessId);

    const result = await pool.query(
      'SELECT * FROM customers WHERE business_id = $1 ORDER BY created_at DESC',
      [businessId]
    );

    console.log('📊 Found', result.rows.length, 'customers for business:', businessId);

    res.json({
      success: true,
      customers: result.rows,
      total: result.rows.length,
      business_id: businessId
    });
  } catch (error) {
    console.error('❌ Error fetching business customers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch business customers',
      customers: []
    });
  }
});

// Create customer for a specific business
app.post('/api/businesses/:businessId/customers', async (req, res) => {
  try {
    const { businessId } = req.params;
    console.log('📡 POST /api/businesses/:businessId/customers - Creating customer for business:', businessId);
    console.log('📝 Request body:', JSON.stringify(req.body, null, 2));

    // Verify business exists (for production, you'd check this properly)
    if (!businessId || businessId === 'undefined' || businessId === 'null') {
      return res.status(400).json({
        success: false,
        message: 'Valid business ID is required'
      });
    }

    const { email, full_name, fullName, phone, address, tags } = req.body;

    // Use fullName if full_name is not provided (for consistency with frontend)
    const customerName = full_name || fullName;

    if (!customerName) {
      return res.status(400).json({
        success: false,
        message: 'Full name is required'
      });
    }

    // Check if either email or phone is provided
    if (!email && !phone) {
      return res.status(400).json({
        success: false,
        message: 'Either email or phone number is required'
      });
    }

    // Check if customer exists for this business (by email if provided)
    if (email) {
      const existingCustomer = await pool.query(
        'SELECT id FROM customers WHERE email = $1 AND (business_id = $2 OR business_id IS NULL)',
        [email, businessId]
      );
      if (existingCustomer.rows.length > 0) {
        return res.status(409).json({
          success: false,
          message: 'Customer with this email already exists for this business'
        });
      }
    }

    // Insert customer for this business
    const result = await pool.query(
      `INSERT INTO customers (email, full_name, phone, business_id, provider, email_verified, preferences, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
       RETURNING id, email, full_name, phone, business_id, provider, email_verified, preferences, created_at`,
      [
        email || null, 
        customerName, 
        phone || null, 
        businessId,
        'manual',
        false,
        JSON.stringify({ address: address || null, tags: tags || [] })
      ]
    );

    const customer = result.rows[0];
    console.log('✅ Business customer created successfully:', customer.id, 'for business:', businessId);

    res.status(201).json({
      success: true,
      message: 'Customer added to business successfully',
      customer: {
        id: customer.id,
        email: customer.email,
        full_name: customer.full_name,
        phone: customer.phone,
        business_id: customer.business_id,
        provider: customer.provider,
        email_verified: customer.email_verified,
        preferences: customer.preferences,
        created_at: customer.created_at
      }
    });
  } catch (error) {
    console.error('❌ Error creating business customer:', error);

    if (error.code === '23505') { // PostgreSQL unique violation
      return res.status(409).json({
        success: false,
        message: 'Customer with this information already exists for this business'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create customer for business: ' + error.message
    });
  }
});

app.get('/api/customers/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(`
      SELECT id, email, full_name, avatar_url, provider, user_type, 
             total_earnings, total_referrals, successful_referrals, tier,
             email_verified, preferences, created_at, updated_at
      FROM customers 
      WHERE id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ 
        success: false, 
        message: 'Customer not found' 
      });
    }

    const customer = result.rows[0];
    if (customer.preferences && typeof customer.preferences === 'string') {
      customer.preferences = JSON.parse(customer.preferences);
    }

    res.json({ 
      success: true, 
      customer: customer 
    });
  } catch (error) {
    console.error('Error fetching customer:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch customer', 
      error: error.message 
    });
  }
});

// Update customer (global)
app.put('/api/customers/:id', async (req, res) => {
  try {
    console.log('📡 PUT /api/customers/:id - Updating customer', req.params.id);
    const { id } = req.params;
    const { full_name, fullName, email, phone, preferences } = req.body;

    // Use fullName if full_name is not provided (for consistency with frontend)
    const customerName = full_name || fullName;

    const result = await pool.query(
      `UPDATE customers 
       SET full_name = $1, email = $2, phone = $3, preferences = $4, updated_at = NOW()
       WHERE id = $5
       RETURNING *`,
      [customerName, email, phone, JSON.stringify(preferences || {}), id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    console.log('✅ Customer updated successfully:', id);
    res.json({
      success: true,
      message: 'Customer updated successfully',
      customer: result.rows[0]
    });
  } catch (error) {
    console.error('❌ Error updating customer:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update customer'
    });
  }
});

// Delete customer (global)
app.delete('/api/customers/:id', async (req, res) => {
  try {
    console.log('📡 DELETE /api/customers/:id - Deleting customer', req.params.id);
    const { id } = req.params;

    const result = await pool.query(
      'DELETE FROM customers WHERE id = $1 RETURNING full_name',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    console.log('✅ Customer deleted successfully:', id);
    res.json({
      success: true,
      message: `Customer ${result.rows[0].full_name} has been permanently deleted`
    });
  } catch (error) {
    console.error('❌ Error deleting customer:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete customer'
    });
  }
});

// ===== BUSINESS PROFILE ROUTES =====

// Create or update business profile
app.post('/api/businesses/:businessId/profile', async (req, res) => {
  try {
    const { businessId } = req.params;
    console.log('📡 POST /api/businesses/:businessId/profile - Creating/updating business profile for:', businessId);
    console.log('📝 Profile data received:', JSON.stringify(req.body, null, 2));
    console.log('🔑 Authorization header:', req.get('Authorization') ? 'Present' : 'Missing');
    console.log('📋 Request headers:', JSON.stringify(req.headers, null, 2));

    const {
      businessName,
      primaryAddress,
      website,
      description,
      phoneNumber,
      logoUrl,
      contactEmail,
      locations,
      businessCategory,
      businessTags,
      businessType,
      serviceArea,
      primaryHours,
      primaryAddressCoordinates,
      formattedAddress,
      timezone,
      isOpen24Hours,
      subscriptionPlan,
      locationLimit,
      maxFreeLocations,
      primaryProfileLocked
    } = req.body;

    if (!businessName || !phoneNumber || !businessCategory) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: businessName, phoneNumber, and businessCategory are required'
      });
    }

    // Basic auth validation - for now just check if business ID is valid
    if (!businessId || businessId === 'undefined' || businessId === 'null') {
      console.error('❌ Invalid business ID provided:', businessId);
      return res.status(400).json({
        success: false,
        message: 'Invalid business ID provided'
      });
    }

    console.log('🔍 Validating business ID:', businessId);

    // Verify business exists
    const businessExists = await pool.query(
      'SELECT id, email FROM businesses WHERE id = $1',
      [businessId]
    );

    if (businessExists.rows.length === 0) {
      console.error('❌ Business not found with ID:', businessId);
      console.error('❌ Available businesses:');
      const allBusinesses = await pool.query('SELECT id, email FROM businesses LIMIT 5');
      console.error(allBusinesses.rows);
      return res.status(404).json({
        success: false,
        message: `Business not found with ID: ${businessId}`
      });
    }

    console.log('✅ Business found:', businessExists.rows[0].email);

    // Check if profile already exists
    const existingProfile = await pool.query(
      'SELECT id FROM business_profiles WHERE business_id = $1',
      [businessId]
    );

    let result;
    if (existingProfile.rows.length > 0) {
      // Update existing profile
      result = await pool.query(`
        UPDATE business_profiles SET
          business_name = $2,
          logo_url = $3,
          primary_address = $4,
          website = $5,
          description = $6,
          phone_number = $7,
          contact_email = $8,
          business_category = $9,
          business_tags = $10,
          business_type = $11,
          service_area = $12,
          primary_hours = $13,
          timezone = $14,
          is_open_24_hours = $15,
          location_limit = $16,
          max_free_locations = $17,
          primary_profile_locked = $18,
          formatted_address = $19,
          coordinates = $20,
          updated_at = NOW()
        WHERE business_id = $1
        RETURNING *
      `, [
        businessId, businessName, logoUrl, JSON.stringify({
          street_address: primaryAddress,
          formatted_address: formattedAddress,
          coordinates: primaryAddressCoordinates,
          service_area: serviceArea
        }), website, description, phoneNumber, contactEmail,
        businessCategory, businessTags, businessType, serviceArea,
        JSON.stringify(primaryHours), timezone, isOpen24Hours,
        locationLimit || 2, maxFreeLocations || 2, primaryProfileLocked || true,
        formattedAddress, JSON.stringify(primaryAddressCoordinates)
      ]);
    } else {
      // Create new profile
      result = await pool.query(`
        INSERT INTO business_profiles (
          business_id, business_name, logo_url, primary_address, website,
          description, phone_number, contact_email, business_category,
          business_tags, business_type, service_area, primary_hours,
          timezone, is_open_24_hours, location_limit, max_free_locations,
          primary_profile_locked, formatted_address, coordinates
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
        RETURNING *
      `, [
        businessId, businessName, logoUrl, JSON.stringify({
          street_address: primaryAddress,
          formatted_address: formattedAddress,
          coordinates: primaryAddressCoordinates,
          service_area: serviceArea
        }), website, description, phoneNumber, contactEmail,
        businessCategory, businessTags, businessType, serviceArea,
        JSON.stringify(primaryHours), timezone, isOpen24Hours,
        locationLimit || 2, maxFreeLocations || 2, primaryProfileLocked || true,
        formattedAddress, JSON.stringify(primaryAddressCoordinates)
      ]);
    }

    // Handle locations if provided
    if (locations && locations.length > 0) {
      // First, delete existing locations for this business profile
      await pool.query(
        'DELETE FROM business_locations WHERE business_id = $1',
        [businessId]
      );

      // Insert new locations
      for (const location of locations) {
        await pool.query(`
          INSERT INTO business_locations (
            business_id, profile_id, name, address, phone, website,
            use_primary_website, operating_hours, use_primary_hours,
            business_type, business_category, business_tags,
            use_primary_category, use_primary_tags, is_primary,
            can_be_deleted, coordinates, formatted_address
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
        `, [
          businessId, result.rows[0].id, location.name,
          JSON.stringify({
            street_address: location.address,
            address_line_2: location.addressLine2,
            city: location.city,
            state: location.state,
            postal_code: location.zipCode,
            country: location.country || 'United States'
          }),
          location.phone, location.website, location.usePrimaryWebsite,
          JSON.stringify(location.operatingHours), location.usePrimaryHours,
          location.businessType, location.businessCategory,
          location.businessTags, location.usePrimaryCategory,
          location.usePrimaryTags, location.isPrimary || false,
          location.canBeDeleted !== false, JSON.stringify(location.coordinates),
          location.formattedAddress
        ]);
      }
    }

    const profile = result.rows[0];
    console.log('✅ Business profile saved successfully:', profile.id);

    res.status(201).json({
      success: true,
      message: 'Business profile saved successfully',
      profile: profile
    });

  } catch (error) {
    console.error('❌ Error saving business profile:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save business profile: ' + error.message
    });
  }
});

// Get business profile
app.get('/api/businesses/:businessId/profile', async (req, res) => {
  try {
    const { businessId } = req.params;
    console.log('📡 GET /api/businesses/:businessId/profile - Fetching profile for:', businessId);

    const profileResult = await pool.query(`
      SELECT bp.*, bl.* FROM business_profiles bp
      LEFT JOIN business_locations bl ON bp.id = bl.profile_id
      WHERE bp.business_id = $1
    `, [businessId]);

    if (profileResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Business profile not found'
      });
    }

    // Group locations with profile
    const profile = profileResult.rows[0];
    const locations = profileResult.rows
      .filter(row => row.name) // Filter out null location data
      .map(row => ({
        id: row.id,
        name: row.name,
        address: row.address,
        phone: row.phone,
        website: row.website,
        usePrimaryWebsite: row.use_primary_website,
        operatingHours: row.operating_hours,
        usePrimaryHours: row.use_primary_hours,
        businessType: row.business_type,
        businessCategory: row.business_category,
        businessTags: row.business_tags,
        usePrimaryCategory: row.use_primary_category,
        usePrimaryTags: row.use_primary_tags,
        isPrimary: row.is_primary,
        canBeDeleted: row.can_be_deleted,
        coordinates: row.coordinates,
        formattedAddress: row.formatted_address
      }));

    res.json({
      success: true,
      profile: {
        ...profile,
        locations: locations
      }
    });

  } catch (error) {
    console.error('❌ Error fetching business profile:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch business profile'
    });
  }
});

// ===== FALLBACK CUSTOMER ENDPOINT FOR DEMO =====
// This endpoint creates customers without requiring a specific business ID
app.post('/api/demo/customers', async (req, res) => {
  try {
    console.log('📡 POST /api/demo/customers - Creating demo customer');
    console.log('📝 Request body:', JSON.stringify(req.body, null, 2));

    const { email, full_name, fullName, phone, address, tags } = req.body;
    const customerName = full_name || fullName;

    if (!customerName) {
      return res.status(400).json({
        success: false,
        message: 'Full name is required'
      });
    }

    if (!email && !phone) {
      return res.status(400).json({
        success: false,
        message: 'Either email or phone number is required'
      });
    }

    // Create customer without business association for demo
    const result = await pool.query(
      `INSERT INTO customers (email, full_name, phone, provider, email_verified, preferences, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
       RETURNING id, email, full_name, phone, provider, email_verified, preferences, created_at`,
      [
        email || null, 
        customerName, 
        phone || null, 
        'manual',
        false,
        JSON.stringify({ address: address || null, tags: tags || [] })
      ]
    );

    const customer = result.rows[0];
    console.log('✅ Demo customer created successfully:', customer.id);

    res.status(201).json({
      success: true,
      message: 'Customer added successfully',
      customer: customer
    });
  } catch (error) {
    console.error('❌ Error creating demo customer:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create customer: ' + error.message
    });
  }
});



// ===== BUSINESS-SPECIFIC CUSTOMER UPDATE/DELETE =====

// Update customer for a specific business
app.put('/api/businesses/:businessId/customers/:id', async (req, res) => {
  try {
    const { businessId, id } = req.params;
    console.log('📡 PUT /api/businesses/:businessId/customers/:id - Updating business customer', id, 'for business', businessId);

    const { full_name, fullName, email, phone, preferences } = req.body;
    const customerName = full_name || fullName;

    const result = await pool.query(
      `UPDATE customers 
       SET full_name = $1, email = $2, phone = $3, preferences = $4, updated_at = NOW()
       WHERE id = $5 AND business_id = $6
       RETURNING *`,
      [customerName, email, phone, JSON.stringify(preferences || {}), id, businessId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found for this business'
      });
    }

    console.log('✅ Business customer updated successfully:', id);
    res.json({
      success: true,
      message: 'Customer updated successfully',
      customer: result.rows[0]
    });
  } catch (error) {
    console.error('❌ Error updating business customer:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update customer'
    });
  }
});

// Delete customer from a specific business
app.delete('/api/businesses/:businessId/customers/:id', async (req, res) => {
  try {
    const { businessId, id } = req.params;
    console.log('📡 DELETE /api/businesses/:businessId/customers/:id - Deleting business customer', id, 'from business', businessId);

    const result = await pool.query(
      'DELETE FROM customers WHERE id = $1 AND business_id = $2 RETURNING full_name',
      [id, businessId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found for this business'
      });
    }

    console.log('✅ Business customer deleted successfully:', id);
    res.json({
      success: true,
      message: `Customer ${result.rows[0].full_name} has been removed from this business`
    });
  } catch (error) {
    console.error('❌ Error deleting business customer:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete customer'
    });
  }
});

// ===== EMAIL ROUTES =====
app.post('/api/send-email', async (req, res) => {
  try {
    const { to, subject, text, html } = req.body;

    if (!to || !subject || (!text && !html)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Missing required fields: to, subject, and text or html' 
      });
    }

    const mailOptions = {
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to,
      subject,
      text,
      html
    };

    const info = await emailTransporter.sendMail(mailOptions);
    res.json({ success: true, messageId: info.messageId });
  } catch (error) {
    console.error('Error sending email:', error);
    res.status(500).json({ success: false, message: 'Failed to send email' });
  }
});

// ===== TEST ROUTES (Development Only) =====
if (process.env.NODE_ENV !== 'production') {
  app.get('/api/test', (req, res) => {
    res.json({ 
      message: 'API routing is working!',
      timestamp: new Date().toISOString(),
      server: 'alive'
    });
  });

  app.post('/api/test', (req, res) => {
    res.json({ 
      message: 'API POST routing is working!',
      body: req.body,
      timestamp: new Date().toISOString()
    });
  });

  app.get('/api/debug-urls', (req, res) => {
    const replId = process.env.REPL_ID;
    const replOwner = process.env.REPL_OWNER;
    const replSlug = process.env.REPL_SLUG;
    const replUrl = process.env.REPL_URL;

    res.json({
      message: 'Debug information',
      environment: {
        REPL_ID: replId || 'not-set',
        REPL_OWNER: replOwner || 'not-set', 
        REPL_SLUG: replSlug || 'not-set',
        REPL_URL: replUrl || 'not-set'
      },
      request: {
        origin: req.get('Origin'),
        host: req.get('Host'),
        url: req.url,
        protocol: req.protocol      }
    });
  });
}

// ===== 404 HANDLER =====
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.path,
    method: req.method,
    availableEndpoints: [
      'GET /api/health',
      'POST /api/auth/register',
      'POST /api/auth/login',
      'GET /api/businesses',
      'POST /api/businesses',
      'GET /api/customers',
      'POST /api/customers'
    ]
  });
});

// ===== ERROR HANDLER =====
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ 
    success: false, 
    message: 'Internal server error',
    ...(process.env.NODE_ENV !== 'production' && { error: err.message })
  });
});

// ===== VOUCHER LIFECYCLE ENDPOINTS =====

  // Helper function to log voucher actions
  async function logVoucherAction(voucherId, action, oldStatus, newStatus, changes, userId, req) {
    try {
      await pool.query(`
        INSERT INTO voucher_audit_log (voucher_id, action, old_status, new_status, changes, performed_by, ip_address, user_agent)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [voucherId, action, oldStatus, newStatus, JSON.stringify(changes), userId, req.ip, req.get('User-Agent')]);
    } catch (error) {
      console.error('Error logging voucher action:', error);
    }
  }

  // Helper function to check if voucher should auto-unpublish
  async function checkAutoUnpublish(voucherId) {
    try {
      const result = await pool.query(`
        SELECT id, status, end_date, quantity_limit, current_redemptions
        FROM vouchers 
        WHERE id = $1 AND status = 'published'
      `, [voucherId]);

      if (result.rows.length === 0) return;

      const voucher = result.rows[0];
      const now = new Date();
      const endDate = new Date(voucher.end_date);
      const isExpired = endDate < now;
      const isOutOfStock = voucher.current_redemptions >= voucher.quantity_limit;

      if (isExpired || isOutOfStock) {
        await pool.query(`
          UPDATE vouchers 
          SET status = 'unpublished', unpublished_at = NOW(), updated_at = NOW()
          WHERE id = $1
        `, [voucherId]);

        const reason = isExpired ? 'expired' : 'out_of_stock';
        await logVoucherAction(voucherId, 'auto_unpublish', 'published', 'unpublished', { reason }, null, { ip: 'system' });
      }
    } catch (error) {
      console.error('Error checking auto-unpublish:', error);
    }
  }

  // Create campaign
  app.post('/api/campaigns', async (req, res) => {
    try {
      const { name, goal, notes, business_id } = req.body;

      if (!name || !business_id) {
        return res.status(400).json({
          success: false,
          message: 'Campaign name and business_id are required'
        });
      }

      const result = await pool.query(`
        INSERT INTO campaigns (business_id, name, goal, notes)
        VALUES ($1, $2, $3, $4)
        RETURNING *
      `, [business_id, name, goal, notes]);

      res.status(201).json({
        success: true,
        campaign: result.rows[0]
      });
    } catch (error) {
      console.error('Error creating campaign:', error);
      res.status(500).json({ success: false, message: 'Failed to create campaign' });
    }
  });

  // Create voucher (always starts as draft)
  app.post('/api/vouchers', async (req, res) => {
    try {
      const {
        business_id,
        campaign_id,
        title,
        description,
        reward,
        classification,
        tags,
        start_date,
        end_date,
        quantity_limit,
        image_url,
        use_ai_image,
        offer_value,
        restrictions,
        targeting,
        redeemable_sites
      } = req.body;

      if (!business_id || !campaign_id || !title || !description || !reward) {
        return res.status(400).json({
          success: false,
          message: 'Required fields missing'
        });
      }

      const result = await pool.query(`
        INSERT INTO vouchers (
          business_id, campaign_id, title, description, reward, classification,
          tags, start_date, end_date, quantity_limit, image_url, use_ai_image,
          offer_value, restrictions, targeting, redeemable_sites, created_by, updated_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $17)
        RETURNING *
      `, [
        business_id, campaign_id, title, description, reward, classification,
        tags, start_date, end_date, quantity_limit, image_url, use_ai_image,
        offer_value, restrictions, JSON.stringify(targeting), redeemable_sites, business_id
      ]);

      await logVoucherAction(result.rows[0].id, 'created', null, 'draft', req.body, business_id, req);

      res.status(201).json({
        success: true,
        voucher: result.rows[0]
      });
    } catch (error) {
      console.error('Error creating voucher:', error);
      res.status(500).json({ success: false, message: 'Failed to create voucher' });
    }
  });

  // Publish voucher
  app.post('/api/vouchers/:id/publish', async (req, res) => {
    try {
      const { id } = req.params;
      const { business_id } = req.body;

      // Check current status
      const currentResult = await pool.query(`
        SELECT status FROM vouchers WHERE id = $1 AND business_id = $2
      `, [id, business_id]);

      if (currentResult.rows.length === 0) {
        return res.status(404).json({ success: false, message: 'Voucher not found' });
      }

      const currentStatus = currentResult.rows[0].status;
      if (!['draft', 'unpublished'].includes(currentStatus)) {
        return res.status(400).json({ 
          success: false, 
          message: `Cannot publish voucher from ${currentStatus} state` 
        });
      }

      // Update to published
      const result = await pool.query(`
        UPDATE vouchers 
        SET status = 'published', published_at = NOW(), updated_at = NOW(), updated_by = $2
        WHERE id = $1 AND business_id = $2
        RETURNING *
      `, [id, business_id]);

      await logVoucherAction(id, 'published', currentStatus, 'published', {}, business_id, req);

      res.json({
        success: true,
        voucher: result.rows[0],
        message: 'Voucher published successfully'
      });
    } catch (error) {
      console.error('Error publishing voucher:', error);
      res.status(500).json({ success: false, message: 'Failed to publish voucher' });
    }
  });

  // Unpublish voucher
  app.post('/api/vouchers/:id/unpublish', async (req, res) => {
    try {
      const { id } = req.params;
      const { business_id } = req.body;

      const currentResult = await pool.query(`
        SELECT status FROM vouchers WHERE id = $1 AND business_id = $2
      `, [id, business_id]);

      if (currentResult.rows.length === 0) {
        return res.status(404).json({ success: false, message: 'Voucher not found' });
      }

      const currentStatus = currentResult.rows[0].status;
      if (currentStatus !== 'published') {
        return res.status(400).json({ 
          success: false, 
          message: `Cannot unpublish voucher from ${currentStatus} state` 
        });
      }

      const result = await pool.query(`
        UPDATE vouchers 
        SET status = 'unpublished', unpublished_at = NOW(), updated_at = NOW(), updated_by = $2
        WHERE id = $1 AND business_id = $2
        RETURNING *
      `, [id, business_id]);

      await logVoucherAction(id, 'unpublished', 'published', 'unpublished', {}, business_id, req);

      res.json({
        success: true,
        voucher: result.rows[0],
        message: 'Voucher unpublished successfully'
      });
    } catch (error) {
      console.error('Error unpublishing voucher:', error);
      res.status(500).json({ success: false, message: 'Failed to unpublish voucher' });
    }
  });

  // Archive voucher
  app.post('/api/vouchers/:id/archive', async (req, res) => {
    try {
      const { id } = req.params;
      const { business_id } = req.body;

      const currentResult = await pool.query(`
        SELECT status FROM vouchers WHERE id = $1 AND business_id = $2
      `, [id, business_id]);

      if (currentResult.rows.length === 0) {
        return res.status(404).json({ success: false, message: 'Voucher not found' });
      }

      const currentStatus = currentResult.rows[0].status;
      if (!['draft', 'unpublished'].includes(currentStatus)) {
        return res.status(400).json({ 
          success: false, 
          message: `Cannot archive voucher from ${currentStatus} state. Must be draft or unpublished.` 
        });
      }

      const result = await pool.query(`
        UPDATE vouchers 
        SET status = 'archived', archived_at = NOW(), updated_at = NOW(), updated_by = $2
        WHERE id = $1 AND business_id = $2
        RETURNING *
      `, [id, business_id]);

      await logVoucherAction(id, 'archived', currentStatus, 'archived', {}, business_id, req);

      res.json({
        success: true,
        voucher: result.rows[0],
        message: 'Voucher archived successfully'
      });
    } catch (error) {
      console.error('Error archiving voucher:', error);
      res.status(500).json({ success: false, message: 'Failed to archive voucher' });
    }
  });

  // Create voucher revision (for staged edits)
  app.post('/api/vouchers/:id/revisions', async (req, res) => {
    try {
      const { id } = req.params;
      const { business_id, changes } = req.body;

      // Verify voucher exists and is published
      const voucherResult = await pool.query(`
        SELECT status FROM vouchers WHERE id = $1 AND business_id = $2
      `, [id, business_id]);

      if (voucherResult.rows.length === 0) {
        return res.status(404).json({ success: false, message: 'Voucher not found' });
      }

      if (voucherResult.rows[0].status !== 'published') {
        return res.status(400).json({ 
          success: false, 
          message: 'Can only create revisions for published vouchers' 
        });
      }

      // Create revision
      const result = await pool.query(`
        INSERT INTO voucher_revisions (
          voucher_id, title, description, reward, classification, tags,
          start_date, end_date, quantity_limit, image_url, use_ai_image,
          offer_value, restrictions, targeting, redeemable_sites, created_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
        RETURNING *
      `, [
        id, changes.title, changes.description, changes.reward, changes.classification,
        changes.tags, changes.start_date, changes.end_date, changes.quantity_limit,
        changes.image_url, changes.use_ai_image, changes.offer_value, changes.restrictions,
        JSON.stringify(changes.targeting), changes.redeemable_sites, business_id
      ]);

      await logVoucherAction(id, 'revision_created', 'published', 'published', changes, business_id, req);

      res.status(201).json({
        success: true,
        revision: result.rows[0],
        message: 'Revision created successfully'
      });
    } catch (error) {
      console.error('Error creating voucher revision:', error);
      res.status(500).json({ success: false, message: 'Failed to create revision' });
    }
  });

  // Apply voucher revision (push changes live)
  app.post('/api/vouchers/:id/apply-revision', async (req, res) => {
    try {
      const { id } = req.params;
      const { business_id, revision_id } = req.body;

      // Get the revision
      const revisionResult = await pool.query(`
        SELECT * FROM voucher_revisions 
        WHERE id = $1 AND voucher_id = $2 AND is_applied = false
      `, [revision_id, id]);

      if (revisionResult.rows.length === 0) {
        return res.status(404).json({ success: false, message: 'Revision not found or already applied' });
      }

      const revision = revisionResult.rows[0];

      // Apply changes to voucher
      const result = await pool.query(`
        UPDATE vouchers SET
          title = COALESCE($3, title),
          description = COALESCE($4, description),
          reward = COALESCE($5, reward),
          classification = COALESCE($6, classification),
          tags = COALESCE($7, tags),
          start_date = COALESCE($8, start_date),
          end_date = COALESCE($9, end_date),
          quantity_limit = COALESCE($10, quantity_limit),
          image_url = COALESCE($11, image_url),
          use_ai_image = COALESCE($12, use_ai_image),
          offer_value = COALESCE($13, offer_value),
          restrictions = COALESCE($14, restrictions),
          targeting = COALESCE($15, targeting),
          redeemable_sites = COALESCE($16, redeemable_sites),
          updated_at = NOW(),
          updated_by = $2
        WHERE id = $1 AND business_id = $2
        RETURNING *
      `, [
        id, business_id, revision.title, revision.description, revision.reward,
        revision.classification, revision.tags, revision.start_date, revision.end_date,
        revision.quantity_limit, revision.image_url, revision.use_ai_image,
        revision.offer_value, revision.restrictions, revision.targeting, revision.redeemable_sites
      ]);

      // Mark revision as applied
      await pool.query(`
        UPDATE voucher_revisions SET is_applied = true WHERE id = $1
      `, [revision_id]);

      await logVoucherAction(id, 'revision_applied', 'published', 'published', revision, business_id, req);

      res.json({
        success: true,
        voucher: result.rows[0],
        message: 'Changes applied successfully'
      });
    } catch (error) {
      console.error('Error applying voucher revision:', error);
      res.status(500).json({ success: false, message: 'Failed to apply changes' });
    }
  });

  // Get vouchers with lifecycle information
  app.get('/api/campaigns/:campaignId/vouchers', async (req, res) => {
    try {
      const { campaignId } = req.params;
      const { includeArchived = 'false' } = req.query;

      let query = `
        SELECT v.*, 
               COUNT(vr.id) as pending_revisions,
               vr.id as latest_revision_id
        FROM vouchers v
        LEFT JOIN voucher_revisions vr ON v.id = vr.voucher_id AND vr.is_applied = false
        WHERE v.campaign_id = $1
      `;

      if (includeArchived !== 'true') {
        query += ` AND v.status != 'archived'`;
      }

      query += ` GROUP BY v.id, vr.id ORDER BY v.created_at DESC`;

      const result = await pool.query(query, [campaignId]);

      // Check for auto-unpublish on published vouchers
      for (const voucher of result.rows) {
        if (voucher.status === 'published') {
          await checkAutoUnpublish(voucher.id);
        }
      }

      res.json({
        success: true,
        vouchers: result.rows
      });
    } catch (error) {
      console.error('Error fetching vouchers:', error);
      res.status(500).json({ success: false, message: 'Failed to fetch vouchers' });
    }
  });

  // Get voucher audit log
  app.get('/api/vouchers/:id/audit-log', async (req, res) => {
    try {
      const { id } = req.params;

      const result = await pool.query(`
        SELECT val.*, b.full_name as performed_by_name
        FROM voucher_audit_log val
        LEFT JOIN businesses b ON val.performed_by = b.id
        WHERE val.voucher_id = $1
        ORDER BY val.performed_at DESC
      `, [id]);

      res.json({
        success: true,
        auditLog: result.rows
      });
    } catch (error) {
      console.error('Error fetching audit log:', error);
      res.status(500).json({ success: false, message: 'Failed to fetch audit log' });
    }
  });

  // Voucher redemption endpoint with automatic customer enrollment
  app.post('/api/vouchers/:id/redeem', async (req, res) => {
    const { id } = req.params;
    const { customer_email, customer_name, customer_phone } = req.body;

    try {
      // Check if voucher exists and is valid
      const voucherResult = await pool.query(`
        SELECT * FROM vouchers WHERE id = $1
      `, [id]);

      if (voucherResult.rows.length === 0) {
        return res.status(404).json({ success: false, message: 'Voucher not found' });
      }

      const voucher = voucherResult.rows[0];

      if (voucher.status !== 'published') {
        return res.status(400).json({ success: false, message: 'Voucher is not active' });
      }

      if (new Date(voucher.end_date) < new Date()) {
        return res.status(400).json({ success: false, message: 'Voucher has expired' });
      }

      if (voucher.quantity_limit !== null && voucher.current_redemptions >= voucher.quantity_limit) {
        return res.status(400).json({ success: false, message: 'Voucher redemption limit reached' });
      }

      // Automatically enroll customer if they don't exist
      if (customer_email && customer_name) {
        try {
          // Check if customer already exists
          const existingCustomerResult = await pool.query(
            'SELECT * FROM customers WHERE email = $1',
            [customer_email]
          );

          if (existingCustomerResult.rows.length === 0) {
            // Create new customer record
            const customerInsertResult = await pool.query(
              `
                INSERT INTO customers (
                  email, full_name, phone, created_at, updated_at, email_verified, 
                  total_earnings, total_referrals
                ) VALUES ($1, $2, $3, NOW(), NOW(), false, 0, 0)
                RETURNING id
              `,
              [customer_email, customer_name, customer_phone || null]
            );

            const newCustomerId = customerInsertResult.rows[0].id;
            console.log(`✅ Auto-enrolled customer ${customer_name} (${customer_email}) with ID ${newCustomerId}`);
          } else {
            // Update last activity for existing customer
            await pool.query(
              'UPDATE customers SET updated_at = NOW() WHERE email = $1',
              [customer_email]
            );
            console.log(`✅ Updated last activity for existing customer ${customer_name} (${customer_email})`);
          }
        } catch (enrollmentError) {
          console.error('Error auto-enrolling customer:', enrollmentError);
          // Continue with redemption even if enrollment fails
        }
      }

      // Record the redemption
      await pool.query(
        `
          UPDATE vouchers
          SET current_redemptions = current_redemptions + 1
          WHERE id = $1
        `,
        [id]
      );

      // Log voucher redemption
      await pool.query(
        `
          INSERT INTO voucher_redemptions (voucher_id, customer_email, customer_name, customer_phone, redeemed_at)
          VALUES ($1, $2, $3, $4, NOW())
        `,
        [id, customer_email, customer_name, customer_phone]
      );

      res.json({
        success: true,
        message: 'Voucher redeemed successfully',
        customer_auto_enrolled: customer_email && customer_name ? true : false
      });
    } catch (error) {
      console.error('Error redeeming voucher:', error);
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  });

// ===== SERVER STARTUP =====
initDatabase().then(() => {
  const server = app.listen(port, '0.0.0.0', () => {
    console.log(`🚀 Server successfully started on port ${port}`);
    console.log(`📡 Local API: http://0.0.0.0:${port}/api`);
    console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log('🔒 CORS: Configured for Replit domains');
    console.log('✅ Database: Connected and initialized');

    if (process.env.SMTP_USER && process.env.SMTP_PASS) {
      console.log('📧 Email: Configured');
    } else {
      console.log('⚠️  Email: Not configured');
    }
  });

  server.on('error', (err) => {
    console.error('❌ Server startup error:', err);
    if (err.code === 'EADDRINUSE') {
      console.error(`❌ Port ${port} is already in use!`);
      process.exit(1);
    }
  });

  process.on('SIGTERM', () => {
    console.log('🛑 SIGTERM received, shutting down gracefully');
    server.close(() => {
      console.log('💤 Server closed');
      pool.end();
      process.exit(0);
    });
  });
});