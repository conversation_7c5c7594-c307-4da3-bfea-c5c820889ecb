const fs = require('fs');
const path = require('path');

class EmailTemplates {
  constructor() {
    this.brandColors = {
      primary: '#667eea',
      secondary: '#764ba2',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      gray: {
        50: '#f9fafb',
        100: '#f3f4f6',
        600: '#4b5563',
        800: '#1f2937',
        900: '#111827'
      }
    };
  }

  // Base template with responsive design
  getBaseTemplate(title, content, footerContent = '') {
    return `
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="x-apple-disable-message-reformatting">
  <title>${title}</title>
  <!--[if mso]>
  <noscript>
    <xml>
      <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  </noscript>
  <![endif]-->
  <style type="text/css">
    @media only screen and (max-width: 600px) {
      .mobile-padding { padding: 20px !important; }
      .mobile-text { font-size: 16px !important; line-height: 24px !important; }
      .mobile-title { font-size: 24px !important; line-height: 32px !important; }
      .mobile-button { width: 100% !important; }
    }
  </style>
</head>
<body style="margin: 0; padding: 0; background-color: ${this.brandColors.gray[50]}; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: ${this.brandColors.gray[50]};">
    <tr>
      <td style="padding: 20px 0;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="margin: 0 auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
          <!-- Header -->
          <tr>
            <td style="background: linear-gradient(135deg, ${this.brandColors.primary} 0%, ${this.brandColors.secondary} 100%); padding: 40px 40px 30px; text-align: center; border-radius: 12px 12px 0 0;" class="mobile-padding">
              <div style="color: #ffffff; font-size: 28px; font-weight: 700; margin-bottom: 8px;" class="mobile-title">Referit</div>
              <div style="color: rgba(255, 255, 255, 0.9); font-size: 16px;">${title}</div>
            </td>
          </tr>

          <!-- Content -->
          <tr>
            <td style="padding: 40px;" class="mobile-padding">
              ${content}
            </td>
          </tr>

          <!-- Footer -->
          <tr>
            <td style="background-color: ${this.brandColors.gray[50]}; padding: 30px 40px; border-radius: 0 0 12px 12px; border-top: 1px solid ${this.brandColors.gray[100]};" class="mobile-padding">
              <div style="text-align: center; color: ${this.brandColors.gray[600]}; font-size: 14px; line-height: 20px;">
                ${footerContent || `
                  <p style="margin: 0 0 16px;">© 2024 Referit. All rights reserved.</p>
                  <p style="margin: 0;">
                    <a href="mailto:<EMAIL>" style="color: ${this.brandColors.primary}; text-decoration: none;">Contact Support</a> |
                    <a href="#" style="color: ${this.brandColors.primary}; text-decoration: none;">Unsubscribe</a>
                  </p>
                `}
              </div>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>`;
  }

  // Reusable button component
  getButton(text, url, style = 'primary') {
    const styles = {
      primary: `background: linear-gradient(135deg, ${this.brandColors.primary} 0%, ${this.brandColors.secondary} 100%); color: #ffffff;`,
      success: `background-color: ${this.brandColors.success}; color: #ffffff;`,
      secondary: `background-color: #ffffff; color: ${this.brandColors.primary}; border: 2px solid ${this.brandColors.primary};`
    };

    return `
      <div style="text-align: center; margin: 30px 0;">
        <a href="${url}" style="display: inline-block; ${styles[style]} padding: 16px 32px; border-radius: 8px; text-decoration: none; font-weight: 600; font-size: 16px; border: none; transition: all 0.2s; min-width: 200px;" class="mobile-button">
          ${text}
        </a>
      </div>
    `;
  }

  // Info box component
  getInfoBox(content, type = 'info') {
    const styles = {
      info: `background-color: #eff6ff; border-left: 4px solid ${this.brandColors.primary}; color: ${this.brandColors.gray[800]};`,
      success: `background-color: #ecfdf5; border-left: 4px solid ${this.brandColors.success}; color: ${this.brandColors.gray[800]};`,
      warning: `background-color: #fffbeb; border-left: 4px solid ${this.brandColors.warning}; color: ${this.brandColors.gray[800]};`
    };

    return `
      <div style="${styles[type]} padding: 20px; border-radius: 8px; margin: 24px 0;">
        <div style="font-size: 14px; line-height: 20px;">
          ${content}
        </div>
      </div>
    `;
  }

  // Customer Welcome Email
  getCustomerWelcomeEmail(customerName, loginUrl) {
    const content = `
      <h1 style="color: ${this.brandColors.gray[900]}; font-size: 28px; font-weight: 700; margin: 0 0 24px; line-height: 1.2;" class="mobile-title">
        Welcome to Referit, ${customerName}! 🎉
      </h1>

      <p style="color: ${this.brandColors.gray[600]}; font-size: 16px; line-height: 24px; margin: 0 0 24px;" class="mobile-text">
        You've just joined thousands of people who are turning their recommendations into real rewards. Get ready to earn from every referral!
      </p>

      ${this.getInfoBox(`
        <strong>What's next?</strong><br>
        • Complete your profile to unlock all features<br>
        • Share your first referral link<br>
        • Start earning rewards immediately
      `, 'success')}

      <h3 style="color: ${this.brandColors.gray[900]}; font-size: 18px; font-weight: 600; margin: 32px 0 16px;">
        Here's how Referit works:
      </h3>

      <div style="margin: 24px 0;">
        <div style="display: flex; align-items: flex-start; margin-bottom: 20px;">
          <div style="background: linear-gradient(135deg, ${this.brandColors.primary} 0%, ${this.brandColors.secondary} 100%); color: white; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; margin-right: 16px; flex-shrink: 0;">1</div>
          <div>
            <h4 style="color: ${this.brandColors.gray[900]}; font-size: 16px; font-weight: 600; margin: 0 0 4px;">Find Great Businesses</h4>
            <p style="color: ${this.brandColors.gray[600]}; font-size: 14px; margin: 0; line-height: 20px;">Discover amazing local businesses and services you'll want to share.</p>
          </div>
        </div>

        <div style="display: flex; align-items: flex-start; margin-bottom: 20px;">
          <div style="background: linear-gradient(135deg, ${this.brandColors.primary} 0%, ${this.brandColors.secondary} 100%); color: white; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; margin-right: 16px; flex-shrink: 0;">2</div>
          <div>
            <h4 style="color: ${this.brandColors.gray[900]}; font-size: 16px; font-weight: 600; margin: 0 0 4px;">Share & Refer</h4>
            <p style="color: ${this.brandColors.gray[600]}; font-size: 14px; margin: 0; line-height: 20px;">Share your unique referral links with friends, family, and your network.</p>
          </div>
        </div>

        <div style="display: flex; align-items: flex-start;">
          <div style="background: linear-gradient(135deg, ${this.brandColors.primary} 0%, ${this.brandColors.secondary} 100%); color: white; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; margin-right: 16px; flex-shrink: 0;">3</div>
          <div>
            <h4 style="color: ${this.brandColors.gray[900]}; font-size: 16px; font-weight: 600; margin: 0 0 4px;">Earn Rewards</h4>
            <p style="color: ${this.brandColors.gray[600]}; font-size: 14px; margin: 0; line-height: 20px;">Get paid real money or valuable rewards for every successful referral.</p>
          </div>
        </div>
      </div>

      ${this.getButton('Get Started Now', loginUrl, 'primary')}

      <p style="color: ${this.brandColors.gray[600]}; font-size: 14px; line-height: 20px; margin: 32px 0 0; text-align: center;">
        Questions? We're here to help! Reply to this email or contact us at 
        <a href="mailto:<EMAIL>" style="color: ${this.brandColors.primary}; text-decoration: none;"><EMAIL></a>
      </p>
    `;

    const plainText = `
Welcome to Referit, ${customerName}!

You've just joined thousands of people who are turning their recommendations into real rewards. Get ready to earn from every referral!

What's next?
• Complete your profile to unlock all features
• Share your first referral link  
• Start earning rewards immediately

Here's how Referit works:

1. Find Great Businesses
   Discover amazing local businesses and services you'll want to share.

2. Share & Refer
   Share your unique referral links with friends, family, and your network.

3. Earn Rewards
   Get paid real money or valuable rewards for every successful referral.

Get started now: ${loginUrl}

Questions? We're here to help! Reply to this email or contact <NAME_EMAIL>

© 2024 Referit. All rights reserved.
    `;

    return {
      html: this.getBaseTemplate('Welcome to Your Referral Journey', content),
      text: plainText
    };
  }

  // Business Registration Email
  getBusinessWelcomeEmail(businessName, ownerName, dashboardUrl) {
    const content = `
      <h1 style="color: ${this.brandColors.gray[900]}; font-size: 28px; font-weight: 700; margin: 0 0 24px; line-height: 1.2;" class="mobile-title">
        Welcome to Referit, ${businessName}! 🚀
      </h1>

      <p style="color: ${this.brandColors.gray[600]}; font-size: 16px; line-height: 24px; margin: 0 0 24px;" class="mobile-text">
        Hi ${ownerName}, your business account has been successfully created. You're now ready to harness the power of referral marketing and grow your customer base exponentially.
      </p>

      ${this.getInfoBox(`
        <strong>🎯 Your account includes:</strong><br>
        • Unlimited referral campaigns<br>
        • Real-time analytics dashboard<br>
        • Automated reward distribution<br>
        • Customer referral tracking
      `, 'success')}

      <h3 style="color: ${this.brandColors.gray[900]}; font-size: 18px; font-weight: 600; margin: 32px 0 16px;">
        Quick Setup Guide:
      </h3>

      <div style="margin: 24px 0;">
        <div style="background-color: ${this.brandColors.gray[50]}; border-radius: 8px; padding: 20px; margin-bottom: 16px;">
          <div style="display: flex; align-items: center; margin-bottom: 12px;">
            <div style="background-color: ${this.brandColors.primary}; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: 700; margin-right: 12px;">1</div>
            <h4 style="color: ${this.brandColors.gray[900]}; font-size: 16px; font-weight: 600; margin: 0;">Complete Business Profile</h4>
          </div>
          <p style="color: ${this.brandColors.gray[600]}; font-size: 14px; margin: 0; padding-left: 36px;">Add your business details, logo, and contact information.</p>
        </div>

        <div style="background-color: ${this.brandColors.gray[50]}; border-radius: 8px; padding: 20px; margin-bottom: 16px;">
          <div style="display: flex; align-items: center; margin-bottom: 12px;">
            <div style="background-color: ${this.brandColors.primary}; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: 700; margin-right: 12px;">2</div>
            <h4 style="color: ${this.brandColors.gray[900]}; font-size: 16px; font-weight: 600; margin: 0;">Create Your First Campaign</h4>
          </div>
          <p style="color: ${this.brandColors.gray[600]}; font-size: 14px; margin: 0; padding-left: 36px;">Set up rewards and referral incentives that will motivate customers to share.</p>
        </div>

        <div style="background-color: ${this.brandColors.gray[50]}; border-radius: 8px; padding: 20px;">
          <div style="display: flex; align-items: center; margin-bottom: 12px;">
            <div style="background-color: ${this.brandColors.primary}; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: 700; margin-right: 12px;">3</div>
            <h4 style="color: ${this.brandColors.gray[900]}; font-size: 16px; font-weight: 600; margin: 0;">Launch & Monitor</h4>
          </div>
          <p style="color: ${this.brandColors.gray[600]}; font-size: 14px; margin: 0; padding-left: 36px;">Activate your campaigns and track performance through your dashboard.</p>
        </div>
      </div>

      ${this.getButton('Access Dashboard', dashboardUrl, 'primary')}

      <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid ${this.brandColors.gray[100]};">
        <h4 style="color: ${this.brandColors.gray[900]}; font-size: 16px; font-weight: 600; margin: 0 0 12px;">Need Help Getting Started?</h4>
        <p style="color: ${this.brandColors.gray[600]}; font-size: 14px; line-height: 20px; margin: 0;">
          Our team is here to support you. Contact us at 
          <a href="mailto:<EMAIL>" style="color: ${this.brandColors.primary}; text-decoration: none;"><EMAIL></a> 
          for personalized onboarding assistance.
        </p>
      </div>
    `;

    const plainText = `
Welcome to Referit, ${businessName}!

Hi ${ownerName}, your business account has been successfully created. You're now ready to harness the power of referral marketing and grow your customer base exponentially.

Your account includes:
• Unlimited referral campaigns
• Real-time analytics dashboard  
• Automated reward distribution
• Customer referral tracking

Quick Setup Guide:

1. Complete Business Profile
   Add your business details, logo, and contact information.

2. Create Your First Campaign
   Set up rewards and referral incentives that will motivate customers to share.

3. Launch & Monitor
   Activate your campaigns and track performance through your dashboard.

Access your dashboard: ${dashboardUrl}

Need Help Getting Started?
Our team is here to support you. Contact <NAME_EMAIL> for personalized onboarding assistance.

© 2024 Referit. All rights reserved.
    `;

    return {
      html: this.getBaseTemplate('Your Business Account is Ready', content),
      text: plainText
    };
  }

  // Contact Form Notification Email
  getContactFormNotification(formData) {
    const content = `
      <h1 style="color: ${this.brandColors.gray[900]}; font-size: 24px; font-weight: 700; margin: 0 0 24px;">
        New Contact Form Submission
      </h1>

      <div style="background-color: ${this.brandColors.gray[50]}; border-radius: 8px; padding: 24px; margin-bottom: 24px;">
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 8px 0; vertical-align: top; width: 120px;">
              <strong style="color: ${this.brandColors.gray[900]};">Name:</strong>
            </td>
            <td style="padding: 8px 0; color: ${this.brandColors.gray[600]};">
              ${formData.firstName} ${formData.lastName}
            </td>
          </tr>
          <tr>
            <td style="padding: 8px 0; vertical-align: top;">
              <strong style="color: ${this.brandColors.gray[900]};">Email:</strong>
            </td>
            <td style="padding: 8px 0; color: ${this.brandColors.gray[600]};">
              <a href="mailto:${formData.email}" style="color: ${this.brandColors.primary}; text-decoration: none;">${formData.email}</a>
            </td>
          </tr>
          <tr>
            <td style="padding: 8px 0; vertical-align: top;">
              <strong style="color: ${this.brandColors.gray[900]};">Subject:</strong>
            </td>
            <td style="padding: 8px 0; color: ${this.brandColors.gray[600]};">
              ${formData.subject}
            </td>
          </tr>
          <tr>
            <td style="padding: 8px 0; vertical-align: top;">
              <strong style="color: ${this.brandColors.gray[900]};">Submitted:</strong>
            </td>
            <td style="padding: 8px 0; color: ${this.brandColors.gray[600]};">
              ${new Date().toLocaleString()}
            </td>
          </tr>
        </table>
      </div>

      <div style="background-color: #ffffff; border: 1px solid ${this.brandColors.gray[100]}; border-radius: 8px; padding: 24px;">
        <h3 style="color: ${this.brandColors.gray[900]}; font-size: 16px; font-weight: 600; margin: 0 0 12px;">Message:</h3>
        <div style="color: ${this.brandColors.gray[600]}; font-size: 15px; line-height: 24px; white-space: pre-wrap; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
${formData.message}
        </div>
      </div>

      ${this.getButton('Reply via Email', `mailto:${formData.email}?subject=Re: ${formData.subject}`, 'primary')}

      <p style="color: ${this.brandColors.gray[600]}; font-size: 13px; line-height: 18px; margin: 24px 0 0; text-align: center;">
        This message was sent via the Referit contact form.
      </p>
    `;

    const plainText = `
New Contact Form Submission

Name: ${formData.firstName} ${formData.lastName}
Email: ${formData.email}
Subject: ${formData.subject}
Submitted: ${new Date().toLocaleString()}

Message:
${formData.message}

Reply to: ${formData.email}

This message was sent via the Referit contact form.
    `;

    const footerContent = `
      <p style="margin: 0 0 16px;">This is an internal notification email.</p>
      <p style="margin: 0;">
        <a href="mailto:${formData.email}" style="color: ${this.brandColors.primary}; text-decoration: none;">Reply to Customer</a>
      </p>
    `;

    return {
      html: this.getBaseTemplate('New Contact Form Submission', content, footerContent),
      text: plainText
    };
  }

  // Password Reset Email
  getPasswordResetEmail(userName, resetLink, expirationTime = '1 hour') {
    const content = `
      <h1 style="color: ${this.brandColors.gray[900]}; font-size: 28px; font-weight: 700; margin: 0 0 24px; line-height: 1.2;" class="mobile-title">
        Reset Your Password 🔐
      </h1>

      <p style="color: ${this.brandColors.gray[600]}; font-size: 16px; line-height: 24px; margin: 0 0 24px;" class="mobile-text">
        Hi ${userName || 'there'}, we received a request to reset your Referit account password. Click the button below to create a new password.
      </p>

      ${this.getButton('Reset My Password', resetLink, 'primary')}

      ${this.getInfoBox(`
        <strong>⚠️ Security Notice:</strong><br>
        • This link will expire in ${expirationTime}<br>
        • If you didn't request this reset, please ignore this email<br>
        • Your current password remains unchanged until you create a new one
      `, 'warning')}

      <div style="margin: 32px 0; padding: 20px; background-color: ${this.brandColors.gray[50]}; border-radius: 8px;">
        <h4 style="color: ${this.brandColors.gray[900]}; font-size: 16px; font-weight: 600; margin: 0 0 12px;">
          Can't click the button?
        </h4>
        <p style="color: ${this.brandColors.gray[600]}; font-size: 14px; line-height: 20px; margin: 0 0 12px;">
          Copy and paste this link into your browser:
        </p>
        <div style="background-color: #ffffff; border: 1px solid ${this.brandColors.gray[100]}; border-radius: 6px; padding: 12px; word-break: break-all;">
          <a href="${resetLink}" style="color: ${this.brandColors.primary}; text-decoration: none; font-size: 13px;">
            ${resetLink}
          </a>
        </div>
      </div>

      <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid ${this.brandColors.gray[100]};">
        <h4 style="color: ${this.brandColors.gray[900]}; font-size: 16px; font-weight: 600; margin: 0 0 12px;">Having trouble?</h4>
        <p style="color: ${this.brandColors.gray[600]}; font-size: 14px; line-height: 20px; margin: 0;">
          If you're experiencing issues with password reset, please contact our support team at 
          <a href="mailto:<EMAIL>" style="color: ${this.brandColors.primary}; text-decoration: none;"><EMAIL></a>
        </p>
      </div>
    `;

    const plainText = `
Reset Your Password

Hi ${userName || 'there'}, we received a request to reset your Referit account password.

Reset your password using this link: ${resetLink}

Security Notice:
• This link will expire in ${expirationTime}
• If you didn't request this reset, please ignore this email
• Your current password remains unchanged until you create a new one

Having trouble?
If you're experiencing issues with password reset, please contact our support <NAME_EMAIL>

© 2024 Referit. All rights reserved.
    `;

    const footerContent = `
      <p style="margin: 0 0 16px;">This is a password reset email.</p>
      <p style="margin: 0;">
        <a href="mailto:<EMAIL>" style="color: ${this.brandColors.primary}; text-decoration: none;">Contact Support</a>
      </p>
    `;

    return {
      html: this.getBaseTemplate('Password Reset Request', content, footerContent),
      text: plainText
    };
  }

  // Email Verification Email
  getEmailVerificationEmail(userName, verificationLink, expirationTime = '24 hours') {
    const content = `
      <h1 style="color: ${this.brandColors.gray[900]}; font-size: 28px; font-weight: 700; margin: 0 0 24px; line-height: 1.2;" class="mobile-title">
        Verify Your Email Address 📧
      </h1>

      <p style="color: ${this.brandColors.gray[600]}; font-size: 16px; line-height: 24px; margin: 0 0 24px;" class="mobile-text">
        Hi ${userName || 'there'}, thank you for signing up for Referit! To complete your registration, please verify your email address by clicking the button below.
      </p>

      ${this.getButton('Verify Email', verificationLink, 'primary')}

      ${this.getInfoBox(`
        <strong>⚠️ Important:</strong><br>
        • This link will expire in ${expirationTime}<br>
        • Please verify your email to access all features of Referit
      `, 'info')}

      <div style="margin: 32px 0; padding: 20px; background-color: ${this.brandColors.gray[50]}; border-radius: 8px;">
        <h4 style="color: ${this.brandColors.gray[900]}; font-size: 16px; font-weight: 600; margin: 0 0 12px;">
          Can't click the button?
        </h4>
        <p style="color: ${this.brandColors.gray[600]}; font-size: 14px; line-height: 20px; margin: 0 0 12px;">
          Copy and paste this link into your browser:
        </p>
        <div style="background-color: #ffffff; border: 1px solid ${this.brandColors.gray[100]}; border-radius: 6px; padding: 12px; word-break: break-all;">
          <a href="${verificationLink}" style="color: ${this.brandColors.primary}; text-decoration: none; font-size: 13px;">
            ${verificationLink}
          </a>
        </div>
      </div>

      <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid ${this.brandColors.gray[100]};">
        <h4 style="color: ${this.brandColors.gray[900]}; font-size: 16px; font-weight: 600; margin: 0 0 12px;">Need Help?</h4>
        <p style="color: ${this.brandColors.gray[600]}; font-size: 14px; line-height: 20px; margin: 0;">
          If you're experiencing issues with email verification, please contact our support team at 
          <a href="mailto:<EMAIL>" style="color: ${this.brandColors.primary}; text-decoration: none;"><EMAIL></a>
        </p>
      </div>
    `;

    const plainText = `
Verify Your Email Address

Hi ${userName || 'there'}, thank you for signing up for Referit! To complete your registration, please verify your email address.

Verify your email using this link: ${verificationLink}

Important:
• This link will expire in ${expirationTime}
• Please verify your email to access all features of Referit

Having trouble?
If you're experiencing issues with email verification, please contact our support <NAME_EMAIL>

© 2024 Referit. All rights reserved.
    `;

    const footerContent = `
      <p style="margin: 0 0 16px;">This is an email verification email.</p>
      <p style="margin: 0;">
        <a href="mailto:<EMAIL>" style="color: ${this.brandColors.primary}; text-decoration: none;">Contact Support</a>
      </p>
    `;

    return {
      html: this.getBaseTemplate('Verify Your Email Address', content, footerContent),
      text: plainText
    };
  }
}

module.exports = new EmailTemplates();