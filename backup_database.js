
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Database configuration
let connectionString = process.env.DATABASE_URL || process.env.REPLIT_DB_URL;

if (connectionString && connectionString.includes('supabase.co')) {
  try {
    const url = new URL(connectionString);
    const decodedPassword = decodeURIComponent(url.password);
    connectionString = `postgresql://${url.username}:${encodeURIComponent(decodedPassword)}@${url.hostname}:${url.port}${url.pathname}${url.search}`;
  } catch (parseError) {
    console.error('Failed to parse connection string:', parseError.message);
  }
}

const pool = new Pool({
  connectionString: connectionString,
  ssl: connectionString && connectionString.includes('supabase.co') ? { 
    rejectUnauthorized: false 
  } : false,
  max: 20,
  min: 2,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 20000,
  acquireTimeoutMillis: 20000
});

async function backupDatabase() {
  try {
    console.log('💾 Starting database backup...');
    
    // Connect to database
    await pool.query('SELECT NOW()');
    console.log('✅ Database connection successful');

    // Create backup directory with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = `backup_${timestamp}`;
    
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir);
    }
    
    console.log(`📁 Created backup directory: ${backupDir}`);

    // Backup all tables
    const tables = ['customers', 'businesses', 'business_profiles', 'business_locations', 'referrals'];
    const backupData = {
      timestamp: new Date().toISOString(),
      backup_info: {
        created_at: new Date().toISOString(),
        total_tables: tables.length,
        database_url: connectionString ? 'configured' : 'not configured'
      },
      tables: {}
    };

    for (const table of tables) {
      try {
        console.log(`📋 Backing up table: ${table}`);
        
        const result = await pool.query(`SELECT * FROM ${table} ORDER BY created_at DESC`);
        backupData.tables[table] = {
          count: result.rows.length,
          data: result.rows
        };
        
        // Save individual table backup
        const tableBackupPath = path.join(backupDir, `${table}.json`);
        fs.writeFileSync(tableBackupPath, JSON.stringify({
          table: table,
          count: result.rows.length,
          backed_up_at: new Date().toISOString(),
          data: result.rows
        }, null, 2));
        
        console.log(`✅ Backed up ${result.rows.length} records from ${table}`);
        
      } catch (error) {
        console.log(`⚠️  Table ${table} might not exist or is empty: ${error.message}`);
        backupData.tables[table] = {
          count: 0,
          data: [],
          error: error.message
        };
      }
    }

    // Save complete backup file
    const completeBackupPath = path.join(backupDir, 'complete_backup.json');
    fs.writeFileSync(completeBackupPath, JSON.stringify(backupData, null, 2));

    // Create backup summary
    const summaryPath = path.join(backupDir, 'backup_summary.txt');
    let summary = `DATABASE BACKUP SUMMARY\n`;
    summary += `========================\n`;
    summary += `Backup Date: ${new Date().toISOString()}\n`;
    summary += `Backup Directory: ${backupDir}\n\n`;
    
    let totalRecords = 0;
    for (const [tableName, tableData] of Object.entries(backupData.tables)) {
      summary += `${tableName.toUpperCase()}: ${tableData.count} records\n`;
      totalRecords += tableData.count;
    }
    
    summary += `\nTOTAL RECORDS BACKED UP: ${totalRecords}\n\n`;
    summary += `FILES CREATED:\n`;
    summary += `- complete_backup.json (all data in one file)\n`;
    for (const table of tables) {
      summary += `- ${table}.json (individual table backup)\n`;
    }
    summary += `- backup_summary.txt (this file)\n`;
    
    fs.writeFileSync(summaryPath, summary);

    console.log('\n🎉 Backup completed successfully!');
    console.log(`📊 Summary:`);
    console.log(`   - Backup directory: ${backupDir}`);
    console.log(`   - Total records backed up: ${totalRecords}`);
    console.log(`   - Files created: ${tables.length + 2}`);
    
    console.log('\n📋 Table breakdown:');
    for (const [tableName, tableData] of Object.entries(backupData.tables)) {
      if (tableData.count > 0) {
        console.log(`   ✅ ${tableName}: ${tableData.count} records`);
      } else {
        console.log(`   ⚪ ${tableName}: ${tableData.count} records${tableData.error ? ' (table might not exist)' : ''}`);
      }
    }

    console.log('\n💡 Your data is now safely backed up!');
    console.log(`   You can restore this backup anytime using the backup files in: ${backupDir}/`);
    
    return {
      success: true,
      backupDir: backupDir,
      totalRecords: totalRecords,
      tables: backupData.tables
    };

  } catch (error) {
    console.error('❌ Error creating backup:', error);
    console.error('Error details:', error.message);
    return {
      success: false,
      error: error.message
    };
  } finally {
    await pool.end();
    console.log('🔌 Database connection closed');
  }
}

// Run the backup
backupDatabase().then(result => {
  if (result.success) {
    console.log('\n✅ Backup process completed successfully');
    process.exit(0);
  } else {
    console.log('\n❌ Backup process failed');
    process.exit(1);
  }
});
