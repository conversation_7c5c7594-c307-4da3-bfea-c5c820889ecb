Business Profile Setup (Pre-requisite)
Before accessing campaign tools, a business must complete this form:

Business Name (required)

Primary Address (required)

Website (optional)

Description (optional, for directory or branding)

Phone Number (required)

Business Logo/Image Upload (recommended for personalization)

Primary Contact Email (optional override from account)

✅ Add Multiple Locations ("Sites"): each with its own name, address, and operating hours

Sites will be linkable to specific vouchers

All metrics and reporting should be filterable per site

🎯 Campaigns & Vouchers
2. "Create New Campaign" Workflow
Once a business profile is set, they can:

Click “Create New Campaign”

Set:

Campaign Name

Optional Goal / Notes (e.g. “Drive foot traffic to new location”)

Then they can begin creating vouchers.

3. Voucher Creation Flow
Each campaign can include multiple vouchers.

🛠 Manual or AI-assisted
Manual mode: business writes it all out

AI Help: auto-suggests title, description, reward ideas based on goals, business type, or tone (“Create something witty and casual for a local coffee shop”)

Voucher Fields:
Title (e.g. "Get $10 Off Your First Visit")

Description (what’s the offer, any rules)

Reward (e.g. $10 gift card when someone refers a new customer)

Classification (optional tags: “Food & Beverage”, “Local Service”, “Seasonal”)

Duration: Start/End date

Quantity Limit: e.g. “100 redemptions max”

Redeemable Sites: assign one or more business locations

Status:

Draft

Published

Archived/Paused (still exists but not visible to users)

✅ Preview Mode: clean, high-fidelity mobile/web preview of what the voucher will look like for users before publishing.

✅ Validation Logic:

If voucher is unpublished, expired, or maxed out → it disappears from the user's share screen.

If someone tries to use it anyway → show a friendly message:

"Oops! This offer is no longer available. Stay tuned — new deals come around often!"

📊 Vendor Dashboard
4. Dashboard Features
View campaigns and their voucher performance at a glance

Filter by:

Campaign

Site

Date Range

For each voucher:

of times shared
of redemptions
of new customers acquired
Earned vs. spent reward stats

Timeline chart (real-time activity)

✅ Highlight real-time activity (e.g., “Maria shared a voucher with 3 people” or “2 redemptions just now”)

👥 Customer Management
5. Import & CRM Integration
Upload CSV of customers

Option to connect with CRM like:

Mailchimp (email campaigns)

HubSpot, Klaviyo (future phases)

Imported customers are:

Matched against existing Referit users

Or, Invited automatically if not yet on the platform

Once matched:

Admin can assign specific vouchers to customers

Assigned vouchers show up in customer’s shareable list

✅ Consider giving customers early access or bonus reward multipliers for exclusive sharing to incentivize behavior.

🛠 Admin & Settings
6. Account Settings Area
Profile (name, email, password reset)

Business Info editing

Site Management (edit/remove/add sites)

API Keys (future, if integrating programmatically)

Billing & Plans (if monetized in future)

💡 Additional Suggestions
✨ Gamify the dashboard:

"You're 3 shares away from your next milestone!"

Visual leaderboard or referral tree

📱 Mobile Preview Toggle while designing vouchers

🧠 Voucher Templates based on industry (“salon”, “coffee shop”, etc.)

🧾 Export campaign data as PDF or CSV

🔁 Clone Campaign or Voucher for quick reuse