Enhancing the "Create New Business Profile" Flow — Locations & Hours Step

Hi Replit,

The Basic Information step in the Create New Business Profile flow is working well — no changes needed there.

Now I’d like to improve the “Locations and Hours” step to make the user experience more intuitive, especially for businesses with one or multiple locations.

🧭 What to Implement:
✅ Step Behavior:
When a user clicks “Next Step” from Basic Information, they should land on Locations and Hours.

This step should start with one location card already visible, labeled clearly as the Primary Business Location (pre-filled from the Basic Info tab).

➕ Additional Location Handling:
💡 If the business has only one location:
Add a clear prompt:

“Only have one location? You can skip this step and proceed to review.”

Provide a “Skip Additional Locations” button (primary CTA style).

Clicking it moves the user to the review/submit step.

🧩 If they want to add more locations:
Add a “+ Add New Location” button that expands a new location card.

Each new location entry should include the same field structure as Basic Info, except:

❌ No need to include: Business Logo and Description (these always default to the Primary)

🔄 Pre-fill options for consistency:
For each added location, include collapsible sections with the following toggle options (same layout and styling as the Primary fields):

Website Configuration
“Use same website as primary business”

Operation Hours
“Use same hours as primary business”

Business Category
“Use same business category as primary business”

Tags
“Use same business tags as primary business”

Each section should collapse or pre-fill based on the checkbox state. Please reuse the same component structure and visual hierarchy we already use in Basic Info.

✏️ Editing Experience:
When a profile has already been created and the user clicks “My Profile”, it should:

Route to the Locations & Hours step with all existing locations listed.

From there, they can:

Edit any existing location

Add a new location

Delete a location

Skip this step if they still only operate one location

🧠 Design & UX Expectations:
Keep the visual and interactive quality consistent with the Basic Information tab.

Layout should feel intuitive, polished, and clean — continue the seamless flow.

Avoid breaking or replacing existing patterns. The overall experience should feel like one cohesive form.