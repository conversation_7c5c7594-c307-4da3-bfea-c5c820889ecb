Hi Replit,

The current “Locations & Hours” tab works well overall, but I’d like to make some adjustments to improve clarity, usability, and consistency with the rest of the platform. Please leave the Basic Information step untouched — it’s working great.

📍 Default Experience
When a user lands on the Locations & Hours tab:

Show a card representing their Primary Business Location, using the details entered from the Basic Info tab.

Do not immediately show empty fields for new locations. Instead:

Show the Primary location card

Show the “+ Add Location” button

Show the “Skip Additional Locations” button

Show the Location Usage Meter

This gives users a clear sense of their default setup and next available options.

➕ Adding More Locations
When a user clicks “+ Add Location”:

A new location card should expand directly below the primary card

This card should include the same components used in the Basic Information step, including:

Full address input

Operating hours

Business category

Tags

Website configuration (no changes here)

These fields should use the same layout, styles, and interactions for a seamless, premium experience.

📫 Address Validation
The Full Address field:

Should allow typing in a street address

Should auto-complete with proper validation

Must return and store full structured address data:

Street

City

State

Zip code

Country

(Include optional "Address 2" / unit field)

📈 Location Usage and Upgrade
The Location Usage Meter should clearly indicate how many free locations are used (e.g., 1/2).

Add a subtle “Upgrade Plan” CTA — when clicked, it opens a beautiful modal offering premium tiers (e.g., up to 10 locations, 10 campaigns, 15 published vouchers, etc.)

✏️ After Profile Is Created
When a user returns to My Profile → Locations & Hours:

They should see all locations listed in the same style.

From here, users can:

Edit any location

Delete any non-primary location

Cannot delete the Primary location

Ensure the experience is polished and consistent with the original onboarding flow — clean, modern, and intuitive.

Let me know if anything is unclear or if there’s a better UX pattern you recommend.