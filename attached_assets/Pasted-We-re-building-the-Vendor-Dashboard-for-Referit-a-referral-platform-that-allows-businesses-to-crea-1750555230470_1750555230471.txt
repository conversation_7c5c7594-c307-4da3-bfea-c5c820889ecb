We’re building the Vendor Dashboard for Referit — a referral platform that allows businesses to create and share promotional vouchers with their customers.

When a business logs in, they should land on their dashboard. If they haven’t set up their business profile yet, they must first enter details like:

Business Name

Address

Website (optional)

Phone

Description (optional)

Businesses can also add multiple locations/sites (name + address), which they can later assign to specific campaigns and use for location-based reporting.

Once a business profile is complete, the dashboard should show a “Create New Campaign” button. This starts a flow for building promotional vouchers.

Each campaign can include one or more vouchers. Each voucher should include:

Title

Description

Reward (e.g., “Get $10 when a new customer redeems”)

Classification (optional — like “Food & Beverage”, “Seasonal”)

Duration (start and end dates)

Quantity Limit (how many redemptions allowed)

Assigned Sites (which locations honor this offer)

There should be an option to manually create a voucher, or use AI to help generate the content.

Each voucher must have a status:

Draft

Published

Archived

Businesses should be able to preview the voucher before publishing. If a voucher expires, hits its quantity limit, or is unpublished, it should no longer show up in customer dashboards. If someone tries to use it anyway, show a friendly message that the offer is no longer active.

The dashboard should allow businesses to:

View and filter campaigns

See real-time activity (referrals, redemptions, shares)

View voucher performance metrics (shares, redemptions, new users acquired)

They should also have a Customers section where they can:

Upload a CSV of existing customers

Connect to third-party CRMs like Mailchimp (optional for now)

View their customer list

Assign vouchers to specific users

If a customer is not yet on Referit, the system should invite them to join. Once they’re in, assigned vouchers should appear in their account for sharing and earning.

Add an Account Settings area for businesses to update profile, manage sites, reset password, etc.

This experience should be intuitive, polished, and visually engaging — think Stripe, Airbnb, and Notion-level UX. Prioritize simplicity and power.